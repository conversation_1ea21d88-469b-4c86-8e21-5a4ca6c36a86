#pragma once

#include <QHBoxLayout>
#include <QHeaderView>
#include <QMessageBox>
#include <QPushButton>
#include <QRegularExpression>
#include <QSyntaxHighlighter>
#include <QTableWidget>
#include <QTextEdit>
#include <QVBoxLayout>
#include <QWidget>

#include <fstream>
#include <sstream>
#include <string>


// Python 语法高亮器
class PythonSyntaxHighlighter : public QSyntaxHighlighter {
    Q_OBJECT
    public:
        PythonSyntaxHighlighter(QTextDocument* parent = nullptr) : QSyntaxHighlighter(parent) {
            // 关键字
            QStringList keywords = {
                "and", "as", "assert", "break", "class", "continue", "def", "del", "elif", "else",
                "except", "exec", "finally", "for", "from", "global", "if", "import", "in", "is",
                "lambda", "not", "or", "pass", "print", "raise", "return", "try", "while", "with", "yield"
            };

            QTextCharFormat keywordFormat;
            keywordFormat.setForeground(Qt::darkBlue);
            keywordFormat.setFontWeight(QFont::Bold);

            for (const QString& keyword : keywords) {
                QRegularExpression pattern(QString("\\b%1\\b").arg(keyword));
                highlightingRules.append({pattern, keywordFormat});
            }

            // 字符串
            QTextCharFormat stringFormat;
            stringFormat.setForeground(Qt::darkGreen);
            highlightingRules.append({QRegularExpression("\".*\""), stringFormat});
            highlightingRules.append({QRegularExpression("'.*'"), stringFormat});

            // 注释
            QTextCharFormat commentFormat;
            commentFormat.setForeground(Qt::gray);
            highlightingRules.append({QRegularExpression("#[^\n]*"), commentFormat});

            // 函数
            QTextCharFormat functionFormat;
            functionFormat.setForeground(Qt::blue);
            highlightingRules.append({QRegularExpression("\\b[A-Za-z0-9_]+(?=\\()"), functionFormat});

            // 数字
            QTextCharFormat numberFormat;
            numberFormat.setForeground(Qt::darkRed);
            highlightingRules.append({QRegularExpression("\\b[0-9]+\\b"), numberFormat});
        }

    protected:
        void highlightBlock(const QString& text) override {
            for (const auto& rule : highlightingRules) {
                QRegularExpressionMatchIterator matchIterator = rule.pattern.globalMatch(text);
                while (matchIterator.hasNext()) {
                    QRegularExpressionMatch match = matchIterator.next();
                    setFormat(match.capturedStart(), match.capturedLength(), rule.format);
                }
            }
        }

    private:
        struct HighlightingRule {
            QRegularExpression pattern;
            QTextCharFormat format;
        };
        QVector<HighlightingRule> highlightingRules;
    };

    // 脚本编辑器控件
    class ScriptEditorWidget : public QWidget {
        Q_OBJECT
    public:
        ScriptEditorWidget(QWidget* parent = nullptr) : QWidget(parent) {
            QVBoxLayout* layout = new QVBoxLayout(this);

            // 创建编辑器
            editor = new QTextEdit(this);
            editor->setFont(QFont("Courier New", 10));
            editor->setMinimumSize(800, 600);  // 设置最小尺寸
            
            // 设置Tab为4个空格
            QFontMetrics metrics(editor->font());
            editor->setTabStopDistance(4 * metrics.horizontalAdvance(' '));
            
            // 安装事件过滤器处理Tab键
            editor->installEventFilter(this);
            
            highlighter = new PythonSyntaxHighlighter(editor->document());

            // 创建工具栏
            QHBoxLayout* toolbarLayout = new QHBoxLayout();

            saveButton = new QPushButton(QObject::tr("保存"), this);
            connect(saveButton, &QPushButton::clicked, this, &ScriptEditorWidget::saveScript);

            reloadButton = new QPushButton(QObject::tr("重新加载"), this);
            connect(reloadButton, &QPushButton::clicked, this, &ScriptEditorWidget::reloadScript);

            toolbarLayout->addWidget(saveButton);
            toolbarLayout->addWidget(reloadButton);
            toolbarLayout->addStretch();

            layout->addLayout(toolbarLayout);
            layout->addWidget(editor);

            setLayout(layout);
        }

        void setScriptPath(const std::string& path) {
            scriptPath = path;
            loadScript();
        }

        std::string getScriptContent() const {
            return editor->toPlainText().toStdString();
        }

    signals:
        void scriptSaved();
        void scriptReloaded();

    private slots:
        void saveScript() {
            if (scriptPath.empty()) {
                return;
            }

            std::ofstream file(scriptPath, std::ios::out);
            if (file.is_open()) {
                file << getScriptContent();
                file.close();
                emit scriptSaved();
            } else {
                QMessageBox::warning(this, QObject::tr("保存失败"), QObject::tr("无法保存脚本文件。"));
            }
        }

        void reloadScript() {
            loadScript();
            emit scriptReloaded();
        }

        void loadScript() {
            if (scriptPath.empty()) {
                return;
            }

            std::ifstream file(scriptPath);
            if (file.is_open()) {
                std::stringstream buffer;
                buffer << file.rdbuf();
                editor->setPlainText(QString::fromStdString(buffer.str()));
                file.close();
            } else {
                QMessageBox::warning(this, QObject::tr("加载失败"), QObject::tr("无法加载脚本文件。"));
            }
        }

    private:
        QTextEdit* editor;
        QPushButton* saveButton;
        QPushButton* reloadButton;
        PythonSyntaxHighlighter* highlighter;
        std::string scriptPath;

        // 添加事件过滤器
        bool eventFilter(QObject* obj, QEvent* event) override {
            if (obj == editor && event->type() == QEvent::KeyPress) {
                QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);
                if (keyEvent->key() == Qt::Key_Tab) {
                    // 插入4个空格而不是Tab字符
                    editor->textCursor().insertText("    ");
                    return true;
                }
            }
            return QWidget::eventFilter(obj, event);
        }
    };

    // 参数编辑器控件
    class ParamsEditorWidget : public QWidget {
        Q_OBJECT
    public:
        ParamsEditorWidget(QWidget* parent = nullptr) : QWidget(parent) {
            QVBoxLayout* layout = new QVBoxLayout(this);

            // 创建表格
            paramsTable = new QTableWidget(0, 2, this);
            paramsTable->setHorizontalHeaderLabels({QObject::tr("参数名"), QObject::tr("参数值")});
            paramsTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

            // 创建工具栏
            QHBoxLayout* toolbarLayout = new QHBoxLayout();

            addButton = new QPushButton(QObject::tr("添加"), this);
            connect(addButton, &QPushButton::clicked, this, &ParamsEditorWidget::addParam);

            removeButton = new QPushButton(QObject::tr("删除"), this);
            connect(removeButton, &QPushButton::clicked, this, &ParamsEditorWidget::removeParam);

            toolbarLayout->addWidget(addButton);
            toolbarLayout->addWidget(removeButton);
            toolbarLayout->addStretch();

            layout->addLayout(toolbarLayout);
            layout->addWidget(paramsTable);

            setLayout(layout);
        }

        void setParams(const std::map<std::string, std::string>& params) {
            paramsTable->setRowCount(0);
            for (const auto& [key, value] : params) {
                addParamWithValue(QString::fromStdString(key), QString::fromStdString(value));
            }
        }

        std::map<std::string, std::string> getParams() const {
            std::map<std::string, std::string> params;
            for (int i = 0; i < paramsTable->rowCount(); i++) {
                QString key = paramsTable->item(i, 0)->text();
                QString value = paramsTable->item(i, 1)->text();
                params[key.toStdString()] = value.toStdString();
            }
            return params;
        }

    private slots:
        void addParam() {
            addParamWithValue("", "");
        }

        void addParamWithValue(const QString& key, const QString& value) {
            int row = paramsTable->rowCount();
            paramsTable->insertRow(row);

            QTableWidgetItem* keyItem = new QTableWidgetItem(key);
            QTableWidgetItem* valueItem = new QTableWidgetItem(value);

            paramsTable->setItem(row, 0, keyItem);
            paramsTable->setItem(row, 1, valueItem);
        }

        void removeParam() {
            int row = paramsTable->currentRow();
            if (row >= 0) {
                paramsTable->removeRow(row);
            }
        }

    private:
        QTableWidget* paramsTable;
        QPushButton* addButton;
        QPushButton* removeButton;
    };


