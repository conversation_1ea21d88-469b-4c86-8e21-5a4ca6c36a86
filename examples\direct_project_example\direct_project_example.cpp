#include <iostream>
#include <string>
#include <memory>
#include <opencv2/opencv.hpp>

#include "core/project_manager.h"
#include "core/video_processing_core.h"
#include "utils/plugin_renderer.h"
#include "utils/log_manager.h"

#ifdef _WIN32
#include <windows.h>
#endif

/**
 * 直接打开项目进行视频处理的示例
 *
 * 本示例演示如何:
 * 1. 直接打开一个已有的项目文件
 * 2. 将项目配置导入到VideoProcessingCore
 * 3. 获取并启用可用的插件
 * 4. 处理视频并显示结果
 */
int main(int argc, char* argv[]) {
    // 设置控制台输出编码，解决中文显示问题
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif
    utils::LogManager::get_instance().initialize("Project", 
                    utils::LogLevel::Debug,
                    utils::LogLevel::Debug);
    
    std::string projectPath = "C:/Users/<USER>/Downloads/test_video/projects/耳机鼠标.aivp";

    try {
        // 初始化VisionFlow环境（需要替换为有效的许可证ID和服务器地址）
        core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "192.168.0.169");

        std::cout << "正在打开项目: " << projectPath << std::endl;

        // 获取ProjectManager单例
        core::ProjectManager& projectManager = core::ProjectManager::get_instance();

        // 打开现有项目
        std::shared_ptr<core::Project> project = projectManager.open_project(projectPath);
        if (!project) {
            std::cerr << "无法打开项目: " << projectPath << std::endl;
            return 1;
        }

        std::cout << "项目名称: " << project->get_name() << std::endl;
        std::cout << "视频路径: " << project->get_video_path() << std::endl;

        // 创建VideoProcessingCore实例
        std::shared_ptr<core::VideoProcessingCore> processor = std::make_shared<core::VideoProcessingCore>();

        // 将项目配置导入到VideoProcessingCore
        if (!projectManager.import_to_video_processing_core(project, processor)) {
            std::cerr << "导入项目配置失败" << std::endl;
            return 1;
        }

        // 打开视频

        if (!processor->open_video_file("rtsp://admin:aqrose_369@192.168.1.108:554/cam/realmonitor?channel=1&subtype=0")) {
            std::cerr << "无法打开视频: " << project->get_video_path() << std::endl;
            return 1;
        }

        // 创建插件渲染器
        utils::PluginRenderer renderer;

        // 获取视频信息
        int frameCount = processor->get_total_frames();
        int fps = processor->get_fps();

        std::cout << "视频总帧数: " << frameCount << std::endl;
        std::cout << "视频帧率: " << fps << std::endl;

        // 创建窗口
        cv::namedWindow("处理后视频", cv::WINDOW_NORMAL);

        // 处理视频
        while (true) {
            try {
                // 获取当前帧索引
                int currentFrame = processor->get_current_frame();

                // 处理当前帧（启用插件）
                auto result = processor->process_frame(currentFrame);

                // 获取处理后的帧
                cv::Mat processedFrame = result.processed_frame;

                // 渲染结果
                renderer.render_frame_result(processedFrame, result);

                // 显示处理后的帧
                cv::imshow("处理后视频", processedFrame);

                // 显示当前帧信息
                currentFrame = processor->get_current_frame();
                std::cout << "/n处理进度: " << currentFrame << "/" << frameCount
                          << " (" << (currentFrame * 100 / frameCount) << "%)" << std::endl;

                // 等待按键，按ESC键退出
                int key = cv::waitKey(30);
                if (key == 27) { // ESC键
                    break;
                }

                // 读取下一帧
                processor->process_next_frame(false);

            } catch (const std::exception& e) {
                std::cerr << "/n处理异常: " << e.what() << std::endl;
                break;
            }
        }

        std::cout << std::endl;

        // 关闭视频和窗口
        processor->close_video();
        cv::destroyAllWindows();

        // 将处理器的最终配置导出到项目中
        // projectManager.export_from_video_processing_core(processor, project);

        // 保存项目的最终状态
        // projectManager.save_project(project);

        std::cout << "视频处理完成，项目已保存" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
