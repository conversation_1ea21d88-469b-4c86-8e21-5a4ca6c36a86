﻿#include "tracking/strack.h"

namespace tracking {

strack::strack() : track_id(-1), score(0.0f), state(0), lost_frames(0), track_count(0) {
    init_kalman_filter();
}

strack::strack(const strack& other) :
    track_id(other.track_id),
    tlwh(other.tlwh),
    detect_class(other.detect_class),
    score(other.score),
    state(other.state),
    lost_frames(other.lost_frames),
    track_count(other.track_count) {
    init_kalman_filter();
    if (other.kf_) {
        kf_->statePost = other.kf_->statePost.clone();
        kf_->statePre = other.kf_->statePre.clone();
        kf_->measurementMatrix = other.kf_->measurementMatrix.clone();
        kf_->processNoiseCov = other.kf_->processNoiseCov.clone();
        kf_->measurementNoiseCov = other.kf_->measurementNoiseCov.clone();
        kf_->errorCovPost = other.kf_->errorCovPost.clone();
        kf_->errorCovPre = other.kf_->errorCovPre.clone();
        kf_->gain = other.kf_->gain.clone();
    }
    if (!other.state_kf_.empty()) {
        state_kf_ = other.state_kf_.clone();
    }
}

strack& strack::operator=(const strack& other) {
    if (this != &other) {
        track_id = other.track_id;
        tlwh = other.tlwh;
        detect_class = other.detect_class;
        score = other.score;
        state = other.state;
        lost_frames = other.lost_frames;
        track_count = other.track_count;

        init_kalman_filter();
        if (other.kf_) {
            kf_->statePost = other.kf_->statePost.clone();
            kf_->statePre = other.kf_->statePre.clone();
            kf_->measurementMatrix = other.kf_->measurementMatrix.clone();
            kf_->processNoiseCov = other.kf_->processNoiseCov.clone();
            kf_->measurementNoiseCov = other.kf_->measurementNoiseCov.clone();
            kf_->errorCovPost = other.kf_->errorCovPost.clone();
            kf_->errorCovPre = other.kf_->errorCovPre.clone();
            kf_->gain = other.kf_->gain.clone();
        }
        if (!other.state_kf_.empty()) {
            state_kf_ = other.state_kf_.clone();
        }
    }
    return *this;
}

void strack::init_kalman_filter() {
    kf_ = std::make_unique<cv::KalmanFilter>(8, 4, 0);

    // 状态转移矩阵
    kf_->transitionMatrix = (cv::Mat_<float>(8, 8) <<
        1, 0, 0, 0, 1, 0, 0, 0,  // x = x + vx
        0, 1, 0, 0, 0, 1, 0, 0,  // y = y + vy
        0, 0, 1, 0, 0, 0, 1, 0,  // w = w + vw
        0, 0, 0, 1, 0, 0, 0, 1,  // h = h + vh
        0, 0, 0, 0, 1, 0, 0, 0,  // vx = vx
        0, 0, 0, 0, 0, 1, 0, 0,  // vy = vy
        0, 0, 0, 0, 0, 0, 1, 0,  // vw = vw
        0, 0, 0, 0, 0, 0, 0, 1); // vh = vh

    // 测量矩阵
    kf_->measurementMatrix = cv::Mat::zeros(4, 8, CV_32F);
    kf_->measurementMatrix.at<float>(0,0) = 1;  // x
    kf_->measurementMatrix.at<float>(1,1) = 1;  // y
    kf_->measurementMatrix.at<float>(2,2) = 1;  // w
    kf_->measurementMatrix.at<float>(3,3) = 1;  // h

    // 过程噪声协方差矩阵
    setIdentity(kf_->processNoiseCov, cv::Scalar::all(1e-2));

    // 测量噪声协方差矩阵
    setIdentity(kf_->measurementNoiseCov, cv::Scalar::all(1e-1));

    // 后验错误估计协方差矩阵
    setIdentity(kf_->errorCovPost, cv::Scalar::all(1));

    // 初始化状态向量
    state_kf_ = cv::Mat::zeros(8, 1, CV_32F);
}

void strack::update_kalman_filter(const cv::Rect_<float>& detection) {
    try {
        // 更新测量值
        cv::Mat measurement = (cv::Mat_<float>(4, 1) <<
            detection.x,
            detection.y,
            detection.width,
            detection.height);

        if (state_kf_.empty() || state_kf_.at<float>(0) == 0) {
            // 首次检测，初始化状态
            kf_->statePost.at<float>(0) = measurement.at<float>(0);
            kf_->statePost.at<float>(1) = measurement.at<float>(1);
            kf_->statePost.at<float>(2) = measurement.at<float>(2);
            kf_->statePost.at<float>(3) = measurement.at<float>(3);
            kf_->statePost.at<float>(4) = 0;  // vx
            kf_->statePost.at<float>(5) = 0;  // vy
            kf_->statePost.at<float>(6) = 0;  // vw
            kf_->statePost.at<float>(7) = 0;  // vh
            state_kf_ = kf_->statePost.clone();
        } else {
            // 预测
            state_kf_ = kf_->predict();
            // 更新
            state_kf_ = kf_->correct(measurement);
        }

        // 更新目标框
        tlwh.x = state_kf_.at<float>(0);
        tlwh.y = state_kf_.at<float>(1);
        tlwh.width = state_kf_.at<float>(2);
        tlwh.height = state_kf_.at<float>(3);
    } catch (const cv::Exception& e) {
        // 如果发生异常，使用原始检测结果
        tlwh = detection;
    }
}

cv::Rect_<float> strack::predict_next_position() {
    try {
        if (kf_) {
            cv::Mat prediction = kf_->predict();
            return cv::Rect_<float>(
                prediction.at<float>(0),
                prediction.at<float>(1),
                prediction.at<float>(2),
                prediction.at<float>(3)
            );
        }
    } catch (const cv::Exception& e) {
        // 如果预测失败，返回当前位置
    }
    return tlwh;
}

float strack::calculate_iou(const cv::Rect_<float>& rect1, const cv::Rect_<float>& rect2) {
    const float x1 = (std::max)(rect1.x, rect2.x);
    const float y1 = (std::max)(rect1.y, rect2.y);
    const float x2 = (std::min)(rect1.x + rect1.width, rect2.x + rect2.width);
    const float y2 = (std::min)(rect1.y + rect1.height, rect2.y + rect2.height);

    if (x1 >= x2 || y1 >= y2) return 0.0f;

    const float intersection = (x2 - x1) * (y2 - y1);
    const float area1 = rect1.width * rect1.height;
    const float area2 = rect2.width * rect2.height;

    return intersection / (area1 + area2 - intersection);
}

} // namespace tracking
