# Xinao异常检测Python绑定

本目录包含了Xinao项目的异常检测功能的Python绑定，允许从Python代码中直接调用C++实现的异常检测功能。

## 功能特性

- **AnomalyDetectionInterface**: 异常检测的主要接口类
- **简单易用**: 提供简洁的Python API
- **高性能**: 基于C++实现，性能优异
- **灵活配置**: 支持项目配置文件和参数自定义

## 编译和安装

### 前提条件

- CMake 3.19+
- Python 3.12
- pybind11
- AiVideoCore库
- OpenCV

### 编译步骤

1. 在项目根目录下创建构建目录：
```bash
mkdir build
cd build
```

2. 配置CMake：
```bash
cmake ..
```

3. 编译项目：
```bash
cmake --build . --config Release
```

4. 安装（可选）：
```bash
cmake --install .
```

编译完成后，Python模块 `xinao_anomaly_detection` 将生成在 `release` 目录中。

## 使用方法

### 基本使用

```python
import xinao_anomaly_detection

# 创建异常检测接口
detector = xinao_anomaly_detection.AnomalyDetectionInterface("path/to/project.json")

# 执行异常检测
result = detector.infer(
    video_path="path/to/video.mp4",
    save_path="path/to/results",
    keep_images=False
)

if result:
    print("检测成功")
else:
    print("检测失败")
```

### API参考

#### AnomalyDetectionInterface

**构造函数**
```python
AnomalyDetectionInterface(project_path: str)
```
- `project_path`: 项目配置文件路径

**方法**

##### infer()
```python
infer(video_path: str, save_path: str, keep_images: bool = False) -> bool
```
执行异常检测推理

**参数:**
- `video_path`: 输入视频文件路径
- `save_path`: 结果保存路径
- `keep_images`: 是否保留处理过程中的图像文件，默认为False

**返回值:**
- `bool`: 检测是否成功

### 示例代码

#### 1. 基本使用示例

参见 `example_usage.py` 文件，包含：
- 单个视频处理
- 批量视频处理
- 错误处理

#### 2. 测试脚本

运行 `test_python_bindings.py` 来测试绑定是否正常工作：

```bash
python test_python_bindings.py
```

## 配置说明

### 项目配置文件

异常检测需要一个项目配置文件（JSON格式），包含：
- 插件配置
- 模型参数
- 处理参数

示例配置文件结构：
```json
{
    "name": "xinao_anomaly_detection",
    "video_path": "",
    "plugins": [
        {
            "name": "anomaly_detection_plugin",
            "enabled": true,
            "parameters": {
                "threshold": "0.5",
                "model_path": "path/to/model"
            }
        }
    ]
}
```

## 故障排除

### 常见问题

1. **导入模块失败**
   - 确保模块已正确编译
   - 检查Python路径设置
   - 确认依赖库已安装

2. **项目文件不存在**
   - 检查项目配置文件路径
   - 确认文件格式正确

3. **视频处理失败**
   - 检查视频文件格式支持
   - 确认输出目录权限
   - 查看日志输出

### 调试建议

1. 使用测试脚本验证基本功能
2. 检查日志输出获取详细错误信息
3. 确认所有依赖库版本兼容

## 性能优化

- 使用SSD存储提高I/O性能
- 根据硬件配置调整处理参数
- 批量处理时考虑内存使用

## 许可证

本项目遵循AiVideoCore的许可证条款。

## 联系方式

如有问题或建议，请联系AiVideoCore开发团队。
