# Xinao异常检测Python绑定

本目录包含了Xinao项目的异常检测功能的Python绑定，允许从Python代码中直接调用C++实现的异常检测功能。

## 功能特性

- **AnomalyDetectionInterface**: 异常检测的主要接口类
- **简单易用**: 提供简洁的Python API
- **高性能**: 基于C++实现，性能优异
- **灵活配置**: 支持项目配置文件和参数自定义
- **避免循环依赖**: 使用C接口包装器解决Python循环依赖问题

## 架构说明

为了解决AiVideoCore内嵌Python解释器导致的循环依赖问题，本项目采用了**延迟加载(DELAYLOAD)**技术：

```
Python解释器
    ↓
xinao_anomaly_detection.pyd (Python模块)
    ↓ (延迟加载)
xinao_project.dll
    ↓ (延迟加载)
AiVideoCore.dll (包含Python解释器)
```

### 延迟加载的工作原理

1. **编译时**: Python模块知道需要调用的函数，但不立即加载DLL
2. **运行时**: 只有在实际调用函数时才加载相应的DLL
3. **避免循环**: 启动时不会同时加载两个包含Python解释器的模块

这种方法的优势：
- ✅ 避免启动时的循环依赖
- ✅ 保持完整的API功能
- ✅ 最小的性能开销
- ✅ 无需修改现有代码

## 编译和安装

### 前提条件

- CMake 3.19+
- Python 3.12
- pybind11
- AiVideoCore库
- OpenCV

### 编译步骤

1. 在项目根目录下创建构建目录：
```bash
mkdir build
cd build
```

2. 配置CMake：
```bash
cmake ..
```

3. 编译项目：
```bash
cmake --build . --config Release
```

4. 安装（可选）：
```bash
cmake --install .
```

编译完成后，Python模块 `xinao_anomaly_detection` 将生成在 `release` 目录中。

## 使用方法

### 基本使用

```python
import xinao_anomaly_detection

# 创建异常检测接口
detector = xinao_anomaly_detection.AnomalyDetectionInterface("path/to/project.json")

# 执行异常检测
result = detector.infer(
    video_path="path/to/video.mp4",
    save_path="path/to/results",
    keep_images=False
)

if result:
    print("检测成功")
else:
    print("检测失败")
```

### API参考

#### AnomalyDetectionInterface

**构造函数**
```python
AnomalyDetectionInterface(project_path: str)
```
- `project_path`: 项目配置文件路径

**方法**

##### infer()
```python
infer(video_path: str, save_path: str, keep_images: bool = False) -> bool
```
执行异常检测推理

**参数:**
- `video_path`: 输入视频文件路径
- `save_path`: 结果保存路径
- `keep_images`: 是否保留处理过程中的图像文件，默认为False

**返回值:**
- `bool`: 检测是否成功

### 示例代码

#### 1. 基本使用示例

参见 `example_usage.py` 文件，包含：
- 单个视频处理
- 批量视频处理
- 错误处理

#### 2. 测试脚本

运行 `test_python_bindings.py` 来测试绑定是否正常工作：

```bash
python test_python_bindings.py
```

## 配置说明

### 项目配置文件

异常检测需要一个项目配置文件（JSON格式），包含：
- 插件配置
- 模型参数
- 处理参数

示例配置文件结构：
```json
{
    "name": "xinao_anomaly_detection",
    "video_path": "",
    "plugins": [
        {
            "name": "anomaly_detection_plugin",
            "enabled": true,
            "parameters": {
                "threshold": "0.5",
                "model_path": "path/to/model"
            }
        }
    ]
}
```

## 故障排除

### 常见问题

#### 1. DLL加载失败 (ImportError: DLL load failed)

这是最常见的问题，通常由以下原因引起：

**解决步骤:**
1. 运行诊断脚本：
   ```bash
   python diagnose_dll_dependencies.py
   ```

2. 手动复制DLL文件：
   ```bash
   copy_dlls.bat
   ```

3. 检查必需的DLL文件是否存在：
   - `AiVideoCore.dll`
   - `xinao_project.dll`
   - `opencv_world470.dll`
   - `python312.dll`

4. 确保Visual C++ Redistributable已安装

#### 2. 导入模块失败

**可能原因:**
- 模块未正确编译
- Python路径设置错误
- 依赖库缺失

**解决方案:**
1. 重新编译：
   ```bash
   build_python_bindings.bat
   ```

2. 检查Python版本（必须是3.12）：
   ```bash
   python --version
   ```

3. 手动添加模块路径：
   ```python
   import sys
   sys.path.insert(0, 'path/to/release/directory')
   import xinao_anomaly_detection
   ```

#### 3. 项目文件不存在

- 检查项目配置文件路径
- 确认文件格式正确（JSON格式）
- 使用绝对路径

#### 4. 视频处理失败

- 检查视频文件格式支持
- 确认输出目录权限
- 查看日志输出

### 调试工具

1. **诊断脚本**: `diagnose_dll_dependencies.py`
   - 检查DLL依赖项
   - 测试模块导入
   - 提供详细的错误信息

2. **测试脚本**: `test_python_bindings.py`
   - 验证基本功能
   - 测试API调用

3. **DLL复制脚本**: `copy_dlls.bat`
   - 手动复制所有必需的DLL文件
   - 解决自动复制失败的问题

### 环境要求

- Windows 10/11
- Python 3.12 (精确版本)
- Visual Studio 2019/2022 (编译时)
- Visual C++ Redistributable (运行时)

## 性能优化

- 使用SSD存储提高I/O性能
- 根据硬件配置调整处理参数
- 批量处理时考虑内存使用

## 许可证

本项目遵循AiVideoCore的许可证条款。

## 联系方式

如有问题或建议，请联系AiVideoCore开发团队。
