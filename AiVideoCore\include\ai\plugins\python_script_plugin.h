#pragma once

#include <Python.h>
#include <filesystem>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <string>

#include "ai/plugins/task_plugin.h"
#include "aivideocore_export.h"

namespace py = pybind11;

namespace ai {
namespace plugins {

/**
 * @brief Python 脚本插件类，用于加载和执行 Python 脚本
 */
class AIVIDEOCORE_API PythonScriptPlugin : public TaskPlugin {
public:
    /**
     * @brief 构造函数
     * @param script_path Python 脚本路径
     */
    PythonScriptPlugin(const std::string& script_path);

    /**
     * @brief 析构函数
     */
    ~PythonScriptPlugin() override;

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    bool initialize() override;

    /**
     * @brief 处理任务
     * @param frame 输入/输出图像
     * @param tracks 跟踪结果
     * @param result 处理结果
     * @return 是否处理成功
     * @deprecated 已过时，请使用多帧处理接口
     */
    bool process(cv::Mat& frame,
                const std::vector<tracking::strack>& tracks,
                FrameResult& result) override;

    /**
     * @brief 批量处理多帧任务
     * @param frames 输入/输出图像列表，包含当前帧及前序帧
     * @param tracks_list 跟踪结果列表，每个元素对应一帧的跟踪结果
     * @param result 处理结果，包含当前帧的处理结果
     * @return 是否处理成功
     */
    bool process_batch(const std::vector<cv::Mat>& frames,
                     const std::vector<std::vector<tracking::strack>>& tracks_list,
                     ai::FrameResult& result) override;

    /**
     * @brief 重置插件状态
     */
    void reset() override;

    /**
     * @brief 获取插件需要处理的帧数
     * @return 需要处理的帧数，默认为5
     */
    int get_required_frames() const override;

    /**
     * @brief 获取插件类型
     * @return 插件类型
     */
    std::string get_type() const override;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    std::string get_description() const override;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    std::string get_version() const override;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    std::string get_author() const override;
    /**
     * @brief 获取插件在任务列表中的显示名称
     * @return 插件显示名称
     */
     std::string get_display_name() const override;

    /**
     * @brief 设置脚本路径
     * @param path 脚本路径
     * @return 是否设置成功
     */
    bool set_script_path(const std::string& path);

    /**
     * @brief 获取脚本路径
     * @return 脚本路径
     */
    std::string get_script_path() const;

    /**
     * @brief 重新加载脚本
     * @return 是否重新加载成功
     */
    bool reload_script();

    /**
     * @brief 获取脚本错误信息
     * @return 错误信息
     */
    std::string get_error_message() const;

    /**
     * @brief 设置脚本参数
     * @param params 参数字典
     * @deprecated 使用TaskPlugin::set_params代替
     */
    void set_script_params(const std::map<std::string, std::string>& params);

    /**
     * @brief 获取脚本参数
     * @return 参数字典
     * @deprecated 使用TaskPlugin::get_params代替
     */
    std::map<std::string, std::string> get_script_params() const;

    /**
     * @brief 获取脚本默认参数
     * @return 默认参数字典
     * @deprecated 使用TaskPlugin::get_default_params代替
     */
    std::map<std::string, std::string> get_default_params() const override;

    /**
     * @brief 获取参数描述信息
     * @return 参数描述字典，键为参数名，值为描述
     */
    std::map<std::string, std::string> get_param_descriptions() const;

    /**
     * @brief 获取脚本需要处理的帧数
     * @return 需要处理的帧数，默认为5
     */
    int get_required_frame_count() const;

    /**
     * @brief 从参数加载插件配置
     * 重写TaskPlugin::load_parameters方法
     */
    void load_parameters() override;

private:
    /**
     * @brief 加载 Python 脚本
     * @return 是否加载成功
     */
    bool load_script();

    /**
     * @brief 检查脚本是否有效
     * @return 是否有效
     */
    bool is_valid_script() const;

    /**
     * @brief 将 cv::Mat 转换为 numpy 数组
     * @param mat OpenCV 图像
     * @return numpy 数组
     */
    py::array_t<uint8_t> mat_to_numpy(const cv::Mat& mat) const;

    /**
     * @brief 将 numpy 数组转换为 cv::Mat
     * @param array numpy 数组
     * @return OpenCV 图像
     */
    cv::Mat numpy_to_mat(const py::array_t<uint8_t>& array) const;

    /**
     * @brief 将跟踪结果转换为 Python 字典列表
     * @param tracks 跟踪结果
     * @return Python 字典列表
     */
    py::list tracks_to_python(const std::vector<tracking::strack>& tracks) const;

    /**
     * @brief 将 Python 字典转换为 FrameResult
     * @param py_result Python 字典
     * @param result FrameResult
     */
    void python_to_result(const py::dict& py_result, FrameResult& result);

    /**
     * @brief 将 Python 字典转换为 Json::Value
     * @param dict Python 字典
     * @return Json::Value 对象
     */
    Json::Value python_dict_to_json(const py::dict& dict);

    /**
     * @brief 将 Python 列表转换为 Json::Value
     * @param list Python 列表
     * @return Json::Value 数组
     */
    Json::Value python_list_to_json(const py::list& list);

    /**
     * @brief 从脚本中提取默认参数和参数描述
     */
    void extract_default_params();

    /**
     * @brief 从脚本文档字符串中提取参数信息
     * @param doc_string 文档字符串
     */
    void extract_params_from_docstring(const std::string& doc_string);

    /**
     * @brief 去除字符串前后的空白字符
     * @param str 输入字符串
     * @return 去除空白后的字符串
     */
    std::string trim(const std::string& str);

    std::string script_path_;                      ///< Python 脚本路径
    std::string error_message_;                    ///< 错误信息
    std::map<std::string, std::string> default_params_;  ///< 脚本默认参数
    std::map<std::string, std::string> param_descriptions_;  ///< 参数描述
    std::filesystem::file_time_type last_file_time_; ///< 脚本文件最后修改时间

    py::module_ main_module_;                      ///< Python 主模块
    py::object script_module_;                     ///< Python 脚本模块
    py::object initialize_func_;                   ///< 初始化函数
    py::object process_func_;                      ///< 处理函数
    py::object reset_func_;                        ///< 重置函数
    py::object get_info_func_;                     ///< 获取信息函数
    py::object get_required_frames_func_;          ///< 获取需要帧数的函数

    bool is_initialized_;                          ///< 是否已初始化
};

} // namespace plugins
} // namespace ai
