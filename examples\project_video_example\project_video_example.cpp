#include <iostream>
#include <string>
#include <vector>
#include <memory>

// AiVideoCore headers
#include "core/project_manager.h"
#include "core/project.h"
#include "core/video_processing_core.h"
#include "ai/plugins/task_plugin.h"
#include "utils/plugin_renderer.h"

// OpenCV headers
#include <opencv2/opencv.hpp>
#include <opencv2/highgui.hpp>

int main(int argc, char* argv[]) {
    // 检查命令行参数
    // if (argc < 3) {
    //     std::cerr << "用法: " << argv[0] << " <视频文件路径> <项目保存路径>" << std::endl;
    //     return 1;
    // }
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif
    
    std::string videoPath = "rtsp://admin:aqrose_369@192.168.1.108:554/cam/realmonitor?channel=1&subtype=0";
    std::string projectPath = "C:/Users/<USER>/Downloads/test_video/projects/ufo_example.aivp";
    std::string model_path = "C:/Users/<USER>/Downloads/test_video/model/UFO测试.vfmodel";
    
    // 初始化VisionFlow环境（在实际应用中需要替换为有效的许可证ID和服务器地址）
    core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "192.168.0.169");

    // 获取ProjectManager单例
    core::ProjectManager& projectManager = core::ProjectManager::get_instance();
    
    // 创建新项目
    std::shared_ptr<core::Project> project = projectManager.create_project();
    
    // 设置项目名称
    project->set_name("视频处理示例项目");
    
    // 设置视频路径
    project->set_video_path(videoPath);
    project->set_model_path(model_path);
    
    // 设置项目其他属性
    project->set_score_threshold(0.5);  // 置信度阈值
    project->set_iou_threshold(70);     // IOU阈值
    project->set_frame_skip_interval(1); // 每帧都处理
    project->set_max_frame_history(30);  // 最大帧历史数量
    project->set_max_frame_history(100);
    project->set_frame_processor_enabled(false);
    project->set_enable_result_storage(false);
    project->set_input_node_id("输入");
    project->set_output_node_id("分割/pred");
    
    // 保存项目
    if (!projectManager.save_project(project, projectPath)) {
        std::cerr << "保存项目失败" << std::endl;
        return 1;
    }
    
    std::cout << "项目已保存到: " << projectPath << std::endl;
    
    // 创建VideoProcessingCore实例
    std::shared_ptr<core::VideoProcessingCore> processor = std::make_shared<core::VideoProcessingCore>();
    
    // 将项目配置导入到VideoProcessingCore
    if (!projectManager.import_to_video_processing_core(project, processor)) {
        std::cerr << "导入项目配置失败" << std::endl;
        return 1;
    }

    // 创建OpenCV窗口
    cv::namedWindow("原始视频", cv::WINDOW_NORMAL);
    cv::namedWindow("处理后视频", cv::WINDOW_NORMAL);
    
    // 处理视频帧
    std::cout << "开始处理视频..." << std::endl;
    
    // 创建一个Mat对象用于存储原始帧
    cv::Mat originalFrame;
    utils::PluginRenderer renderer;
    
    while (processor->is_video_opened()) {
        try {
            // 获取当前帧索引
            int currentFrameIndex = processor->get_current_frame();
            
            // 先获取原始帧（不进行AI处理）
            try {
                auto rawResult = processor->process_frame(currentFrameIndex, false);
                originalFrame = rawResult.processed_frame.clone();
            } catch (const std::exception& e) {
                std::cerr << "获取原始帧异常: " << e.what() << std::endl;
                break;
            }
            
            if (originalFrame.empty()) {
                std::cerr << "原始帧为空" << std::endl;
                break;
            }
            
            // 显示原始帧
            cv::imshow("原始视频", originalFrame);
            
            // 处理当前帧（启用插件）
            auto result = processor->process_frame(originalFrame);
            std::cout << "Processed frame with " << result.to_json() << " detections" << std::endl;
            
            // 获取处理后的帧
            cv::Mat processedFrame = result.processed_frame;
            
            // 如果处理后的帧为空，使用原始帧
            if (processedFrame.empty()) {
                processedFrame = originalFrame.clone();
            }

            renderer.render_frame_result(processedFrame, result);
            
            // 显示处理后的帧
            cv::imshow("处理后视频", processedFrame);
            
            // 等待按键，按ESC键退出
            int key = cv::waitKey(30);
            if (key == 27) { // ESC键
                break;
            }
            
            // 读取下一帧
            processor->process_next_frame(false);
            
        } catch (const std::exception& e) {
            std::cerr << "处理异常: " << e.what() << std::endl;
            break;
        }
    }
    
    // 关闭视频和窗口
    processor->close_video();
    cv::destroyAllWindows();
    
    // 将处理器的最终配置导出到项目中
    // projectManager.export_from_video_processing_core(processor, project);
    
    // 保存项目的最终状态
    // projectManager.save_project(project);
    
    std::cout << "视频处理完成，项目已保存" << std::endl;
    
    return 0;
}
