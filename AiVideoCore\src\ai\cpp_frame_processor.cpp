#include "ai/cpp_frame_processor.h"
#include "utils/settings_manager.h"
#include <iostream>
#include <algorithm> // 用于 std::transform

namespace ai {

CppFrameProcessor::CppFrameProcessor()
    : is_initialized_(false), enabled_(false) {
}

CppFrameProcessor::~CppFrameProcessor() {
}

bool CppFrameProcessor::initialize() {
    std::cout << "Initializing CppFrameProcessor..." << std::endl;

    // 记录初始化前的当前插件名称
    std::string originalPluginName = current_plugin_name_;
    std::cout << "Original plugin name before initialization: '" << originalPluginName << "'" << std::endl;

    // 检查插件列表是否为空
    if (plugins_.empty()) {
        std::cout << "Plugin list is empty, loading available plugins" << std::endl;
        // 加载可用的插件
        load_available_plugins();
    } else {
        std::cout << "Plugin list is not empty, keeping existing plugins" << std::endl;
        std::cout << "Existing plugins:" << std::endl;
        for (const auto& pair : plugins_) {
            std::cout << "  - '" << pair.first << "'" << std::endl;
        }
    }

    // 如果原始插件名称为空，才加载配置
    if (originalPluginName.empty()) {
        std::cout << "Original plugin name is empty, loading config" << std::endl;
        load_config();
    } else {
        std::cout << "Original plugin name is not empty, keeping it: '" << originalPluginName << "'" << std::endl;

        // 检查原始插件是否存在
        if (plugins_.find(originalPluginName) == plugins_.end()) {
            std::cout << "Warning: Original plugin '" << originalPluginName << "' does not exist in available plugins" << std::endl;

            // 尝试忽略大小写查找插件
            for (const auto& pair : plugins_) {
                std::string lowerName = originalPluginName;
                std::string lowerPluginName = pair.first;
                std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
                std::transform(lowerPluginName.begin(), lowerPluginName.end(), lowerPluginName.begin(), ::tolower);

                if (lowerName == lowerPluginName) {
                    std::cout << "Found plugin with case-insensitive match: '" << pair.first << "'" << std::endl;
                    current_plugin_name_ = pair.first;
                    break;
                }
            }
        } else {
            std::cout << "Original plugin '" << originalPluginName << "' exists in available plugins" << std::endl;
            current_plugin_name_ = originalPluginName;
        }
    }

    std::cout << "CppFrameProcessor initialized, current plugin: '" << current_plugin_name_ << "'" << std::endl;
    is_initialized_ = true;
    return true;
}

void CppFrameProcessor::load_available_plugins() {
    // 使用工厂加载所有可用的插件
    auto& factory = plugins::FrameProcessorPluginFactory::get_instance();
    auto plugin_names = factory.get_registered_plugin_names();

    // 保存当前插件列表的副本
    std::map<std::string, std::shared_ptr<plugins::FrameProcessorPlugin>> existing_plugins = plugins_;

    // 加载所有插件
    for (const auto& name : plugin_names) {
        // 如果插件已经存在，跳过
        if (plugins_.find(name) != plugins_.end()) {
            continue;
        }

        auto plugin = factory.create_plugin(name);
        if (plugin) {
            std::string actual_name = plugin->get_name();

            // 如果实际名称与注册名称不同，检查是否已经存在
            if (name != actual_name && plugins_.find(actual_name) != plugins_.end()) {
                continue;
            }

            register_plugin(plugin);
        }
    }
}

bool CppFrameProcessor::register_plugin(std::shared_ptr<plugins::FrameProcessorPlugin> plugin) {
    if (!plugin) {
        error_message_ = "Invalid plugin pointer";
        return false;
    }

    const std::string& name = plugin->get_name();

    if (plugins_.find(name) != plugins_.end()) {
        error_message_ = "Plugin with name '" + name + "' already exists";
        return false;
    }

    if (!plugin->initialize()) {
        error_message_ = "Failed to initialize plugin: " + plugin->get_error_message();
        return false;
    }

    plugins_[name] = plugin;

    // 如果是第一个插件，设置为当前插件
    if (current_plugin_name_.empty()) {
        current_plugin_name_ = name;
    }

    return true;
}

std::shared_ptr<plugins::FrameProcessorPlugin> CppFrameProcessor::get_plugin(const std::string& name) const {
    auto it = plugins_.find(name);
    if (it != plugins_.end()) {
        return it->second;
    }

    // 尝试忽略大小写查找插件
    for (const auto& pair : plugins_) {
        std::string lowerName = name;
        std::string lowerKey = pair.first;
        std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
        std::transform(lowerKey.begin(), lowerKey.end(), lowerKey.begin(), ::tolower);

        if (lowerName == lowerKey) {
            return pair.second;
        }
    }

    return nullptr;
}

std::vector<std::shared_ptr<plugins::FrameProcessorPlugin>> CppFrameProcessor::get_plugins() const {
    std::vector<std::shared_ptr<plugins::FrameProcessorPlugin>> result;
    for (const auto& pair : plugins_) {
        result.push_back(pair.second);
    }
    return result;
}

std::vector<std::string> CppFrameProcessor::get_plugin_names() const {
    std::vector<std::string> result;
    for (const auto& pair : plugins_) {
        result.push_back(pair.first);
    }
    return result;
}

bool CppFrameProcessor::set_current_plugin(const std::string& name) {
    if (plugins_.find(name) == plugins_.end()) {
        error_message_ = "Plugin with name '" + name + "' not found";
        return false;
    }

    // 获取新插件
    auto plugin = plugins_[name];

    // 无论帧处理器是否启用，都启用插件
    plugin->set_enabled(true);

    current_plugin_name_ = name;
    return true;
}

std::string CppFrameProcessor::get_current_plugin_name() const {
    return current_plugin_name_;
}

cv::Mat CppFrameProcessor::process_frames(const std::vector<cv::Mat>& frames) {
    if (!is_initialized_ || !enabled_ || current_plugin_name_.empty()) {
        // 如果未初始化或未启用，返回第一帧
        return frames.empty() ? cv::Mat() : frames[0].clone();
    }

    auto plugin = get_plugin(current_plugin_name_);
    if (!plugin || !plugin->is_enabled()) {
        // 如果插件不存在或未启用，返回第一帧
        return frames.empty() ? cv::Mat() : frames[0].clone();
    }

    try {
        return plugin->process_frames(frames);
    } catch (const std::exception& e) {
        error_message_ = "Failed to process frames: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
        // 出错时返回第一帧
        return frames.empty() ? cv::Mat() : frames[0].clone();
    }
}

void CppFrameProcessor::set_params(const std::map<std::string, std::string>& params) {
    if (!current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            plugin->set_params(params);
        }
    }
}

std::map<std::string, std::string> CppFrameProcessor::get_params() const {
    if (!current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            return plugin->get_params();
        }
    }
    return {};
}

void CppFrameProcessor::set_enabled(bool enabled) {
    enabled_ = enabled;

    // 如果启用帧处理器，同时启用当前插件
    if (enabled && !current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            plugin->set_enabled(true);
        }
    }

    // 如果禁用帧处理器，也禁用当前插件
    if (!enabled && !current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            plugin->set_enabled(false);
        }
    }
}

bool CppFrameProcessor::is_enabled() const {
    return enabled_;
}

int CppFrameProcessor::get_required_frames() const {
    if (!current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            return plugin->get_required_frames();
        }
    }
    return 1;
}

std::string CppFrameProcessor::get_error_message() const {
    return error_message_;
}

void CppFrameProcessor::save_config() const {
    std::cout << "Saving CppFrameProcessor config..." << std::endl;
    // 使用SettingsManager保存配置
    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("CppFrameProcessor");

    // 保存是否启用
    settings.setValue("enabled", enabled_);
    std::cout << "Saving enabled: " << (enabled_ ? "true" : "false") << std::endl;

    // 保存当前插件名称
    std::cout << "About to save current plugin name: '" << current_plugin_name_ << "'" << std::endl;
    settings.setValue("current_plugin", utils::SettingsValue(current_plugin_name_));
    std::cout << "Saved current plugin name: '" << current_plugin_name_ << "'" << std::endl;

    // 验证保存是否成功
    std::string savedValue = settings.value("current_plugin", utils::SettingsValue("")).toString();
    std::cout << "Verification - Read back saved plugin name: '" << savedValue << "'" << std::endl;
    if (savedValue != current_plugin_name_) {
        std::cerr << "WARNING: Saved plugin name does not match current plugin name!" << std::endl;
    }

    // 保存插件配置
    settings.beginGroup("Plugins");
    for (const auto& pair : plugins_) {
        const auto& name = pair.first;
        const auto& plugin = pair.second;

        settings.beginGroup(name);

        // 保存是否启用
        settings.setValue("enabled", utils::SettingsValue(plugin->is_enabled()));

        // 保存参数
        settings.beginGroup("Parameters");
        const auto& params = plugin->get_params();
        for (const auto& param_pair : params) {
            settings.setValue(param_pair.first, utils::SettingsValue(param_pair.second));
        }
        settings.endGroup(); // Parameters

        settings.endGroup(); // Plugin name
    }
    settings.endGroup(); // Plugins

    settings.endGroup(); // CppFrameProcessor

    // 强制同步设置到磁盘
    settings.sync();
    std::cout << "Settings synced to disk" << std::endl;
}

void CppFrameProcessor::load_config() {
    std::cout << "Loading CppFrameProcessor config..." << std::endl;
    // 使用SettingsManager加载配置
    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("CppFrameProcessor");

    // 加载是否启用
    enabled_ = settings.value("enabled", utils::SettingsValue(false)).toBool();
    std::cout << "Loaded enabled: " << (enabled_ ? "true" : "false") << std::endl;

    // 如果帧处理器启用，记录这个状态，稍后在加载完当前插件后再设置其启用状态
    bool processor_enabled = enabled_;

    // 加载当前插件名称
    std::cout << "Reading current_plugin from settings..." << std::endl;
    utils::SettingsValue pluginVariant = settings.value("current_plugin", utils::SettingsValue(""));
    std::cout << "Plugin variant type: " << pluginVariant.typeName() << ", isNull: " << (pluginVariant.isNull() ? "true" : "false") << std::endl;

    std::string savedPluginName = pluginVariant.toString();
    std::cout << "Loaded saved plugin name: '" << savedPluginName << "'" << std::endl;
    std::cout << "Current plugin name before update: '" << current_plugin_name_ << "'" << std::endl;

    // 检查设置中的所有键
    std::cout << "All keys in CppFrameProcessor settings group:" << std::endl;
    auto keys = settings.childKeys();
    for (const auto& key : keys) {
        std::cout << "  " << key << ": " << settings.value(key, utils::SettingsValue("")).toString() << std::endl;
    }

    // 从配置中加载当前插件名称
    if (!savedPluginName.empty()) {
        std::cout << "Loading plugin name from settings: '" << savedPluginName << "'" << std::endl;

        // 先直接设置当前插件名称，确保即使插件不存在也会保存设置
        current_plugin_name_ = savedPluginName;
        std::cout << "Current plugin name set to: '" << current_plugin_name_ << "'" << std::endl;

        // 检查插件是否存在
        if (plugins_.find(savedPluginName) == plugins_.end()) {
            std::cout << "Warning: Plugin '" << savedPluginName << "' does not exist in available plugins" << std::endl;

            // 如果有其他插件可用，记录警告信息
            if (!plugins_.empty()) {
                std::string firstPlugin = plugins_.begin()->first;
                std::cout << "First available plugin is: '" << firstPlugin << "'" << std::endl;
            } else {
                std::cout << "No plugins available" << std::endl;
            }
        } else {
            std::cout << "Plugin '" << savedPluginName << "' exists in available plugins" << std::endl;
        }
    } else if (current_plugin_name_.empty() && !plugins_.empty()) {
        // 如果没有保存的插件名称且当前插件名称为空，但有可用插件，则使用第一个插件
        std::string firstPlugin = plugins_.begin()->first;
        std::cout << "No saved plugin name and current plugin name is empty, using first available plugin: '" << firstPlugin << "'" << std::endl;
        current_plugin_name_ = firstPlugin;
    } else {
        std::cout << "Using current plugin name: '" << current_plugin_name_ << "'" << std::endl;
    }

    std::cout << "Current plugin name after update: '" << current_plugin_name_ << "'" << std::endl;

    // 加载插件配置
    settings.beginGroup("Plugins");
    for (const auto& pair : plugins_) {
        const auto& name = pair.first;
        const auto& plugin = pair.second;

        settings.beginGroup(name);

        // 加载是否启用
        plugin->set_enabled(settings.value("enabled", utils::SettingsValue(false)).toBool());

        // 加载参数
        settings.beginGroup("Parameters");
        auto keys = settings.childKeys();
        std::map<std::string, std::string> params;
        for (const auto& key : keys) {
            params[key] = settings.value(key, utils::SettingsValue("")).toString();
        }
        plugin->set_params(params);
        settings.endGroup(); // Parameters

        settings.endGroup(); // Plugin name
    }
    settings.endGroup(); // Plugins

    settings.endGroup(); // CppFrameProcessor

    // 确保如果帧处理器启用，当前插件也启用
    if (enabled_ && !current_plugin_name_.empty()) {
        auto plugin = get_plugin(current_plugin_name_);
        if (plugin) {
            plugin->set_enabled(true);
        }
    }
}

int CppFrameProcessor::reload_dll_plugins(const std::string& directory) {
    // 使用工厂加载DLL插件
    auto& factory = plugins::FrameProcessorPluginFactory::get_instance();

    // 如果目录为空，使用默认目录
    std::string plugin_dir = directory.empty() ? factory.get_plugin_directory() : directory;

    // 加载DLL插件
    int loaded_count = factory.load_dll_plugins(plugin_dir);

    // 重新加载所有可用的插件
    load_available_plugins();

    return loaded_count;
}

} // namespace ai
