#pragma once

#include <atomic>
#include <condition_variable>
#include <deque>
#include <fstream>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include <json/json.h>

#include "ai/frame_result.h"
#include "protocols/result_protocol.h"
#include "../aivideocore_export.h"

namespace core {

/**
 * @brief 结果存储服务类，用于管理处理结果的存储和通信服务
 */
class AIVIDEOCORE_API VideoResultStorageServer {
public:
    /**
     * @brief 存储模式枚举
     */
    enum class StorageMode {
        IMMEDIATE,  ///< 立即存储模式，每个结果立即写入文件
        TIMED       ///< 定时存储模式，按照指定时间间隔批量写入文件
    };

    /**
     * @brief 构造函数
     * @param storage_path 存储路径，默认为"results"
     * @param mode 存储模式，默认为立即存储
     * @param flush_interval_ms 刷新间隔（毫秒），仅在定时存储模式下有效，默认为5000ms
     */
    VideoResultStorageServer(const std::string& storage_path = "results",
                            StorageMode mode = StorageMode::IMMEDIATE,
                            int flush_interval_ms = 5000);

    /**
     * @brief 析构函数
     */
    ~VideoResultStorageServer();

    /**
     * @brief 启动服务
     * @param port 服务端口，默认为8888
     * @param protocol_type 协议类型，默认为TCP
     * @return 是否成功启动
     */
    bool start(int port = 8888, protocols::ProtocolType protocol_type = protocols::ProtocolType::TCP);

    /**
     * @brief 停止服务
     */
    void stop();

    /**
     * @brief 添加处理结果
     * @param result 帧处理结果
     * @return 是否成功添加
     */
    bool add_result(const ai::FrameResult& result);

    /**
     * @brief 设置存储路径
     * @param path 存储路径
     */
    void set_storage_path(const std::string& path);

    /**
     * @brief 获取存储路径
     * @return 存储路径
     */
    std::string get_storage_path() const;

    /**
     * @brief 设置存储模式
     * @param mode 存储模式
     */
    void set_storage_mode(StorageMode mode);

    /**
     * @brief 获取存储模式
     * @return 存储模式
     */
    StorageMode get_storage_mode() const;

    /**
     * @brief 设置刷新间隔
     * @param interval_ms 刷新间隔（毫秒）
     */
    void set_flush_interval(int interval_ms);

    /**
     * @brief 获取刷新间隔
     * @return 刷新间隔（毫秒）
     */
    int get_flush_interval() const;

    /**
     * @brief 获取当前结果文件路径
     * @return 当前结果文件路径
     */
    std::string get_current_file_path() const;

    /**
     * @brief 获取服务状态
     * @return 服务是否正在运行
     */
    bool is_running() const;

    /**
     * @brief 获取服务端口
     * @return 服务端口
     */
    int get_port() const;

    /**
     * @brief 获取连接客户端数量
     * @return 连接客户端数量
     */
    int get_client_count() const;

    /**
     * @brief 手动刷新缓冲区
     * @return 是否成功刷新
     */
    bool flush();

    /**
     * @brief 获取最近的结果数据
     * @return 最近的结果数据的JSON字符串
     */
    std::string get_last_result() const;

    /**
     * @brief 获取当前结果文件内容
     * @param max_results 最大返回结果数量，默认为100
     * @return 当前结果文件的内容，每行一个JSON字符串
     */
    std::vector<std::string> get_result_file_content(int max_results = 100) const;

    /**
     * @brief 获取当前使用的协议类型
     * @return 协议类型
     */
    protocols::ProtocolType get_protocol_type() const;

    /**
     * @brief 获取当前使用的协议名称
     * @return 协议名称
     */
    std::string get_protocol_name() const;

    /**
     * @brief 设置Modbus寄存器映射（仅在使用Modbus协议时有效）
     * @param register_map 寄存器映射表
     * @return 是否成功设置
     */
    bool set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map);

    /**
     * @brief 获取Modbus寄存器映射（仅在使用Modbus协议时有效）
     * @return 寄存器映射表
     */
    std::unordered_map<std::string, uint16_t> get_modbus_register_map() const;

private:
    /**
     * @brief 初始化存储目录
     * @return 是否成功初始化
     */
    bool initialize_storage_directory();

    /**
     * @brief 创建新的结果文件
     * @return 是否成功创建
     */
    bool create_new_result_file();

    /**
     * @brief 确保目录存在
     * @param dir_path 目录路径
     * @return 是否成功创建或已存在
     */
    bool ensure_directory_exists(const std::string& dir_path);

    /**
     * @brief 存储线程函数
     */
    void storage_thread_func();

    /**
     * @brief 处理新的客户端连接
     * @param client 客户端连接
     */
    void handle_new_connection(void* client);

    /**
     * @brief 发送结果到协议服务
     * @param json_str JSON字符串
     * @return 是否成功发送
     */
    bool send_result_to_protocol(const std::string& json_str);

private:
    std::string storage_path_;                  ///< 存储路径
    StorageMode storage_mode_;                  ///< 存储模式
    int flush_interval_ms_;                     ///< 刷新间隔（毫秒）
    std::atomic<bool> running_;                 ///< 服务是否正在运行
    std::atomic<int> port_;                     ///< 服务端口
    protocols::ProtocolType protocol_type_;     ///< 协议类型

    std::string current_date_;                  ///< 当前日期
    std::string current_file_path_;             ///< 当前文件路径
    std::ofstream current_file_;                ///< 当前文件流
    std::mutex file_mutex_;                     ///< 文件操作互斥锁

    std::deque<ai::FrameResult> result_queue_;  ///< 结果队列
    std::mutex queue_mutex_;                    ///< 队列互斥锁
    std::condition_variable queue_cv_;          ///< 队列条件变量

    std::thread storage_thread_;                ///< 存储线程

    // 协议服务相关
    std::shared_ptr<protocols::IResultProtocol> protocol_; ///< 协议服务实例

    std::string last_result_json_;              ///< 最近一次的结果的JSON字符串
    std::mutex last_result_mutex_;              ///< 最近结果互斥锁
};

} // namespace core
