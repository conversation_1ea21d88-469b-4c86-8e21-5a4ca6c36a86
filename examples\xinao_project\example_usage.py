#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Xinao异常检测Python接口使用示例
"""

import os
import sys

def example_anomaly_detection():
    """异常检测使用示例"""
    try:
        # 导入异常检测模块
        import xinao_anomaly_detection
        
        print("Xinao异常检测Python接口使用示例")
        print(f"模块版本: {xinao_anomaly_detection.__version__}")
        print("-" * 40)
        
        # 配置路径（请根据实际情况修改）
        project_path = "D:/yupei.wu/AiVideo/bin/release/projects/xinao_project.json"
        video_path = "D:/yupei.wu/AiVideo/test_videos/sample.mp4"
        save_path = "D:/yupei.wu/AiVideo/results"
        
        # 检查路径是否存在
        if not os.path.exists(project_path):
            print(f"警告: 项目文件不存在: {project_path}")
            print("请修改 project_path 为实际的项目文件路径")
            
        if not os.path.exists(video_path):
            print(f"警告: 视频文件不存在: {video_path}")
            print("请修改 video_path 为实际的视频文件路径")
            
        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)
        
        # 创建异常检测接口
        print("正在初始化异常检测接口...")
        detector = xinao_anomaly_detection.AnomalyDetectionInterface(project_path)
        print("异常检测接口初始化成功")
        
        # 执行异常检测
        print(f"正在处理视频: {video_path}")
        print(f"结果将保存到: {save_path}")
        
        # 执行推理
        result = detector.infer(video_path, save_path, keep_images=True)
        
        if result:
            print("✓ 异常检测完成，处理成功")
        else:
            print("✗ 异常检测失败")
            
        return result
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保xinao_anomaly_detection模块已正确编译和安装")
        return False
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        return False

def batch_process_example():
    """批量处理示例"""
    try:
        import xinao_anomaly_detection
        
        print("\n批量处理示例")
        print("-" * 40)
        
        # 配置
        project_path = "D:/yupei.wu/AiVideo/bin/release/projects/xinao_project.json"
        video_dir = "D:/yupei.wu/AiVideo/test_videos"
        output_dir = "D:/yupei.wu/AiVideo/batch_results"
        
        # 支持的视频格式
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
        
        # 创建检测器
        detector = xinao_anomaly_detection.AnomalyDetectionInterface(project_path)
        
        # 查找视频文件
        video_files = []
        if os.path.exists(video_dir):
            for file in os.listdir(video_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(video_dir, file))
        
        if not video_files:
            print(f"在目录 {video_dir} 中未找到视频文件")
            return False
            
        print(f"找到 {len(video_files)} 个视频文件")
        
        # 批量处理
        success_count = 0
        for i, video_file in enumerate(video_files, 1):
            print(f"\n处理第 {i}/{len(video_files)} 个视频: {os.path.basename(video_file)}")
            
            # 为每个视频创建单独的输出目录
            video_name = os.path.splitext(os.path.basename(video_file))[0]
            video_output_dir = os.path.join(output_dir, video_name)
            os.makedirs(video_output_dir, exist_ok=True)
            
            try:
                result = detector.infer(video_file, video_output_dir, keep_images=False)
                if result:
                    print(f"  ✓ 处理成功")
                    success_count += 1
                else:
                    print(f"  ✗ 处理失败")
            except Exception as e:
                print(f"  ✗ 处理出错: {e}")
        
        print(f"\n批量处理完成: {success_count}/{len(video_files)} 个视频处理成功")
        return success_count == len(video_files)
        
    except Exception as e:
        print(f"批量处理出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Xinao异常检测Python接口使用示例")
    print("=" * 50)
    
    # 单个视频处理示例
    success1 = example_anomaly_detection()
    
    # 批量处理示例
    success2 = batch_process_example()
    
    if success1 and success2:
        print("\n所有示例执行完成")
    else:
        print("\n部分示例执行失败，请检查配置和路径")

if __name__ == "__main__":
    main()
