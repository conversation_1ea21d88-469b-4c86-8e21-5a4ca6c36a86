# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置 CMake 的编码
if(MSVC)
    # 为 MSVC 编译器设置 UTF-8 编码
    add_compile_options(
        /utf-8                   # 强制使用 UTF-8
        /wd4819                  # 禁用 code page 警告
        /DWIN32_LEAN_AND_MEAN   # 减少 Windows 头文件包含
    )

    # 添加 Unicode 定义
    add_compile_definitions(
        _UNICODE
        UNICODE
        NOMINMAX                 # 避免 Windows 宏与 STL 冲突
    )
endif()

# 确保源文件使用 UTF-8 编码
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-finput-charset=UTF-8)
endif()

if(MSVC)
    add_compile_definitions(
        _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
        HAVE_SNPRINTF
        _CRT_SECURE_NO_WARNINGS
    )
endif()


add_subdirectory(project_video_example)
add_subdirectory(direct_project_example)
add_subdirectory(multi_stream_example)
add_subdirectory(xinao_project)