#include "ai/frame_result.h"

#include <json/json.h>

namespace ai {

Json::Value FrameResult::to_json() const {
    Json::Value root;
    
    // 基本信息
    root["frame_id"] = frame_id;
    root["timestamp"] = Json::Value::Int64(timestamp);  // 添加时间戳
    root["task_type"] = task_type;
    root["total_count"] = total_count;
    root["ext_info"] = ext_info;
    
    // 检测结果
    Json::Value detections(Json::arrayValue);
    for (const auto& track : detection_tracks) {
        Json::Value detection;
        detection["track_id"] = track.track_id;
        detection["class"] = track.detect_class;
        detection["score"] = track.score;
        detection["state"] = track.state;
        detection["duration"] = action_duration;
        
        Json::Value bbox;
        bbox["x"] = track.tlwh.x;
        bbox["y"] = track.tlwh.y;
        bbox["width"] = track.tlwh.width;
        bbox["height"] = track.tlwh.height;
        detection["bbox"] = bbox;
        detection["duration"] = action_duration;
        
        detections.append(detection);
    }
    root["detections"] = detections;
    
    // 类别计数
    Json::Value counts;
    for (const auto& [class_name, count] : class_counts) {
        counts[class_name] = count;
    }
    root["class_counts"] = counts;
    
    return root;
}

FrameResult FrameResult::from_json(const Json::Value& json) {
    FrameResult result;
    
    // 基本信息
    result.frame_id = json["frame_id"].asInt();
    result.task_type = json["task_type"].asString();
    result.total_count = json["total_count"].asInt();
    result.ext_info = json.get("ext_info", "").asString();  // 使用get方法提供默认值
    
    // 检测结果
    const Json::Value& detections = json["detections"];
    for (const auto& detection : detections) {
        tracking::strack track;
        track.track_id = detection["track_id"].asInt();
        track.detect_class = detection["class"].asString();
        track.score = detection["score"].asFloat();
        track.state = detection["state"].asInt();
        
        const Json::Value& bbox = detection["bbox"];
        track.tlwh.x = bbox["x"].asFloat();
        track.tlwh.y = bbox["y"].asFloat();
        track.tlwh.width = bbox["width"].asFloat();
        track.tlwh.height = bbox["height"].asFloat();
        
        result.detection_tracks.push_back(track);
        result.action_duration = bbox["duration"].asFloat();
    }
    
    // 计数结果
    const Json::Value& counts = json["class_counts"];
    for (auto it = counts.begin(); it != counts.end(); ++it) {
        result.class_counts[it.key().asString()] = it->asInt();
    }
   
    
    return result;
}

} // namespace ai





