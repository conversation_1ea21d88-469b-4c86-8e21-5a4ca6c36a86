﻿#pragma once

#include <string>
#include "../aivideocore_export.h"

namespace utils {

#ifdef _WIN32
/**
 * @brief 将UTF-8字符串转换为宽字符串(UNICODE)
 * @param str UTF-8编码的字符串
 * @return 对应的宽字符串
 */
AIVIDEOCORE_API std::wstring utf8ToWide(const std::string& str);

/**
 * @brief 将宽字符串转换为本地编码的ANSI字符串
 * @param wstr 宽字符串
 * @return 对应的ANSI字符串
 */
AIVIDEOCORE_API std::string wideToAnsi(const std::wstring& wstr);

/**
 * @brief 将UTF-8字符串转换为本地编码
 * @param str UTF-8编码的字符串
 * @return 对应的本地编码字符串
 */
AIVIDEOCORE_API std::string utf8ToAnsi(const std::string& str);
#endif

} // namespace utils
