@echo off
REM Xinao异常检测Python绑定构建脚本
REM 使用方法: build_python_bindings.bat

echo ========================================
echo Xinao异常检测Python绑定构建脚本
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查是否在正确的目录
if not exist "python_bindings.cpp" (
    echo 错误: 请在xinao_project目录下运行此脚本
    pause
    exit /b 1
)

REM 进入项目根目录
cd ..\..

REM 检查构建目录
if not exist "build" (
    echo 创建构建目录...
    mkdir build
)

cd build

echo 正在配置CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败
    pause
    exit /b 1
)

echo 正在编译项目...
cmake --build . --config Release --target xinao_anomaly_detection

if %ERRORLEVEL% neq 0 (
    echo 编译失败
    pause
    exit /b 1
)

echo 正在安装...
cmake --install .

if %ERRORLEVEL% neq 0 (
    echo 安装失败
    pause
    exit /b 1
)

echo ========================================
echo 构建完成！
echo ========================================
echo Python模块位置: bin\release\xinao_anomaly_detection.pyd
echo 
echo 测试方法:
echo 1. 将 bin\release 目录添加到Python路径
echo 2. 运行: python examples\xinao_project\test_python_bindings.py
echo ========================================

pause
