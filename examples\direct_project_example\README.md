# 直接打开项目进行视频处理示例

本示例演示了如何直接打开一个已有的项目文件，并使用 AiVideoCore 库进行视频处理。

## 功能特点

1. 直接打开已有的项目文件（.aivp）
2. 将项目配置导入到 VideoProcessingCore
3. 使用项目中已配置的所有参数进行视频处理
4. 显示处理结果

## 编译

在项目根目录下执行以下命令：

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

## 使用方法

```bash
./bin/direct_project_example <项目文件路径>
```

例如：

```bash
./bin/direct_project_example D:/projects/my_project.aivp
```

也可以使用提供的批处理文件：

```bash
cd examples/direct_project_example
run_example.bat D:/projects/my_project.aivp
```

## 注意事项

1. 需要替换示例中的许可证 ID 和服务器地址为有效值
2. 项目文件必须是有效的 .aivp 文件
3. 项目文件中必须包含有效的视频路径和模型路径
4. 处理过程中按 ESC 键可以退出

## 代码说明

示例代码展示了以下关键步骤：

1. 初始化 VisionFlow 环境
   ```cpp
   core::VideoProcessingCore::initialize_visionflow("your_license_id", "your_server_addr");
   ```

2. 打开现有项目
   ```cpp
   core::ProjectManager& projectManager = core::ProjectManager::get_instance();
   std::shared_ptr<core::Project> project = projectManager.open_project(projectPath);
   ```

3. 将项目配置导入到 VideoProcessingCore
   ```cpp
   projectManager.import_to_video_processing_core(project, processor);
   ```

4. 处理视频帧
   ```cpp
   auto result = processor->process_frame(originalFrame);
   ```

5. 渲染结果
   ```cpp
   renderer.render_frame_result(processedFrame, result);
   ```

6. 保存项目
   ```cpp
   projectManager.export_from_video_processing_core(processor, project);
   projectManager.save_project(project);
   ```
