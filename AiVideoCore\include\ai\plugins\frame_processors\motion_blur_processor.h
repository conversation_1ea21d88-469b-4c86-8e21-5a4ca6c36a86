#pragma once

#include "ai/plugins/frame_processor_plugin.h"
#include <opencv2/opencv.hpp>

namespace ai {
namespace plugins {
namespace frame_processors {

/**
 * @brief 运动模糊帧处理器插件，用于在多帧之间创建运动模糊效果
 */
class MotionBlurProcessor : public FrameProcessorPlugin {
public:
    /**
     * @brief 构造函数
     */
    MotionBlurProcessor();

    /**
     * @brief 析构函数
     */
    ~MotionBlurProcessor() override = default;

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    bool initialize() override;

    /**
     * @brief 处理多帧图像
     * @param frames 输入帧列表
     * @return 处理后的单帧图像
     */
    cv::Mat process_frames(const std::vector<cv::Mat>& frames) override;

    /**
     * @brief 获取需要的帧数
     * @return 需要的帧数
     */
    int get_required_frames() const override;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    std::string get_description() const override;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    std::string get_version() const override;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    std::string get_author() const override;

private:
    int alpha_; ///< 混合权重，0-100
    int frames_to_blend_; ///< 要混合的帧数
};

} // namespace frame_processors
} // namespace plugins
} // namespace ai
