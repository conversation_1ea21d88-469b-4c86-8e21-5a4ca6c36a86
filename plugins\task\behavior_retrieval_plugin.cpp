#include "behavior_retrieval_plugin.h"
#include <iostream>
#include <algorithm>

// 定义导出宏
#define BEHAVIOR_RETRIEVAL_PLUGIN_EXPORTS

// 定义日志宏
#define LOG_DEBUG(message) std::cout << __FILE__ << ":" << __LINE__ << " " << message << std::endl

namespace ai {
namespace plugins {

BehaviorRetrievalPlugin::BehaviorRetrievalPlugin()
    : TaskPlugin("BehaviorRetrievalPlugin"),
      required_frames_(1),
      frame_count_(0),
      render_interval_(1),
      last_render_frame_(0),
      debug_mode_(false),
      max_history_length_(100000),
      valid_action_num_(5),
      fps_(30.0) {


    // 初始化步骤模式
    // step_patterns_ = {"拿笔管", "拿笔芯", "安装", "拿笔帽", "安装"};
    step_patterns_ = {"步骤1", "步骤2", "步骤3"};


    // 设置默认参数（使用基类的params_）
    TaskPlugin::set_params(get_default_params());
}

bool BehaviorRetrievalPlugin::initialize() {
    LOG_DEBUG("BehaviorRetrievalPlugin initialized");

    // 重置状态
    reset();

    // 加载参数
    load_parameters();

    // 初始化步骤计数
    for (const auto& step : step_patterns_) {
        step_counts_[step] = 0;
    }

    LOG_DEBUG("行为检索插件初始化，步骤模式: ");
    for (const auto& step : step_patterns_) {
        LOG_DEBUG(step);
    }

    errMsg_ = "";
    finished_cycle_num_ = 0;
    current_step_index_ = 0;
    has_error_ = false;

    return true;
}

void BehaviorRetrievalPlugin::load_parameters() {
    // 获取基类的参数
    auto params = TaskPlugin::get_params();

    // 从参数中读取调试模式设置
    if (params.find("debug_mode") != params.end()) {
        std::string debug_value = params["debug_mode"];
        std::transform(debug_value.begin(), debug_value.end(), debug_value.begin(), ::tolower);
        debug_mode_ = (debug_value == "true" || debug_value == "1" || debug_value == "yes" || debug_value == "y" || debug_value == "on");

        if (debug_mode_) {
            LOG_DEBUG("已启用调试模式");
        }
    }

    // 从参数中读取渲染间隔
    if (params.find("render_interval") != params.end()) {
        try {
            render_interval_ = std::stoi(params["render_interval"]);
            if (debug_mode_) {
                LOG_DEBUG("设置渲染间隔为每 " << render_interval_ << " 帧");
            }
        } catch (...) {
            // 如果转换失败，使用默认值
            render_interval_ = 1;
            if (debug_mode_) {
                LOG_DEBUG("渲染间隔参数无效，使用默认值: 每 " << render_interval_ << " 帧");
            }
        }
    }

    // 从参数中读取步骤模式
    if (params.find("step_patterns") != params.end()) {
        try {
            std::string patterns_str = params["step_patterns"];
            std::vector<std::string> patterns;

            // 分割字符串
            size_t pos = 0;
            std::string token;
            while ((pos = patterns_str.find(',')) != std::string::npos) {
                token = patterns_str.substr(0, pos);
                patterns.push_back(token);
                patterns_str.erase(0, pos + 1);
            }
            patterns.push_back(patterns_str);

            // 更新步骤模式
            step_patterns_ = patterns;
        } catch (...) {
            // 如果解析失败，使用默认值
            if (debug_mode_) {
                LOG_DEBUG("步骤模式解析失败，使用默认值");
            }
        }
    }

    // 从参数中读取历史记录长度
    if (params.find("max_history_length") != params.end()) {
        try {
            max_history_length_ = std::stoi(params["max_history_length"]);
            if (debug_mode_) {
                LOG_DEBUG("设置历史记录最大长度为: " << max_history_length_);
            }
        } catch (...) {
            // 如果转换失败，使用默认值
            if (debug_mode_) {
                LOG_DEBUG("历史记录长度参数无效，使用默认值: " << max_history_length_);
            }
        }
    }

    // 从参数中读取有效动作判定数量
    if (params.find("valid_action_num") != params.end()) {
        try {
            valid_action_num_ = std::stoi(params["valid_action_num"]);
            if (valid_action_num_ < 1) {
                valid_action_num_ = 1;  // 确保至少为1
            }
            if (debug_mode_) {
                LOG_DEBUG("设置有效动作判定数量为: " << valid_action_num_);
            }
        } catch (...) {
            // 如果转换失败，使用默认值
            if (debug_mode_) {
                LOG_DEBUG("有效动作判定数量参数无效，使用默认值: " << valid_action_num_);
            }
        }
    }

    // 从参数中读取帧率
    if (params.find("fps") != params.end()) {
        try {
            fps_ = std::stod(params["fps"]);
            if (fps_ <= 0) {
                fps_ = 30.0;  // 确保帧率为正数
            }
            if (debug_mode_) {
                LOG_DEBUG("设置帧率为: " << fps_);
            }
        } catch (...) {
            // 如果转换失败，使用默认值
            if (debug_mode_) {
                LOG_DEBUG("帧率参数无效，使用默认值: " << fps_);
            }
        }
    }
}


/*
添加的功能：
new_action_info: 根据当前动作类型创建动作信息结构数据
format_time: 将帧数转换为时间字符串
update_valid_continue_actions_info_: 更新有效连续动作信息
check_valid_continue_actions_info_: 检查有效连续动作信息内是否有合法完整的动作序列
*/

BehaviorRetrievalPlugin::ActionInfo BehaviorRetrievalPlugin::new_action_info(const std::string& action_name) {
    ActionInfo action_info;
    action_info.action_name = action_name;
    action_info.start_frame = frame_count_;
    action_info.end_frame = frame_count_;
    action_info.formatted_start_time = format_time(action_info.start_frame);
    action_info.formatted_end_time = format_time(action_info.end_frame);
    action_info.duration = 0;

    return action_info;
}

std::string BehaviorRetrievalPlugin::format_time(int frame_number) {
    double time_in_seconds = frame_number / fps_;
    int minutes = static_cast<int>(time_in_seconds) / 60;
    int seconds = static_cast<int>(time_in_seconds) % 60;
    int milliseconds = static_cast<int>((time_in_seconds - static_cast<int>(time_in_seconds)) * 1000);
    
    char time_str[32];
    snprintf(time_str, sizeof(time_str), "%02d:%02d.%03d", minutes, seconds, milliseconds);
    return std::string(time_str);
}

bool BehaviorRetrievalPlugin::update_valid_continue_actions_info_(const std::string& current_frame_action) {
    // 如果当前帧为空，则不操作
    if (current_frame_action.empty()) {
        return false;
    }
    
    if (continue_actions_info_.empty()) {
        // 当前连续动作队列为空，则添加当前帧动作
        continue_actions_info_.push_back(new_action_info(current_frame_action));
    }
    else {
        if (continue_actions_info_.back().action_name != current_frame_action) {
            // 当前动作同队列里最后一个动作不同，出现新动作，添加到队列中
            continue_actions_info_.push_back(new_action_info(current_frame_action));
        }
        else {
            // 当前动作同队列里最后一个动作相同，判断间隔时间
            if (frame_count_ - continue_actions_info_.back().end_frame < valid_action_num_ * 2) {
                // 间隔时间短，为前一动作的延续，更新队尾动作的结束时间
                continue_actions_info_.back().end_frame = frame_count_;
                continue_actions_info_.back().formatted_end_time = format_time(continue_actions_info_.back().end_frame);
                continue_actions_info_.back().duration = (continue_actions_info_.back().end_frame - continue_actions_info_.back().start_frame) / fps_;
            }
            else {
                
                // 如果间隔时间很长，说明动作中间中断了一段时间，当作两个独立动作添加到队列中
                continue_actions_info_.push_back(new_action_info(current_frame_action));
            }
        }
    }

    // 过滤掉持续时间小于valid_action_num_的动作，并把队尾动作添加到valid_continue_actions_info_中
    valid_continue_actions_info_.clear();
    for (int i = 0; i < static_cast<int>(continue_actions_info_.size()) - 1; ++i) {
        const auto& act_info = continue_actions_info_[i];
        if ((act_info.end_frame - act_info.start_frame) >= static_cast<size_t>(valid_action_num_)) {
            valid_continue_actions_info_.push_back(act_info);
        }
    }
    valid_continue_actions_info_.push_back(continue_actions_info_.back());

    // std::vector<ActionInfo> temp_valid_continue_actions_info_;
    // temp_valid_continue_actions_info_.push_back(valid_continue_actions_info_[0]);
    // for (int i = 1; i < static_cast<int>(valid_continue_actions_info_.size()); ++i) {
    //     if (temp_valid_continue_actions_info_.back().action_name == valid_continue_actions_info_[i].action_name) {
    //         temp_valid_continue_actions_info_.back().end_frame = valid_continue_actions_info_[i].end_frame;
    //         temp_valid_continue_actions_info_.back().formatted_end_time = format_time(temp_valid_continue_actions_info_.back().end_frame);
    //         temp_valid_continue_actions_info_.back().duration = (temp_valid_continue_actions_info_.back().end_frame - temp_valid_continue_actions_info_.back().start_frame) / fps_;
    //     }
    //     else {
    //         temp_valid_continue_actions_info_.push_back(valid_continue_actions_info_[i]);
    //     }
    // }
    // valid_continue_actions_info_.clear();
    // for (const auto& act_info : temp_valid_continue_actions_info_) {
    //     valid_continue_actions_info_.push_back(act_info);
    // }

    if (debug_mode_) {
        for (const auto& act_info : valid_continue_actions_info_) {
            LOG_DEBUG("action name: " << act_info.action_name << " | duration: " << act_info.duration);
        }
    }
    
    return true;
}

void BehaviorRetrievalPlugin::rollback_valid_continue_actions_info_(const int sequence_start_index, const int err_index) {
    // 把valid_continue_actions_info_回退一步，并用回退后的valid_contnue_actions_info更新continue_actions_info_
    continue_actions_info_.clear();
    for (int i = sequence_start_index; i < err_index; ++i) {
        continue_actions_info_.push_back(valid_continue_actions_info_[i]);
    }

    valid_continue_actions_info_.clear();
    for (int i = 0; i < static_cast<int>(continue_actions_info_.size()); ++i) {
        valid_continue_actions_info_.push_back(continue_actions_info_[i]);
    }
}

bool BehaviorRetrievalPlugin::check_valid_continue_actions_info_() {
    

    // 如果valid_continue_actions_info_为空，返回false，暂不报错
    if (valid_continue_actions_info_.empty()) {
        errMsg_ = "等待开始执行动作序列";
        current_step_index_ = -1;
        return false;
    }

    std::vector<std::string> temp_step_patterns = step_patterns_;
    temp_step_patterns.push_back(step_patterns_[0]); // 对于 [A,B,C,D] 这类的行为序列，形成一个 [A,B,C,D,A] 的闭环序列

    // 在valid_continue_actions_info_中查找第一个动作，有第一个动作，说明动作序列开始
    // 这一步是以防视频流从序列动作的中间开启，导致一开始就是错的
    int sequence_start_index = -1;
    for (int i = 0; i < static_cast<int>(valid_continue_actions_info_.size()); ++i) {
        if (valid_continue_actions_info_[i].action_name == step_patterns_[0]) {
            sequence_start_index = i;
            break;
        }
    }
    
    // 没有找到的话，返回false。认为动作序列未开始，暂不报错。
    if (sequence_start_index == -1) {
        errMsg_ = "等待开始执行动作序列";
        continue_actions_info_.clear();
        valid_continue_actions_info_.clear();
        current_step_index_ = -1;
        return false;
    }

    current_step_index_ = 0;

    int step_index = 1;
    expected_next_step_ = step_patterns_[step_index];
    // 这里设置step_index为1，是因为sequence_start_index标记的动作为step_index=0，已经比对过了。因此从step_index=1开始继续比对

    for (int i = sequence_start_index + 1; i < static_cast<int>(valid_continue_actions_info_.size()); ++i) {
        // 允许有连续相同的动作，比如安装过程中间中断了几秒钟，继续安装，被认为是同一个安装动作序列
        if (valid_continue_actions_info_[i].action_name == valid_continue_actions_info_[i - 1].action_name) {
            continue;
        }
        else {
            if (valid_continue_actions_info_[i].end_frame - valid_continue_actions_info_[i].start_frame < valid_action_num_) {
                continue; // 这种情况只会出现在队尾最后一个动作，如果持续时间短，则认为还未完成，暂不报错
            }
            else {
                // 下一个不一样的动作，必须是合法序列的下一个动作，否则该序列错误
                if (valid_continue_actions_info_[i].action_name == temp_step_patterns[step_index]) {
                    // 是合法的动作
                    current_step_index_ = step_index;
                    step_index++;
                    if (step_index >= step_patterns_.size()) {
                        expected_next_step_ = step_patterns_[0];
                    }
                    else {
                        expected_next_step_ = step_patterns_[step_index];
                    }
                    has_error_ = false;
                }
                else {
                    errMsg_ = "【错误】操作流程错误，请回退到上一步 '" + temp_step_patterns[step_index-1] + "' ，执行 '" + temp_step_patterns[step_index] + "' 继续进行标准流程";
                    rollback_valid_continue_actions_info_(sequence_start_index, i);
                    has_error_ = true;
                    return false;
                }
            }
        }
    }

    if (has_error_) {
        return false;
    }
    else {
        if (step_index == static_cast<int>(temp_step_patterns.size())) {
            // 有一个闭环序列存在，标志操作已完成，队列里最后一步是标准序列的第一步
            finished_cycle_num_ += 1;
            errMsg_ = "操作流程已完成 | 周期 " + std::to_string(finished_cycle_num_);
            current_step_index_ = current_step_index_ - 1;
            return true;
        }
        else {
            if (valid_continue_actions_info_.back().action_name == step_patterns_.back()) {
                if (valid_continue_actions_info_.size() >= step_patterns_.size())
                {
                    // 队列里最后一步动作是标准序列的最后一步
                    if (frame_count_ - valid_continue_actions_info_.back().end_frame > valid_action_num_) {
                        // 最后一步动作已经完成一段时间，认为操作已完成
                        finished_cycle_num_ = finished_cycle_num_ + 1;
                        errMsg_ = "操作流程已完成 | 周期 " + std::to_string(finished_cycle_num_);
                        return true;
                    }
                    else {
                        errMsg_ = "操作流程未完成 | 周期 " + std::to_string(finished_cycle_num_);
                        return false;
                    }
                }
                else {
                    errMsg_ = "操作 '" + valid_continue_actions_info_.back().action_name + "' 仍在进行中";
                    return false;
                }
            }
            else {
                if (valid_continue_actions_info_.back().end_frame - valid_continue_actions_info_.back().start_frame >= valid_action_num_) {
                    errMsg_ = "操作 '" + valid_continue_actions_info_.back().action_name + "' 仍在进行中";
                }
                return false;
            }
        }
    }
}                                 

bool BehaviorRetrievalPlugin::process_batch(const std::vector<cv::Mat>& frames,
                                          const std::vector<std::vector<tracking::strack>>& tracks_list,
                                          ai::FrameResult& result) {
    if (!is_enabled() || frames.empty() || tracks_list.empty()) {
        return false;
    }

    // 获取当前帧和跟踪结果
    const cv::Mat& frame = frames[0];
    const auto& tracks = tracks_list[0];

    // 更新帧计数
    frame_count_++;

    // 判断是否需要渲染当前帧
    bool should_render = (frame_count_ - last_render_frame_) >= render_interval_;

    // 提取当前帧中所有目标的类别名
    std::vector<std::string> current_categories;
    for (const auto& track : tracks) {
        if (track.state == 1 || track.state == 2) { // 只处理确认的跟踪目标
            std::string category = track.detect_class;
            if (std::find(current_categories.begin(), current_categories.end(), category) == current_categories.end()) {
                current_categories.push_back(category);
            }
        }
    }

    // 将当前帧的类别名添加到历史记录
    if (!current_categories.empty()) {
        // 排序类别名
        std::sort(current_categories.begin(), current_categories.end());

        // 合并为一个字符串
        std::string category_str;
        for (size_t i = 0; i < current_categories.size(); ++i) {
            if (i > 0) {
                category_str += "+";
            }
            category_str += current_categories[i];
        }

        category_history_.push_back(category_str);

        // 限制历史记录长度
        if (category_history_.size() > static_cast<size_t>(max_history_length_)) {
            category_history_.erase(category_history_.begin(), category_history_.begin() + (category_history_.size() - max_history_length_));
        }
    }

    // 从category_history中分析有效动作
    std::string current_frame_action;
    if (!current_categories.empty()) {
        current_frame_action = current_categories[0];
    }

    if (debug_mode_) {
        LOG_DEBUG("Current action " << current_frame_action << " " << continue_actions_.size());
    }

    continue_actions_.push_back(current_frame_action);

    update_valid_continue_actions_info_(current_frame_action);
    bool is_action_sequence_legal = check_valid_continue_actions_info_();
    if (debug_mode_) {
        LOG_DEBUG("告警信息：" << errMsg_ << " | " << finished_cycle_num_);
    }
    

    // 创建渲染信息并添加到结果中
    if (should_render) {
        LOG_DEBUG("current_step_index_" << current_step_index_);
        current_status_ = errMsg_;
        if (current_step_index_ >= 0) {
            current_step_ = step_patterns_[current_step_index_];
        }
        else {
            current_step_ = "";
        }

        completed_steps_.clear();
        for (int i = 0; i < current_step_index_; ++i) {
            completed_steps_.push_back(step_patterns_[i]);
        }

        Json::Value render_info = create_render_info(frame, tracks, true);

        // 创建JSON对象存储扩展信息
        Json::Value ext_info_json;
        ext_info_json["render_info"] = render_info;

        // 将JSON转换为字符串并设置到结果中
        Json::FastWriter writer;
        result.ext_info = writer.write(ext_info_json);

        last_render_frame_ = frame_count_;
    }
    if (valid_continue_actions_info_.size() > 0) {
        result.action_duration = valid_continue_actions_info_.back().duration;
    }
    else {
        result.action_duration = 0;
    }
    

    if (is_action_sequence_legal) {
        valid_continue_actions_info_.clear();
        continue_actions_info_.clear();
    }

    return true;
}

Json::Value BehaviorRetrievalPlugin::create_render_info(const cv::Mat& frame,
                                                      const std::vector<tracking::strack>& tracks,
                                                      bool should_render) {
    Json::Value render_info;

    // 基本信息
    render_info["should_render"] = should_render;

    // 帧信息
    Json::Value frame_info;
    frame_info["frame_count"] = frame_count_;

    // 添加类别文本
    std::string category_text;
    for (const auto& track : tracks) {
        if (track.state == 1 || track.state == 2) { // 只处理确认的跟踪目标
            if (!category_text.empty()) {
                category_text += "+";
            }
            category_text += track.detect_class;
        }
    }
    frame_info["category_text"] = category_text.empty() ? "无" : category_text;
    render_info["frame_info"] = frame_info;

    // 状态信息
    Json::Value status;
    status["current_step"] = current_step_;

    auto current_step_index = current_step_index_;
    ActionInfo current_action_info;
    if (valid_continue_actions_info_.size() > 0) {
        current_action_info = valid_continue_actions_info_.back();
    }
    else {
        current_action_info = new_action_info("");
    }
    
    auto current_action = current_action_info.action_name;

    auto next_action = expected_next_step_;
    status["expected_step"] = next_action;

    // 添加步骤进度和所有步骤的状态
    int total_steps = static_cast<int>(step_patterns_.size());
    status["total_steps"] = total_steps;

    // 创建步骤状态数组
    Json::Value steps_status(Json::arrayValue);

    // 遍历所有步骤，设置其状态
    for (int i = 0; i < total_steps; i++) {
        Json::Value step_info;
        std::string step_name = step_patterns_[i];
        step_info["step_id"] = std::to_string(i + 1);
        step_info["step_name"] = step_name;

        // 设置步骤状态：0=未执行，1=当前执行，2=已完成
        if (i < current_step_index - 1) {
            step_info["status"] = 2; // 已完成，显示为绿色
        } else if (i == current_step_index - 1) {
            step_info["status"] = 1; // 当前执行，显示为黄色
        } else {
            step_info["status"] = 0; // 未执行，显示为默认颜色
        }

        steps_status.append(step_info);
    }

    status["steps_status"] = steps_status;

    // 添加步骤进度文本
    if (current_step_index >= 0) {
        status["step_progress"] = std::to_string(current_step_index+1) + "/" + std::to_string(total_steps);
    } else {
        status["step_progress"] = "0/" + std::to_string(total_steps);
    }
    LOG_DEBUG("step_progress" << status["step_progress"] << " | current step: " << std::to_string(current_step_index));

    status["status_text"] = current_status_;
    status["is_error"] = current_status_.find("错误") != std::string::npos;

    // 添加周期信息
    status["cycle_count"] = finished_cycle_num_;
    status["cycle_text"] = "已完成周期: " + std::to_string(finished_cycle_num_);

    // 添加有效动作和连续动作信息
    std::string valid_actions_text = "有效动作: ";
    for (const auto& step : valid_continue_actions_info_) {
        valid_actions_text += step.action_name + " ";
    }
    status["valid_actions_text"] = valid_actions_text;

    std::string continuous_actions_text = "连续动作: ";
    for (const auto& step : continue_actions_) {
        continuous_actions_text += step + " ";
    }
    status["continuous_actions_text"] = continuous_actions_text;

    // 添加有效动作判定数量
    status["valid_action_num"] = valid_action_num_;

    render_info["status"] = status;

    // 已完成步骤信息
    Json::Value completed_steps(Json::arrayValue);
    for (const auto& step : completed_steps_) {
        Json::Value step_info;
        step_info["step_name"] = step;
        step_info["step_id"] = step;
        completed_steps.append(step_info);
    }
    render_info["completed_steps"] = completed_steps;

    // 轨迹信息
    Json::Value tracks_info(Json::arrayValue);
    for (const auto& track : tracks) {
        if (track.state == 1 || track.state == 2) { // 只处理确认的跟踪目标
            Json::Value track_info;
            track_info["track_id"] = track.track_id;
            track_info["class"] = track.detect_class;

            Json::Value bbox(Json::arrayValue);
            bbox.append(static_cast<int>(track.tlwh.x));
            bbox.append(static_cast<int>(track.tlwh.y));
            bbox.append(static_cast<int>(track.tlwh.width));
            bbox.append(static_cast<int>(track.tlwh.height));
            track_info["bbox"] = bbox;

            tracks_info.append(track_info);
        }
    }
    render_info["tracks"] = tracks_info;

    // 添加自定义渲染元素，用于在屏幕上显示周期计数和下一个预期动作
    Json::Value custom_elements(Json::arrayValue);

    // 获取图像尺寸，用于计算位置
    int frame_height = frame.rows;
    int frame_width = frame.cols;

    // 1. 添加周期计数显示 - 放在底部
    Json::Value cycle_count_element;
    cycle_count_element["type"] = "text";
    if (current_status_.find("错误") == std::string::npos) {
        cycle_count_element["text"] = "正常周期: " + std::to_string(finished_cycle_num_);
    }
    else {
        cycle_count_element["text"] = "操作错误！";
    }

    cycle_count_element["position"] = Json::Value(Json::arrayValue);
    cycle_count_element["position"].append(frame_width / 4); // x坐标
    cycle_count_element["position"].append(frame_height - 120); // y坐标 - 距离底部120像素
    cycle_count_element["color"] = Json::Value(Json::arrayValue);
    cycle_count_element["color"].append(255); // B
    cycle_count_element["color"].append(255); // G
    cycle_count_element["color"].append(255); // R
    cycle_count_element["font_size"] = 0.8;
    cycle_count_element["thickness"] = 2;
    cycle_count_element["background"] = true;
    cycle_count_element["bg_color"] = Json::Value(Json::arrayValue);
    cycle_count_element["bg_color"].append(0); // B
    cycle_count_element["bg_color"].append(0); // G
    cycle_count_element["bg_color"].append(0); // R
    cycle_count_element["bg_alpha"] = 0.5;
    custom_elements.append(cycle_count_element);

    // 2. 添加下一个预期动作显示 - 放在底部
    if (!next_action.empty()) {
        Json::Value next_step_element;
        next_step_element["type"] = "text";
        next_step_element["text"] = "下一步: " + next_action;
        next_step_element["position"] = Json::Value(Json::arrayValue);
        next_step_element["position"].append(frame_width / 4); // x坐标
        next_step_element["position"].append(frame_height - 40); // y坐标 - 距离底部80像素
        next_step_element["color"] = Json::Value(Json::arrayValue);
        next_step_element["color"].append(50);  // B
        next_step_element["color"].append(255); // G
        next_step_element["color"].append(50);  // R - 绿色
        next_step_element["font_size"] = 0.8;
        next_step_element["thickness"] = 2;
        next_step_element["background"] = true;
        next_step_element["bg_color"] = Json::Value(Json::arrayValue);
        next_step_element["bg_color"].append(0); // B
        next_step_element["bg_color"].append(0); // G
        next_step_element["bg_color"].append(0); // R
        next_step_element["bg_alpha"] = 0.5;
        custom_elements.append(next_step_element);
    }

    // 把current_action_info.duration由double转换为string
    std::string duration_str = std::to_string(current_action_info.duration);

    // 3. 添加当前执行动作显示 - 放在底部
    if (!current_action.empty()) {
        Json::Value current_step_element;
        current_step_element["type"] = "text";
        current_step_element["text"] = "当前: " + current_action + "       持续: " + duration_str + " 秒 ";
        current_step_element["position"] = Json::Value(Json::arrayValue);
        current_step_element["position"].append(frame_width / 4); // x坐标
        current_step_element["position"].append(frame_height - 80); // y坐标 - 距离底部40像素
        current_step_element["color"] = Json::Value(Json::arrayValue);
        current_step_element["color"].append(50);  // B
        current_step_element["color"].append(215); // G
        current_step_element["color"].append(255); // R - 黄色
        current_step_element["font_size"] = 0.8;
        current_step_element["thickness"] = 2;
        current_step_element["background"] = true;
        current_step_element["bg_color"] = Json::Value(Json::arrayValue);
        current_step_element["bg_color"].append(0); // B
        current_step_element["bg_color"].append(0); // G
        current_step_element["bg_color"].append(0); // R
        current_step_element["bg_alpha"] = 0.5;
        custom_elements.append(current_step_element);
    }

    render_info["custom_elements"] = custom_elements;

    return render_info;
}

void BehaviorRetrievalPlugin::reset() {
    frame_count_ = 0;
    category_history_.clear();
    current_step_.clear();
    current_status_.clear();
    completed_steps_.clear();
    step_counts_.clear();
    current_executing_step_.clear();
    last_render_frame_ = 0;
    last_detected_step_.clear();

    // 重置有效动作和连续动作列表
    valid_actions_.clear();
    continue_actions_.clear();
    valid_actions_info_.clear();

    if (debug_mode_) {
        LOG_DEBUG("行为检索插件已重置");
    }

    // 初始化步骤计数
    for (const auto& step : step_patterns_) {
        step_counts_[step] = 0;
    }

    finished_cycle_num_ = 0;
    has_error_ = false;
    continue_actions_info_.clear();
    valid_continue_actions_info_.clear();
}

int BehaviorRetrievalPlugin::get_required_frames() const {
    return required_frames_;
}

std::string BehaviorRetrievalPlugin::get_type() const {
    return "behavior_retrieval";
}

std::string BehaviorRetrievalPlugin::get_description() const {
    return "提取目标类别名，按帧顺序排列，搜索指定步骤模式";
}

std::string BehaviorRetrievalPlugin::get_version() const {
    return "2.0.0";
}

std::string BehaviorRetrievalPlugin::get_author() const {
    return "阿丘科技";
}

std::map<std::string, std::string> BehaviorRetrievalPlugin::get_default_params() const {
    std::map<std::string, std::string> default_params;

    // 设置默认参数
    default_params["render_interval"] = "1";
    default_params["debug_mode"] = "true";
    default_params["max_history_length"] = "100000";
    default_params["valid_action_num"] = "5";
    default_params["fps"] = "30.0";

    // 设置默认步骤模式
    std::string patterns_str;
    for (const auto& pattern : step_patterns_) {
        if (!patterns_str.empty()) {
            patterns_str += ",";
        }
        patterns_str += pattern;
    }
    default_params["step_patterns"] = patterns_str;

    return default_params;
}

} // namespace plugins
} // namespace ai

// 导出函数实现
extern "C" {
    BEHAVIOR_RETRIEVAL_PLUGIN_API ai::plugins::TaskPlugin* CreateTaskPlugin() {
        return new ai::plugins::BehaviorRetrievalPlugin();
    }

    BEHAVIOR_RETRIEVAL_PLUGIN_API void DestroyTaskPlugin(ai::plugins::TaskPlugin* plugin) {
        delete plugin;
    }

    BEHAVIOR_RETRIEVAL_PLUGIN_API int GetTaskPluginApiVersion() {
        return 1; // 与TaskPluginDllLoader::TASK_PLUGIN_API_VERSION保持一致
    }
}
