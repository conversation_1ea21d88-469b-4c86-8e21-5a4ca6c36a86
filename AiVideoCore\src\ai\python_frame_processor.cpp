#include "ai/python_frame_processor.h"

#include <Python.h>
#include <filesystem>
#include <pybind11/embed.h>
#include <pybind11/stl.h>
#include "utils/settings_manager.h"

#include <iostream>

namespace py = pybind11;

// Remove the PyStdOutRedirect class and PYBIND11_EMBEDDED_MODULE definition
// Rest of the file remains the same

namespace ai {

PythonFrameProcessor::PythonFrameProcessor()
    : is_initialized_(false), enabled_(false) {
    initialize_func_ = py::none();
    process_func_ = py::none();
    get_required_frames_func_ = py::none();

    // 设置默认脚本目录
    script_directory_ = "scripts/frame_processors";

    // 确保脚本目录存在
    if (!std::filesystem::exists(script_directory_)) {
        std::filesystem::create_directories(script_directory_);
    }
}

PythonFrameProcessor::~PythonFrameProcessor() {
    // 释放 Python 对象
    initialize_func_ = py::none();
    process_func_ = py::none();
    get_required_frames_func_ = py::none();
    script_module_ = py::none();

    // 注意：我们不在析构函数中关闭 Python 解释器
    // 因为这可能导致崩溃
}

bool PythonFrameProcessor::initialize() {
    if (is_initialized_) {
        return true;
    }

    // 初始化 Python 解释器
    try {
        if (!Py_IsInitialized()) {
            // 使用 Python C API 初始化解释器
            Py_Initialize();

            if (!Py_IsInitialized()) {
                error_message_ = "Failed to initialize Python interpreter";
                return false;
            }
        }
    } catch (const std::exception& e) {
        error_message_ = "Failed to initialize Python interpreter: " + std::string(e.what());
        return false;
    }

    is_initialized_ = true;
    return true;
}

bool PythonFrameProcessor::load_script(const std::string& script_path) {
    script_path_ = script_path;

    if (script_path_.empty()) {
        error_message_ = "Script path is empty";
        return false;
    }

    if (!std::filesystem::exists(script_path_)) {
        error_message_ = "Script file does not exist: " + script_path_;
        return false;
    }

    try {
        // 确保 Python 已初始化
        if (!is_initialized_ && !initialize()) {
            return false;
        }

        // 获取脚本目录
        std::string script_dir = std::filesystem::path(script_path_).parent_path().string();

        // 导入 sys 模块并添加脚本目录到 sys.path
        py::gil_scoped_acquire gil;

        // 重定向标准输出
        py::module sys = py::module_::import("sys");
        py::module stdout_redirect = py::module_::import("stdout_redirect");
        py::object redirector = stdout_redirect.attr("StdOutRedirect")();
        sys.attr("stdout") = redirector;

        // 添加脚本目录到 sys.path
        py::list sys_path = sys.attr("path").cast<py::list>();
        bool path_exists = false;
        for (const auto& path : sys_path) {
            if (path.cast<std::string>() == script_dir) {
                path_exists = true;
                break;
            }
        }

        if (!path_exists) {
            sys_path.append(script_dir);
        }

        // 导入脚本模块
        std::string module_name = std::filesystem::path(script_path_).stem().string();
        std::cout << "Importing module: " << module_name << std::endl;
        try {
            script_module_ = py::module_::import(module_name.c_str());

            // 获取必需的函数
            if (!script_module_.attr("process_frames").is_none()) {
                process_func_ = script_module_.attr("process_frames");
            } else {
                error_message_ = "Script must define a 'process_frames' function";
                return false;
            }

            // 获取可选的函数
            if (py::hasattr(script_module_, "initialize")) {
                initialize_func_ = script_module_.attr("initialize");
            } else {
                initialize_func_ = py::none();
            }

            // 检查是否实现了get_required_frames函数
            if (py::hasattr(script_module_, "get_required_frames")) {
                get_required_frames_func_ = script_module_.attr("get_required_frames");
            } else {
                get_required_frames_func_ = py::none();
            }

            // 调用初始化函数
            if (!initialize_func_.is_none()) {
                py::dict py_params;
                std::cout << "Initializing script with " << params_.size() << " parameters" << std::endl;
                for (const auto& [key, value] : params_) {
                    std::cout << "  Parameter: " << key << " = " << value << std::endl;
                    py_params[py::str(key)] = py::str(value);
                }
                initialize_func_(py_params);
                std::cout << "Script initialization complete" << std::endl;
            }

            return true;
        } catch (const py::error_already_set& e) {
            error_message_ = "Import error: " + std::string(e.what());
            std::cout << "Error at import: " << error_message_ << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        error_message_ = "Failed to load script: " + std::string(e.what());
        std::cout << "Error at load_script: " << error_message_ << std::endl;
        return false;
    }
}

bool PythonFrameProcessor::reload_script() {
    // 释放旧的 Python 对象
    initialize_func_ = py::none();
    process_func_ = py::none();
    get_required_frames_func_ = py::none();
    script_module_ = py::none();

    // 重新加载脚本
    return load_script(script_path_);
}

cv::Mat PythonFrameProcessor::process_frames(const std::vector<cv::Mat>& frames) {
    if (!is_initialized_ || process_func_.is_none() || !enabled_) {
        // 如果未初始化或未启用，返回第一帧
        return frames.empty() ? cv::Mat() : frames[0].clone();
    }

    try {
        py::gil_scoped_acquire gil;  // 确保获取GIL

        // 创建帧列表
        py::list py_frames;
        for (const auto& frame : frames) {
            py_frames.append(mat_to_numpy(frame));
        }

        // 调用处理函数
        py::object result_obj = process_func_(py_frames);
        if (result_obj.is_none()) {
            throw std::runtime_error("Process function returned None");
        }

        // 转换返回的结果为cv::Mat
        py::array_t<uint8_t> py_result = result_obj.cast<py::array_t<uint8_t>>();
        cv::Mat result = numpy_to_mat(py_result);

        return result;
    } catch (const std::exception& e) {
        error_message_ = "Failed to process frames: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
        // 出错时返回第一帧
        return frames.empty() ? cv::Mat() : frames[0].clone();
    }
}

void PythonFrameProcessor::set_params(const std::map<std::string, std::string>& params) {
    params_ = params;
    if (is_initialized_ && !initialize_func_.is_none()) {
        try {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            py::dict py_params;  // 创建参数字典
            for (const auto& [key, value] : params_) {
                py_params[py::str(key)] = py::str(value);
            }
            initialize_func_(py_params);
        } catch (const std::exception& e) {
            error_message_ = "Failed to reinitialize with new params: " + std::string(e.what());
            std::cout << error_message_ << std::endl;
        }
    }
}

std::map<std::string, std::string> PythonFrameProcessor::get_params() const {
    return params_;
}

std::string PythonFrameProcessor::get_script_path() const {
    return script_path_;
}

bool PythonFrameProcessor::set_script_path(const std::string& path) {
    // 保存旧路径
    std::string old_path = script_path_;

    // 设置新路径
    script_path_ = path;

    // 先保存当前参数
    save_config();

    // 重新加载脚本
    bool result = load_script(path);

    // 加载新脚本的配置
    load_config();

    return result;
}

std::string PythonFrameProcessor::get_script_directory() const {
    return script_directory_;
}

void PythonFrameProcessor::set_script_directory(const std::string& directory) {
    script_directory_ = directory;

    // 确保脚本目录存在
    if (!std::filesystem::exists(script_directory_)) {
        std::filesystem::create_directories(script_directory_);
    }
}

std::string PythonFrameProcessor::get_error_message() const {
    return error_message_;
}

bool PythonFrameProcessor::is_enabled() const {
    return enabled_;
}

void PythonFrameProcessor::set_enabled(bool enabled) {
    enabled_ = enabled;
}

bool PythonFrameProcessor::is_initialized() const {
    return is_initialized_;
}

void PythonFrameProcessor::save_config() const {
    // 使用SettingsManager保存配置
    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("PythonFrameProcessor");

    // 使用脚本文件名作为配置组名
    std::string script_name = std::filesystem::path(script_path_).stem().string();
    settings.beginGroup(script_name);

    std::cout << "Saving frame processor config for script: " << script_name << std::endl;

    // 保存脚本路径
    settings.setValue("script_path", utils::SettingsValue(script_path_));

    // 保存是否启用
    settings.setValue("enabled", utils::SettingsValue(enabled_));

    // 保存参数
    settings.beginGroup("Parameters");
    for (const auto& [key, value] : params_) {
        settings.setValue(key, utils::SettingsValue(value));
        std::cout << "  Saving parameter: " << key << " = " << value << std::endl;
    }
    settings.endGroup();

    settings.endGroup();
    settings.endGroup();
}

void PythonFrameProcessor::load_config() {
    // 使用SettingsManager加载配置
    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("PythonFrameProcessor");

    // 使用脚本文件名作为配置组名
    std::string script_name = std::filesystem::path(script_path_).stem().string();
    settings.beginGroup(script_name);

    std::cout << "Loading frame processor config for script: " << script_name << std::endl;

    // 加载脚本路径
    std::string scriptPath = settings.value("script_path", utils::SettingsValue("")).toString();

    // 加载是否启用
    enabled_ = settings.value("enabled", utils::SettingsValue(false)).toBool();

    // 加载参数
    settings.beginGroup("Parameters");
    auto keys = settings.childKeys();

    // 打印调试信息
    std::cout << "Loading frame processor parameters, found " << keys.size() << " parameters" << std::endl;

    params_.clear();
    for (const auto& key : keys) {
        std::string value = settings.value(key, utils::SettingsValue("")).toString();
        params_[key] = value;
        std::cout << "  Loaded parameter: " << key << " = " << value << std::endl;
    }
    settings.endGroup();

    settings.endGroup();
    settings.endGroup();

    // 如果有保存的脚本路径，尝试加载脚本
    if (!scriptPath.empty() && std::filesystem::exists(scriptPath)) {
        std::cout << "Loading saved script: " << scriptPath << std::endl;
        if (load_script(scriptPath)) {
            std::cout << "Successfully loaded saved script" << std::endl;
        } else {
            std::cout << "Failed to load saved script: " << error_message_ << std::endl;
        }
    } else {
        std::cout << "No saved script path or script does not exist" << std::endl;
    }
}

int PythonFrameProcessor::get_required_frames() const {
    // 默认值为5
    int required_frames = 5;

    // 如果脚本实现了get_required_frames函数，则调用它
    if (is_initialized_ && !get_required_frames_func_.is_none()) {
        try {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            py::object result = get_required_frames_func_();
            if (!result.is_none()) {
                // 尝试将结果转换为整数
                required_frames = result.cast<int>();

                // 限制帧数在合理范围内
                if (required_frames < 1) {
                    required_frames = 1;
                } else if (required_frames > 100) {
                    required_frames = 100;
                }
            }
        } catch (const std::exception& e) {
            // 如果出错，使用默认值
            std::cout << "Error getting required frame count: " << e.what() << std::endl;
        }
    }

    return required_frames;
}

py::array_t<uint8_t> PythonFrameProcessor::mat_to_numpy(const cv::Mat& mat) const {
    if (mat.empty()) {
        throw std::runtime_error("Input matrix is empty");
    }

    py::gil_scoped_acquire gil;

    // 确保图像连续
    cv::Mat continuous = mat.isContinuous() ? mat : mat.clone();

    std::vector<std::ptrdiff_t> shape;
    std::vector<std::ptrdiff_t> strides;

    if (continuous.channels() == 1) {
        shape = {continuous.rows, continuous.cols};
        strides = {static_cast<std::ptrdiff_t>(continuous.step[0]), static_cast<std::ptrdiff_t>(continuous.step[1])};
    } else {
        shape = {continuous.rows, continuous.cols, continuous.channels()};
        strides = {static_cast<std::ptrdiff_t>(continuous.step[0]), static_cast<std::ptrdiff_t>(continuous.step[1]), static_cast<std::ptrdiff_t>(continuous.elemSize1())};
    }

    // 使用零复制模式，直接引用原始数据
    // 创建一个管理数据生命周期的capsule
    auto cleanup = [](void* data) {}; // 空函数，因为我们不需要释放内存

    return py::array_t<uint8_t>(
        shape,
        strides,
        continuous.data,
        py::capsule(continuous.data, cleanup)
    );
}

cv::Mat PythonFrameProcessor::numpy_to_mat(const py::array_t<uint8_t>& array) const {
    py::buffer_info info = array.request();
    cv::Mat temp;

    // 创建临时Mat对象
    if (info.ndim == 2) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC1, info.ptr);
    } else if (info.ndim == 3 && info.shape[2] == 3) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC3, info.ptr);
    } else if (info.ndim == 3 && info.shape[2] == 4) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC4, info.ptr);
    } else {
        throw std::runtime_error("Unsupported array shape");
    }

    // 始终进行深拷贝
    return temp.clone();
}

} // namespace ai




