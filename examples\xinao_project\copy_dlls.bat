@echo off
REM 手动复制DLL依赖项脚本
REM 用于解决Python模块DLL加载问题

echo ========================================
echo 手动复制DLL依赖项
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 设置路径
set "BUILD_DIR=..\..\build"
set "RELEASE_DIR=..\..\bin\release"
set "PYTHON_MODULE_DIR=%RELEASE_DIR%"

echo 源目录: %BUILD_DIR%
echo 目标目录: %PYTHON_MODULE_DIR%

REM 确保目标目录存在
if not exist "%PYTHON_MODULE_DIR%" (
    echo 创建目标目录: %PYTHON_MODULE_DIR%
    mkdir "%PYTHON_MODULE_DIR%"
)

echo.
echo 正在复制DLL文件...

REM 复制AiVideoCore.dll
if exist "%BUILD_DIR%\AiVideoCore\Release\AiVideoCore.dll" (
    copy "%BUILD_DIR%\AiVideoCore\Release\AiVideoCore.dll" "%PYTHON_MODULE_DIR%\"
    echo ✓ 复制 AiVideoCore.dll
) else (
    echo ✗ 未找到 AiVideoCore.dll
)

REM 复制xinao_project.dll
if exist "%BUILD_DIR%\examples\xinao_project\Release\xinao_project.dll" (
    copy "%BUILD_DIR%\examples\xinao_project\Release\xinao_project.dll" "%PYTHON_MODULE_DIR%\"
    echo ✓ 复制 xinao_project.dll
) else (
    echo ✗ 未找到 xinao_project.dll
)

REM 复制Python模块
if exist "%BUILD_DIR%\examples\xinao_project\Release\xinao_anomaly_detection.pyd" (
    copy "%BUILD_DIR%\examples\xinao_project\Release\xinao_anomaly_detection.pyd" "%PYTHON_MODULE_DIR%\"
    echo ✓ 复制 xinao_anomaly_detection.pyd
) else (
    echo ✗ 未找到 xinao_anomaly_detection.pyd
)

REM 查找并复制OpenCV DLL
for %%f in ("%BUILD_DIR%\*opencv*.dll") do (
    if exist "%%f" (
        copy "%%f" "%PYTHON_MODULE_DIR%\"
        echo ✓ 复制 %%~nxf
    )
)

REM 查找并复制其他依赖DLL
echo.
echo 查找其他依赖项...

REM 从_deps目录复制依赖项
if exist "%BUILD_DIR%\_deps" (
    for /r "%BUILD_DIR%\_deps" %%f in (*.dll) do (
        copy "%%f" "%PYTHON_MODULE_DIR%\" >nul 2>&1
        if not errorlevel 1 (
            echo ✓ 复制 %%~nxf
        )
    )
)

REM 复制常见的系统依赖项（如果存在）
set "COMMON_DLLS=msvcp140.dll vcruntime140.dll vcruntime140_1.dll"
for %%d in (%COMMON_DLLS%) do (
    where %%d >nul 2>&1
    if not errorlevel 1 (
        for /f "tokens=*" %%p in ('where %%d') do (
            copy "%%p" "%PYTHON_MODULE_DIR%\" >nul 2>&1
            if not errorlevel 1 (
                echo ✓ 复制 %%d
                goto :next_dll
            )
        )
    )
    :next_dll
)

echo.
echo ========================================
echo DLL复制完成
echo ========================================

echo 目标目录中的文件:
dir /b "%PYTHON_MODULE_DIR%\*.dll" 2>nul
dir /b "%PYTHON_MODULE_DIR%\*.pyd" 2>nul

echo.
echo 测试导入:
cd /d "%PYTHON_MODULE_DIR%"
python -c "import xinao_anomaly_detection; print('✓ 模块导入成功')" 2>nul
if errorlevel 1 (
    echo ✗ 模块导入失败，请运行诊断脚本获取更多信息
    echo python examples\xinao_project\diagnose_dll_dependencies.py
) else (
    echo ✓ 模块导入成功！
)

echo.
echo 如果仍有问题，请:
echo 1. 运行诊断脚本: python examples\xinao_project\diagnose_dll_dependencies.py
echo 2. 检查Python版本是否为3.12
echo 3. 确保Visual C++ Redistributable已安装

pause
