#pragma once

#include <map>
#include <memory>
#include <string>
#include <functional>
#include <vector>
#include "ai/plugins/frame_processor_plugin.h"
#include "ai/plugins/dll_plugin_loader.h"

namespace ai {
namespace plugins {

/**
 * @brief 帧处理器插件工厂，用于注册和创建帧处理器插件
 */
class AIVIDEOCORE_API FrameProcessorPluginFactory {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例
     */
    static FrameProcessorPluginFactory& get_instance();

    /**
     * @brief 注册插件创建函数
     * @param name 插件名称
     * @param creator 插件创建函数
     * @return 是否注册成功
     */
    bool register_plugin(const std::string& name, std::function<std::shared_ptr<FrameProcessorPlugin>()> creator);

    /**
     * @brief 创建插件
     * @param name 插件名称
     * @return 插件指针
     */
    std::shared_ptr<FrameProcessorPlugin> create_plugin(const std::string& name);

    /**
     * @brief 获取所有已注册的插件名称
     * @return 插件名称列表
     */
    std::vector<std::string> get_registered_plugin_names() const;

    /**
     * @brief 从目录加载DLL插件
     * @param directory 插件目录
     * @return 加载的插件数量
     */
    int load_dll_plugins(const std::string& directory);

    /**
     * @brief 获取插件目录
     * @return 插件目录
     */
    std::string get_plugin_directory() const;

    /**
     * @brief 设置插件目录
     * @param directory 插件目录
     */
    void set_plugin_directory(const std::string& directory);

private:
    /**
     * @brief 构造函数
     */
    FrameProcessorPluginFactory();

    /**
     * @brief 析构函数
     */
    ~FrameProcessorPluginFactory();

    /**
     * @brief 注册所有内置插件
     */
    void register_builtin_plugins();

    std::map<std::string, std::function<std::shared_ptr<FrameProcessorPlugin>()>> plugin_creators_; ///< 插件创建函数映射表
    DllPluginLoader dll_plugin_loader_; ///< DLL插件加载器
    std::string plugin_directory_; ///< 插件目录
};

} // namespace plugins
} // namespace ai
