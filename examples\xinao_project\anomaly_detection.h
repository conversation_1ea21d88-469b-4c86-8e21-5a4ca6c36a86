#pragma once

#ifndef ANOMALY_DETECTION_H
#define ANOMALY_DETECTION_H

#include <memory>
#include "core/project_manager.h"
#include "core/video_processing_core.h"
#include "utils/plugin_renderer.h"
#include "utils/log_manager.h"
#include "aivideocore_export.h"

class AIVIDEOCORE_API AnomalyDetection
{
private:
    std::string projectPath;  // 项目路径

    // 编码图像为视频
    void encode_images_to_video(const std::string& image_folder, const std::string& output_video, bool keep_images = false);
    
public:
    AnomalyDetection(std::string projectPath);

    // 处理异常检测逻辑
    bool anomaly_detection(std::string videoPath, std::string savePath, bool keep_images);
};

#endif // ANOMALY_DETECTION_H