#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Xinao异常检测Python绑定测试脚本
"""

import sys
import os

def test_anomaly_detection():
    """测试异常检测Python绑定"""
    try:
        # 导入模块
        import xinao_anomaly_detection
        
        print(f"模块版本: {xinao_anomaly_detection.__version__}")
        print(f"模块作者: {xinao_anomaly_detection.__author__}")
        
        # 测试项目路径（请根据实际情况修改）
        project_path = "path/to/your/project.json"
        
        # 创建异常检测接口实例
        detector = xinao_anomaly_detection.AnomalyDetectionInterface(project_path)
        print("异常检测接口创建成功")
        
        # 测试推理（请根据实际情况修改路径）
        video_path = "path/to/your/video.mp4"
        save_path = "path/to/save/results"
        
        # 注意：这里只是演示API调用，实际使用时需要提供有效的路径
        print("API绑定测试完成")
        print("使用方法:")
        print(f"  detector = xinao_anomaly_detection.AnomalyDetectionInterface('{project_path}')")
        print(f"  result = detector.infer('{video_path}', '{save_path}', keep_images=False)")
        
        return True
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保模块已正确编译和安装")
        return False
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Xinao异常检测Python绑定测试")
    print("=" * 50)
    
    success = test_anomaly_detection()
    
    if success:
        print("\n✓ 测试完成")
    else:
        print("\n✗ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
