#pragma once

#include "ai/plugins/frame_processor_plugin.h"
#include <opencv2/opencv.hpp>

namespace ai {
namespace plugins {
namespace frame_processors {

/**
 * @brief 星鹏帧差累加处理器插件，用于检测视频中的运动区域
 */
class XingpengFrameProcessor : public FrameProcessorPlugin {
public:
    /**
     * @brief 构造函数
     */
    XingpengFrameProcessor();

    /**
     * @brief 析构函数
     */
    ~XingpengFrameProcessor() override = default;

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    bool initialize() override;

    /**
     * @brief 处理多帧图像
     * @param frames 输入帧列表
     * @return 处理后的单帧图像
     */
    cv::Mat process_frames(const std::vector<cv::Mat>& frames) override;

    /**
     * @brief 获取需要的帧数
     * @return 需要的帧数
     */
    int get_required_frames() const override;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    std::string get_description() const override;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    std::string get_version() const override;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    std::string get_author() const override;

private:
    /**
     * @brief 预处理图像，转为灰度图并进行高斯模糊
     * @param image 输入图像
     * @return 预处理后的灰度图
     */
    cv::Mat preprocessing(const cv::Mat& image);

    /**
     * @brief 计算帧差
     * @param pre_frame 前一帧
     * @param cur_frame 当前帧
     * @param mode 模式，默认为"gray"
     * @return 帧差图
     */
    cv::Mat calculate_frame_diff(const cv::Mat& pre_frame, const cv::Mat& cur_frame, const std::string& mode = "gray");

    /**
     * @brief 处理帧差序列并生成彩色结果
     * @param frame_diff_clip 帧差序列
     * @return 彩色结果图
     */
    cv::Mat process_frame_clip_color(const std::vector<cv::Mat>& frame_diff_clip);

    /**
     * @brief 将灰度图着色
     * @param bw_img 灰度图
     * @return 彩色图
     */
    cv::Mat colorize(const cv::Mat& bw_img);

    int required_frames_; ///< 需要的帧数
    bool save_result_image_; ///< 是否保存结果图像
    std::string save_path_; ///< 保存路径
};

} // namespace frame_processors
} // namespace plugins
} // namespace ai
