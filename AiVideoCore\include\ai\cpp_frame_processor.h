#pragma once

#include <string>
#include <map>
#include <memory>
#include <vector>
#include <opencv2/opencv.hpp>
#include "ai/plugins/frame_processor_plugin.h"
#include "ai/plugins/frame_processor_plugin_factory.h"
#include "aivideocore_export.h"

namespace ai {

/**
 * @brief C++帧处理器，用于在模型推理前处理多帧图像
 */
class AIVIDEOCORE_API CppFrameProcessor {
public:
    /**
     * @brief 构造函数
     */
    CppFrameProcessor();

    /**
     * @brief 析构函数
     */
    ~CppFrameProcessor();

    /**
     * @brief 初始化处理器
     * @return 是否初始化成功
     */
    bool initialize();

    /**
     * @brief 加载所有可用的C++帧处理器插件
     */
    void load_available_plugins();

    /**
     * @brief 重新加载DLL插件
     * @param directory 插件目录，如果为空则使用默认目录
     * @return 加载的插件数量
     */
    int reload_dll_plugins(const std::string& directory = "");

    /**
     * @brief 注册插件
     * @param plugin 插件指针
     * @return 是否注册成功
     */
    bool register_plugin(std::shared_ptr<plugins::FrameProcessorPlugin> plugin);

    /**
     * @brief 获取插件
     * @param name 插件名称
     * @return 插件指针
     */
    std::shared_ptr<plugins::FrameProcessorPlugin> get_plugin(const std::string& name) const;

    /**
     * @brief 获取所有插件
     * @return 插件列表
     */
    std::vector<std::shared_ptr<plugins::FrameProcessorPlugin>> get_plugins() const;

    /**
     * @brief 获取所有插件名称
     * @return 插件名称列表
     */
    std::vector<std::string> get_plugin_names() const;

    /**
     * @brief 设置当前插件
     * @param name 插件名称
     * @return 是否设置成功
     */
    bool set_current_plugin(const std::string& name);

    /**
     * @brief 获取当前插件名称
     * @return 当前插件名称
     */
    std::string get_current_plugin_name() const;

    /**
     * @brief 处理多帧图像
     * @param frames 输入帧列表
     * @return 处理后的单帧图像
     */
    cv::Mat process_frames(const std::vector<cv::Mat>& frames);

    /**
     * @brief 设置插件参数
     * @param params 参数字典
     */
    void set_params(const std::map<std::string, std::string>& params);

    /**
     * @brief 获取插件参数
     * @return 参数字典
     */
    std::map<std::string, std::string> get_params() const;

    /**
     * @brief 设置是否启用
     * @param enabled 是否启用
     */
    void set_enabled(bool enabled);

    /**
     * @brief 获取是否启用
     * @return 是否启用
     */
    bool is_enabled() const;

    /**
     * @brief 获取需要的帧数
     * @return 需要的帧数
     */
    int get_required_frames() const;

    /**
     * @brief 获取错误信息
     * @return 错误信息
     */
    std::string get_error_message() const;

    /**
     * @brief 保存配置
     */
    void save_config() const;

    /**
     * @brief 加载配置
     */
    void load_config();

    /**
     * @brief 检查是否已初始化
     * @return 是否已初始化
     */
    bool is_initialized() const { return is_initialized_; }

    /**
     * @brief 直接设置当前插件名称，不检查插件是否存在
     * @param name 插件名称
     * @note 该方法仅在项目加载时使用，一般情况下应使用set_current_plugin
     */
    void set_current_plugin_name_directly(const std::string& name) { current_plugin_name_ = name; }

private:
    bool is_initialized_;                                                  ///< 是否已初始化
    bool enabled_;                                                         ///< 是否启用
    std::string error_message_;                                            ///< 错误信息
    std::map<std::string, std::shared_ptr<plugins::FrameProcessorPlugin>> plugins_; ///< 插件映射表
    std::string current_plugin_name_;                                      ///< 当前插件名称
};

} // namespace ai

