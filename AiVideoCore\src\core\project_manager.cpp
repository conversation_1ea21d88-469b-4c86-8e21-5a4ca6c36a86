#include "core/project_manager.h"

#include <algorithm>
#include <iostream>
#include "utils/settings_manager.h"

namespace core {

ProjectManager& ProjectManager::get_instance() {
    static ProjectManager instance;
    return instance;
}

ProjectManager::ProjectManager() {
    load_recent_projects();
}

ProjectManager::~ProjectManager() {
    save_recent_projects();
}

std::shared_ptr<Project> ProjectManager::create_project() {
    current_project_ = std::make_shared<Project>();
    return current_project_;
}

std::shared_ptr<Project> ProjectManager::open_project(const std::string& filePath) {
    auto project = std::make_shared<Project>();
    if (project->load_from_file(filePath)) {
        current_project_ = project;
        add_to_recent_projects(filePath);
        return project;
    }
    return nullptr;
}

bool ProjectManager::save_project(std::shared_ptr<Project> project, const std::string& filePath) {
    if (!project) {
        return false;
    }

    std::string path = filePath;
    if (path.empty()) {
        path = project->get_file_path();
        if (path.empty()) {
            return false;
        }
    }

    if (project->save_to_file(path)) {
        add_to_recent_projects(path);
        return true;
    }

    return false;
}

std::shared_ptr<Project> ProjectManager::get_current_project() const {
    return current_project_;
}

void ProjectManager::set_current_project(std::shared_ptr<Project> project) {
    current_project_ = project;
}

bool ProjectManager::export_from_video_processing_core(std::shared_ptr<VideoProcessingCore> videoProcessingCore, std::shared_ptr<Project> project) {
    if (!videoProcessingCore || !project) {
        return false;
    }

    try {
        // 获取视频路径
        if (videoProcessingCore->get_video_provider()) {
            project->set_video_path(videoProcessingCore->get_video_provider()->get_video_path());
        }

        // 获取模型路径
        if (videoProcessingCore->get_model_manager() && videoProcessingCore->get_model_manager()->get_model()) {
            // 从VideoProcessingCore获取模型路径
            project->set_model_path(videoProcessingCore->get_model_path());
        }

        // 获取输入输出节点ID
        project->set_input_node_id(videoProcessingCore->get_input_node_id());
        project->set_output_node_id(videoProcessingCore->get_output_node_id());

        // 获取帧跳过间隔
        project->set_frame_skip_interval(videoProcessingCore->get_frame_skip_interval());

        // 获取最大帧历史数量
        if (videoProcessingCore->get_ai_processor()) {
            project->set_max_frame_history(videoProcessingCore->get_ai_processor()->get_max_frame_history());
        }

        // 获取结果存储服务配置
        auto resultServer = videoProcessingCore->get_result_storage_server();
        if (resultServer) {
            // 设置结果存储服务是否启用
            project->set_enable_result_storage(true);

            // 设置结果存储服务端口
            project->set_result_storage_tcp_port(resultServer->get_port());

            // 设置结果存储服务模式
            project->set_result_storage_mode(static_cast<int>(resultServer->get_storage_mode()));

            // 设置结果存储服务刷新间隔
            project->set_result_storage_flush_interval(resultServer->get_flush_interval());

            // 设置结果存储服务协议类型
            project->set_result_storage_protocol_type(resultServer->get_protocol_type());

            // 如果是Modbus协议，获取寄存器映射
            if (resultServer->get_protocol_type() == protocols::ProtocolType::MODBUS) {
                project->set_modbus_register_map(resultServer->get_modbus_register_map());
            }
        } else {
            // 如果没有结果存储服务，设置为禁用
            project->set_enable_result_storage(false);
        }

        // 获取启用的插件列表
        std::vector<std::string> enabledPlugins;
        auto plugins = videoProcessingCore->get_plugin_names();
        std::cout << "Exporting plugin states from VideoProcessingCore to Project" << std::endl;
        for (const auto& pluginName : plugins) {
            if (videoProcessingCore->is_plugin_enabled(pluginName)) {
                std::cout << "  Plugin enabled: " << pluginName << std::endl;
                enabledPlugins.push_back(pluginName);
            } else {
                std::cout << "  Plugin disabled: " << pluginName << std::endl;
            }
        }
        project->set_enabled_plugins(enabledPlugins);
        std::cout << "Total enabled plugins: " << enabledPlugins.size() << std::endl;

        // 获取插件参数
        for (const auto& pluginName : plugins) {
            auto params = videoProcessingCore->get_plugin_params(pluginName);
            if (!params.empty()) {
                std::cout << "Exporting parameters for plugin: " << pluginName << std::endl;
                for (const auto& [key, value] : params) {
                    std::cout << "  " << key << ": " << value << std::endl;
                }
                project->set_plugin_params(pluginName, params);
            }
        }

        // 获取帧处理器配置
        if (videoProcessingCore->get_ai_processor()) {
            auto aiProcessor = videoProcessingCore->get_ai_processor();

            // 获取是否使用C++帧处理器
            bool useCpp = aiProcessor->is_using_cpp_frame_processor();
            project->set_use_cpp_frame_processor(useCpp);
            std::cout << "  Using C++ frame processor: " << (useCpp ? "true" : "false") << std::endl;

            if (useCpp) {
                // 如果使用C++帧处理器
                if (aiProcessor->get_cpp_frame_processor()) {
                    auto frameProcessor = aiProcessor->get_cpp_frame_processor();
                    std::cout << "Exporting C++ frame processor configuration" << std::endl;

                    // 获取插件名称
                    std::string pluginName = frameProcessor->get_current_plugin_name();
                    project->set_cpp_frame_processor_plugin_name(pluginName);
                    std::cout << "  Plugin name: " << pluginName << std::endl;

                    // 获取是否启用
                    bool enabled = frameProcessor->is_enabled();
                    project->set_frame_processor_enabled(enabled);
                    std::cout << "  Enabled: " << (enabled ? "true" : "false") << std::endl;

                    // 获取参数
                    auto params = frameProcessor->get_params();
                    project->set_frame_processor_params(params);
                    std::cout << "  Parameters: " << params.size() << std::endl;
                    for (const auto& [key, value] : params) {
                        std::cout << "    " << key << ": " << value << std::endl;
                    }
                }
            } else {
                // 如果使用Python帧处理器
                if (aiProcessor->get_python_frame_processor()) {
                    auto frameProcessor = aiProcessor->get_python_frame_processor();
                    std::cout << "Exporting Python frame processor configuration" << std::endl;

                    // 获取脚本路径
                    std::string scriptPath = frameProcessor->get_script_path();
                    project->set_frame_processor_script_path(scriptPath);
                    std::cout << "  Script path: " << scriptPath << std::endl;

                    // 获取是否启用
                    bool enabled = frameProcessor->is_enabled();
                    project->set_frame_processor_enabled(enabled);
                    std::cout << "  Enabled: " << (enabled ? "true" : "false") << std::endl;

                    // 获取参数
                    auto params = frameProcessor->get_params();
                    project->set_frame_processor_params(params);
                    std::cout << "  Parameters: " << params.size() << std::endl;
                    for (const auto& [key, value] : params) {
                        std::cout << "    " << key << ": " << value << std::endl;
                    }
                }
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "导出项目设置失败: " << e.what() << std::endl;
        return false;
    }
}

bool ProjectManager::import_to_video_processing_core(std::shared_ptr<Project> project,
                                                std::shared_ptr<VideoProcessingCore> videoProcessingCore,
                                                const std::string& plugin_directory) {
    if (!project || !videoProcessingCore) {
        return false;
    }

    try {
        // 打开视频文件
        std::string videoPath = project->get_video_path();
        if (!videoPath.empty()) {
            videoProcessingCore->open_video_file(videoPath);
        }

        // 加载模型
        std::string modelPath = project->get_model_path();
        if (!modelPath.empty()) {
            std::cout << "Loading model from project: " << modelPath << std::endl;
            std::cout << "  Prompt: " << project->get_prompt() << std::endl;
            std::cout << "  Score threshold: " << project->get_score_threshold() << std::endl;
            std::cout << "  IOU threshold: " << project->get_iou_threshold() << std::endl;

            bool modelLoaded = videoProcessingCore->load_model(
                modelPath,
                project->get_prompt(),
                project->get_score_threshold(),
                project->get_iou_threshold()
            );

            if (modelLoaded) {
                std::cout << "Model loaded successfully" << std::endl;

                // 初始化运行时
                std::string inputNodeId = project->get_input_node_id();
                std::string outputNodeId = project->get_output_node_id();
                if (!inputNodeId.empty() && !outputNodeId.empty()) {
                    std::cout << "Initializing runtime with input node: " << inputNodeId
                              << ", output node: " << outputNodeId << std::endl;

                    bool runtimeInitialized = videoProcessingCore->initialize_runtime(inputNodeId, outputNodeId);

                    if (runtimeInitialized) {
                        std::cout << "Runtime initialized successfully" << std::endl;
                    } else {
                        std::cerr << "Failed to initialize runtime" << std::endl;
                    }
                }
            } else {
                std::cerr << "Failed to load model" << std::endl;
            }
        }

        // 设置帧跳过间隔
        videoProcessingCore->set_frame_skip_interval(project->get_frame_skip_interval());

        // 设置最大帧历史数量
        if (videoProcessingCore->get_ai_processor()) {
            videoProcessingCore->get_ai_processor()->set_max_frame_history(project->get_max_frame_history());
        }

        // 设置结果存储服务
        if (project->get_enable_result_storage()) {
            // 启动结果存储服务，使用项目中的协议类型
            bool success = videoProcessingCore->start_result_storage_server(
                "results",
                static_cast<VideoResultStorageServer::StorageMode>(project->get_result_storage_mode()),
                project->get_result_storage_tcp_port(),
                project->get_result_storage_flush_interval(),
                project->get_result_storage_protocol_type()
            );

            if (success) {
                std::cout << "Result storage server started with protocol: "
                          << static_cast<int>(project->get_result_storage_protocol_type()) << std::endl;

                // 如果是Modbus协议，设置寄存器映射
                if (project->get_result_storage_protocol_type() == protocols::ProtocolType::MODBUS) {
                    auto registerMap = project->get_modbus_register_map();
                    if (!registerMap.empty()) {
                        std::cout << "Setting Modbus register map with " << registerMap.size() << " entries" << std::endl;
                        videoProcessingCore->set_modbus_register_map(registerMap);
                    }
                }
            } else {
                std::cerr << "Failed to start result storage server" << std::endl;
            }
        }

        // 如果提供了插件目录，则从该目录加载插件并设置参数
        if (!plugin_directory.empty()) {
            std::cout << "Loading plugins from directory: " << plugin_directory << std::endl;
            int loaded_count = videoProcessingCore->load_plugins_and_set_params(plugin_directory, project);
            std::cout << "Loaded and configured " << loaded_count << " plugins from directory" << std::endl;
        } else {
            // 先禁用所有插件
            std::cout << "Disabling all plugins before loading project-specific plugin states" << std::endl;
            videoProcessingCore->disable_all_plugins();

            // 从项目加载插件参数和启用状态
            std::cout << "Loading plugin parameters and states from project" << std::endl;
            if (videoProcessingCore->load_params_from_project(project)) {
                std::cout << "Successfully loaded plugin parameters and states from project" << std::endl;
            } else {
                std::cerr << "Failed to load plugin parameters and states from project" << std::endl;

                // 如果加载失败，使用旧方法作为备选
                // 启用项目中指定的插件（不保存到全局设置）
                auto enabledPlugins = project->get_enabled_plugins();
                std::cout << "Enabling " << enabledPlugins.size() << " plugins from project" << std::endl;
                for (const auto& pluginName : enabledPlugins) {
                    std::cout << "  Enabling plugin: " << pluginName << std::endl;
                    videoProcessingCore->set_plugin_enabled(pluginName, true);
                }

                // 设置插件参数
                auto plugins = videoProcessingCore->get_plugin_names();
                for (const auto& pluginName : plugins) {
                    auto params = project->get_plugin_params(pluginName);
                    if (!params.empty()) {
                        std::cout << "Importing parameters for plugin: " << pluginName << std::endl;
                        for (const auto& [key, value] : params) {
                            std::cout << "  " << key << ": " << value << std::endl;
                        }
                        videoProcessingCore->set_plugin_params(pluginName, params);
                    }
                }
            }
        }

        // 设置帧处理器配置
        if (videoProcessingCore->get_ai_processor()) {
            auto aiProcessor = videoProcessingCore->get_ai_processor();

            // 设置是否使用C++帧处理器
            bool useCpp = project->get_use_cpp_frame_processor();
            aiProcessor->set_use_cpp_frame_processor(useCpp);
            std::cout << "  Setting use C++ frame processor: " << (useCpp ? "true" : "false") << std::endl;

            if (useCpp) {
                // 如果使用C++帧处理器
                if (aiProcessor->get_cpp_frame_processor()) {
                    auto frameProcessor = aiProcessor->get_cpp_frame_processor();
                    std::cout << "Importing C++ frame processor configuration" << std::endl;

                    // 获取项目中的插件名称
                    std::string projectPluginName = project->get_cpp_frame_processor_plugin_name();
                    std::cout << "Project cpp_frame_processor_plugin_name: '" << projectPluginName << "'" << std::endl;

                    // 如果项目中有插件名称，先直接设置
                    if (!projectPluginName.empty()) {
                        std::cout << "  Setting current plugin name directly: '" << projectPluginName << "'" << std::endl;
                        frameProcessor->set_current_plugin_name_directly(projectPluginName);
                    }

                    // 确保帧处理器已经初始化
                    if (!frameProcessor->is_initialized()) {
                        std::cout << "  Initializing C++ frame processor" << std::endl;
                        if (!frameProcessor->initialize()) {
                            std::cerr << "  Failed to initialize C++ frame processor: " << frameProcessor->get_error_message() << std::endl;
                        }
                    }

                    std::cout << "  Current plugin name after initialization: '" << frameProcessor->get_current_plugin_name() << "'" << std::endl;

                    // 设置插件名称
                    std::cout << "Current plugin name before setting: '" << frameProcessor->get_current_plugin_name() << "'" << std::endl;

                    if (!projectPluginName.empty()) {
                        std::cout << "  Setting plugin name: " << projectPluginName << std::endl;

                        // 检查插件是否存在
                        auto pluginNames = frameProcessor->get_plugin_names();
                        bool pluginExists = false;
                        for (const auto& name : pluginNames) {
                            if (name == projectPluginName) {
                                pluginExists = true;
                                break;
                            }
                        }

                        if (pluginExists) {
                            std::cout << "  Plugin '" << projectPluginName << "' exists in available plugins" << std::endl;
                            if (frameProcessor->set_current_plugin(projectPluginName)) {
                                std::cout << "  Plugin set successfully" << std::endl;
                                std::cout << "  Current plugin name after setting: '" << frameProcessor->get_current_plugin_name() << "'" << std::endl;
                            } else {
                                std::cerr << "  Failed to set plugin: " << projectPluginName << std::endl;
                            }
                        } else {
                            std::cerr << "  Plugin not found: " << projectPluginName << std::endl;

                            // 如果有其他插件，使用第一个
                            if (!pluginNames.empty()) {
                                std::string firstPlugin = pluginNames[0];
                                std::cout << "  Using first available plugin instead: " << firstPlugin << std::endl;
                                frameProcessor->set_current_plugin(firstPlugin);
                            }
                        }
                    }

                    // 设置参数
                    auto params = project->get_frame_processor_params();
                    if (!params.empty()) {
                        std::cout << "  Setting parameters: " << params.size() << std::endl;
                        for (const auto& [key, value] : params) {
                            std::cout << "    " << key << ": " << value << std::endl;
                        }
                        frameProcessor->set_params(params);
                    }

                    // 设置是否启用
                    bool enabled = project->get_frame_processor_enabled();
                    std::cout << "  Setting enabled: " << (enabled ? "true" : "false") << std::endl;
                    frameProcessor->set_enabled(enabled);

                    // 确保当前插件也被正确启用
                    if (enabled) {
                        std::string currentPluginName = frameProcessor->get_current_plugin_name();
                        if (!currentPluginName.empty()) {
                            auto plugin = frameProcessor->get_plugin(currentPluginName);
                            if (plugin) {
                                plugin->set_enabled(true);
                            }
                        }
                    }

                    // 保存配置
                    frameProcessor->save_config();

                    // 强制同步设置到磁盘
                    utils::SettingsManager::get_instance().sync();
                }
            } else {
                // 如果使用Python帧处理器
                if (aiProcessor->get_python_frame_processor()) {
                    auto frameProcessor = aiProcessor->get_python_frame_processor();
                    std::cout << "Importing Python frame processor configuration" << std::endl;

                    // 设置脚本路径
                    std::string scriptPath = project->get_frame_processor_script_path();
                    if (!scriptPath.empty()) {
                        std::cout << "  Setting script path: " << scriptPath << std::endl;
                        if (frameProcessor->set_script_path(scriptPath)) {
                            std::cout << "  Script loaded successfully" << std::endl;
                        } else {
                            std::cerr << "  Failed to load script: " << frameProcessor->get_error_message() << std::endl;
                        }
                    }

                    // 设置参数
                    auto params = project->get_frame_processor_params();
                    if (!params.empty()) {
                        std::cout << "  Setting parameters: " << params.size() << std::endl;
                        for (const auto& [key, value] : params) {
                            std::cout << "    " << key << ": " << value << std::endl;
                        }
                        frameProcessor->set_params(params);
                    }

                    // 设置是否启用
                    bool enabled = project->get_frame_processor_enabled();
                    std::cout << "  Setting enabled: " << (enabled ? "true" : "false") << std::endl;
                    frameProcessor->set_enabled(enabled);

                    // 保存配置
                    frameProcessor->save_config();

                    // 强制同步设置到磁盘
                    utils::SettingsManager::get_instance().sync();
                }
            }
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "导入项目设置失败: " << e.what() << std::endl;
        return false;
    }
}

std::vector<std::string> ProjectManager::get_recent_projects() const {
    return recent_projects_;
}

void ProjectManager::add_to_recent_projects(const std::string& filePath) {
    // 如果路径已存在，先移除
    auto it = std::find(recent_projects_.begin(), recent_projects_.end(), filePath);
    if (it != recent_projects_.end()) {
        recent_projects_.erase(it);
    }

    // 添加到列表开头
    recent_projects_.insert(recent_projects_.begin(), filePath);

    // 限制列表大小
    if (recent_projects_.size() > MAX_RECENT_PROJECTS) {
        recent_projects_.resize(MAX_RECENT_PROJECTS);
    }

    // 保存列表
    save_recent_projects();
}

void ProjectManager::clear_recent_projects() {
    recent_projects_.clear();
    save_recent_projects();
}

void ProjectManager::load_recent_projects() {
    auto& settings = utils::SettingsManager::get_instance();

    // 由于我们现在使用标准库实现，需要逐个加载最近项目
    // 这里简化实现，使用索引方式存储
    recent_projects_.clear();

    settings.beginGroup("app");
    settings.beginGroup("recentProjects");

    auto keys = settings.childKeys();
    std::vector<std::pair<int, std::string>> indexed_projects;

    for (const auto& key : keys) {
        try {
            int index = std::stoi(key);
            std::string project_path = settings.value(key, utils::SettingsValue("")).toString();
            if (!project_path.empty()) {
                indexed_projects.emplace_back(index, project_path);
            }
        } catch (...) {
            // 忽略无效的键
        }
    }

    // 按索引排序
    std::sort(indexed_projects.begin(), indexed_projects.end());

    for (const auto& [index, project_path] : indexed_projects) {
        recent_projects_.push_back(project_path);
    }

    settings.endGroup();
    settings.endGroup();
}

void ProjectManager::save_recent_projects() {
    auto& settings = utils::SettingsManager::get_instance();

    settings.beginGroup("app");
    settings.beginGroup("recentProjects");

    // 清除现有的项目列表
    auto keys = settings.childKeys();
    for (const auto& key : keys) {
        settings.remove(key);
    }

    // 保存新的项目列表
    for (size_t i = 0; i < recent_projects_.size(); ++i) {
        settings.setValue(std::to_string(i), utils::SettingsValue(recent_projects_[i]));
    }

    settings.endGroup();
    settings.endGroup();
}

} // namespace core


