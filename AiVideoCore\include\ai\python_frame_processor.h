#pragma once

#include <Python.h>
#include <opencv2/opencv.hpp>
#include <pybind11/numpy.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl.h>

#include <map>
#include <string>
#include "aivideocore_export.h"

namespace py = pybind11;

namespace ai {

/**
 * @brief Python帧处理器，用于在模型推理前处理多帧图像
 */
class AIVIDEOCORE_API PythonFrameProcessor {
public:
    /**
     * @brief 构造函数
     */
    PythonFrameProcessor();

    /**
     * @brief 析构函数
     */
    ~PythonFrameProcessor();

    /**
     * @brief 初始化Python解释器
     * @return 是否初始化成功
     */
    bool initialize();

    /**
     * @brief 加载Python脚本
     * @param script_path 脚本路径
     * @return 是否加载成功
     */
    bool load_script(const std::string& script_path);

    /**
     * @brief 重新加载当前脚本
     * @return 是否重新加载成功
     */
    bool reload_script();

    /**
     * @brief 处理多帧图像
     * @param frames 输入帧列表
     * @return 处理后的单帧图像
     */
    cv::Mat process_frames(const std::vector<cv::Mat>& frames);

    /**
     * @brief 设置脚本参数
     * @param params 参数字典
     */
    void set_params(const std::map<std::string, std::string>& params);

    /**
     * @brief 获取脚本参数
     * @return 参数字典
     */
    std::map<std::string, std::string> get_params() const;

    /**
     * @brief 保存配置到设置文件
     */
    void save_config() const;

    /**
     * @brief 从设置文件加载配置
     */
    void load_config();

    /**
     * @brief 获取脚本路径
     * @return 脚本路径
     */
    std::string get_script_path() const;

    /**
     * @brief 设置脚本路径
     * @param path 脚本路径
     * @return 是否设置成功
     */
    bool set_script_path(const std::string& path);

    /**
     * @brief 获取脚本目录
     * @return 脚本目录
     */
    std::string get_script_directory() const;

    /**
     * @brief 设置脚本目录
     * @param directory 脚本目录
     */
    void set_script_directory(const std::string& directory);

    /**
     * @brief 获取错误信息
     * @return 错误信息
     */
    std::string get_error_message() const;

    /**
     * @brief 是否已启用
     * @return 是否已启用
     */
    bool is_enabled() const;

    /**
     * @brief 设置是否启用
     * @param enabled 是否启用
     */
    void set_enabled(bool enabled);

    /**
     * @brief 是否已初始化
     * @return 是否已初始化
     */
    bool is_initialized() const;

    /**
     * @brief 获取需要的帧数
     * @return 需要的帧数
     */
    int get_required_frames() const;

private:
    /**
     * @brief 将cv::Mat转换为numpy数组
     * @param mat OpenCV图像
     * @return numpy数组
     */
    py::array_t<uint8_t> mat_to_numpy(const cv::Mat& mat) const;

    /**
     * @brief 将numpy数组转换为cv::Mat
     * @param array numpy数组
     * @return OpenCV图像
     */
    cv::Mat numpy_to_mat(const py::array_t<uint8_t>& array) const;

    std::string script_path_;                      ///< Python脚本路径
    std::string script_directory_;                 ///< Python脚本目录
    std::string error_message_;                    ///< 错误信息
    std::map<std::string, std::string> params_;    ///< 脚本参数
    bool enabled_;                                 ///< 是否启用

    py::module_ script_module_;                    ///< Python脚本模块
    py::object initialize_func_;                   ///< 初始化函数
    py::object process_func_;                      ///< 处理函数
    py::object get_required_frames_func_;          ///< 获取需要帧数的函数

    bool is_initialized_;                          ///< 是否已初始化
};

} // namespace ai
