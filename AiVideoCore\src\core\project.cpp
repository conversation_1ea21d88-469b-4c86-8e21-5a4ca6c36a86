#include "core/project.h"

#include <fstream>
#include <iostream>
#include <json/json.h>
#include "utils/string_utils.h"

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/stat.h>
#include <errno.h>
#endif

namespace core {

Project::Project()
    : name_("未命名项目"),
      score_threshold_(0.15),
      iou_threshold_(70),
      frame_skip_interval_(1),
      enable_result_storage_(false),
      result_storage_tcp_port_(8888),
      result_storage_mode_(0),
      result_storage_flush_interval_(5000),
      result_storage_protocol_type_(protocols::ProtocolType::TCP),
      max_frame_history_(30),
      frame_processor_enabled_(false),
      use_cpp_frame_processor_(false),
      cpp_frame_processor_plugin_name_("") {
}

Project::~Project() {
}

void Project::set_name(const std::string& name) {
    name_ = name;
}

std::string Project::get_name() const {
    return name_;
}

void Project::set_file_path(const std::string& path) {
    file_path_ = path;

    // 如果项目名称为默认值，则从文件名中提取项目名称
    if (name_ == "未命名项目" && !path.empty()) {
        // 提取文件名（不包含路径和扩展名）
        size_t lastSlash = path.find_last_of("/\\");
        size_t lastDot = path.find_last_of(".");

        if (lastSlash == std::string::npos) {
            lastSlash = 0; // 没有路径分隔符，从开始位置算起
        } else {
            lastSlash++; // 跳过分隔符
        }

        if (lastDot == std::string::npos || lastDot < lastSlash) {
            // 没有扩展名或扩展名在路径中
            name_ = path.substr(lastSlash);
        } else {
            name_ = path.substr(lastSlash, lastDot - lastSlash);
        }
    }
}

std::string Project::get_file_path() const {
    return file_path_;
}

void Project::set_video_path(const std::string& path) {
    video_path_ = path;
}

std::string Project::get_video_path() const {
    return video_path_;
}

void Project::set_model_path(const std::string& path) {
    model_path_ = path;
}

std::string Project::get_model_path() const {
    return model_path_;
}

void Project::set_input_node_id(const std::string& id) {
    input_node_id_ = id;
}

std::string Project::get_input_node_id() const {
    return input_node_id_;
}

void Project::set_output_node_id(const std::string& id) {
    output_node_id_ = id;
}

std::string Project::get_output_node_id() const {
    return output_node_id_;
}

void Project::set_prompt(const std::string& prompt) {
    prompt_ = prompt;
}

std::string Project::get_prompt() const {
    return prompt_;
}

void Project::set_score_threshold(double score) {
    score_threshold_ = score;
}

double Project::get_score_threshold() const {
    return score_threshold_;
}

void Project::set_iou_threshold(int iou) {
    iou_threshold_ = iou;
}

int Project::get_iou_threshold() const {
    return iou_threshold_;
}

void Project::set_frame_skip_interval(int interval) {
    frame_skip_interval_ = interval;
}

int Project::get_frame_skip_interval() const {
    return frame_skip_interval_;
}

void Project::set_recording_output_path(const std::string& path) {
    recording_output_path_ = path;
}

std::string Project::get_recording_output_path() const {
    return recording_output_path_;
}

void Project::set_enable_result_storage(bool enable) {
    enable_result_storage_ = enable;
}

bool Project::get_enable_result_storage() const {
    return enable_result_storage_;
}

void Project::set_result_storage_tcp_port(int port) {
    result_storage_tcp_port_ = port;
}

int Project::get_result_storage_tcp_port() const {
    return result_storage_tcp_port_;
}

void Project::set_result_storage_mode(int mode) {
    result_storage_mode_ = mode;
}

int Project::get_result_storage_mode() const {
    return result_storage_mode_;
}

void Project::set_result_storage_flush_interval(int interval) {
    result_storage_flush_interval_ = interval;
}

int Project::get_result_storage_flush_interval() const {
    return result_storage_flush_interval_;
}

void Project::set_result_storage_protocol_type(protocols::ProtocolType type) {
    result_storage_protocol_type_ = type;
}

protocols::ProtocolType Project::get_result_storage_protocol_type() const {
    return result_storage_protocol_type_;
}

void Project::set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map) {
    modbus_register_map_ = register_map;
}

std::unordered_map<std::string, uint16_t> Project::get_modbus_register_map() const {
    return modbus_register_map_;
}

void Project::set_max_frame_history(int count) {
    max_frame_history_ = count;
}

int Project::get_max_frame_history() const {
    return max_frame_history_;
}

void Project::set_user_defined_class_names(const std::vector<std::string>& classNames) {
    user_defined_class_names_ = classNames;
}

std::vector<std::string> Project::get_user_defined_class_names() const {
    return user_defined_class_names_;
}

void Project::set_plugin_params(const std::string& pluginName, const std::map<std::string, std::string>& params) {
    plugin_params_[pluginName] = params;
}

std::map<std::string, std::string> Project::get_plugin_params(const std::string& pluginName) const {
    auto it = plugin_params_.find(pluginName);
    if (it != plugin_params_.end()) {
        return it->second;
    }
    return std::map<std::string, std::string>();
}

const std::map<std::string, std::map<std::string, std::string>>& Project::get_all_plugin_params() const {
    return plugin_params_;
}

void Project::set_enabled_plugins(const std::vector<std::string>& plugins) {
    enabled_plugins_ = plugins;
}

std::vector<std::string> Project::get_enabled_plugins() const {
    return enabled_plugins_;
}

void Project::set_frame_processor_script_path(const std::string& path) {
    frame_processor_script_path_ = path;
}

std::string Project::get_frame_processor_script_path() const {
    return frame_processor_script_path_;
}

void Project::set_frame_processor_enabled(bool enabled) {
    frame_processor_enabled_ = enabled;
}

bool Project::get_frame_processor_enabled() const {
    return frame_processor_enabled_;
}

void Project::set_frame_processor_params(const std::map<std::string, std::string>& params) {
    frame_processor_params_ = params;
}

std::map<std::string, std::string> Project::get_frame_processor_params() const {
    return frame_processor_params_;
}

void Project::set_use_cpp_frame_processor(bool use) {
    use_cpp_frame_processor_ = use;
}

bool Project::get_use_cpp_frame_processor() const {
    return use_cpp_frame_processor_;
}

void Project::set_cpp_frame_processor_plugin_name(const std::string& name) {
    cpp_frame_processor_plugin_name_ = name;
}

std::string Project::get_cpp_frame_processor_plugin_name() const {
    return cpp_frame_processor_plugin_name_;
}

// 私有辅助函数，用于从JSON加载项目
namespace {
    bool load_project_from_json(Project& project, const Json::Value& json) {
        try {
            // 基本信息
            if (json.isMember("name") && json["name"].isString()) {
                project.set_name(json["name"].asString());
            }

            if (json.isMember("video_path") && json["video_path"].isString()) {
                project.set_video_path(json["video_path"].asString());
            }

            if (json.isMember("model_path") && json["model_path"].isString()) {
                project.set_model_path(json["model_path"].asString());
            }

            if (json.isMember("input_node_id") && json["input_node_id"].isString()) {
                project.set_input_node_id(json["input_node_id"].asString());
            }

            if (json.isMember("output_node_id") && json["output_node_id"].isString()) {
                project.set_output_node_id(json["output_node_id"].asString());
            }

            // 模型参数
            if (json.isMember("prompt") && json["prompt"].isString()) {
                project.set_prompt(json["prompt"].asString());
            }

            if (json.isMember("score_threshold") && json["score_threshold"].isDouble()) {
                project.set_score_threshold(json["score_threshold"].asDouble());
            }

            if (json.isMember("iou_threshold") && json["iou_threshold"].isInt()) {
                project.set_iou_threshold(json["iou_threshold"].asInt());
            }

            // 处理设置
            if (json.isMember("frame_skip_interval") && json["frame_skip_interval"].isInt()) {
                project.set_frame_skip_interval(json["frame_skip_interval"].asInt());
            }

            if (json.isMember("recording_output_path") && json["recording_output_path"].isString()) {
                project.set_recording_output_path(json["recording_output_path"].asString());
            }

            if (json.isMember("enable_result_storage") && json["enable_result_storage"].isBool()) {
                project.set_enable_result_storage(json["enable_result_storage"].asBool());
            }

            if (json.isMember("result_storage_tcp_port") && json["result_storage_tcp_port"].isInt()) {
                project.set_result_storage_tcp_port(json["result_storage_tcp_port"].asInt());
            }

            if (json.isMember("result_storage_mode") && json["result_storage_mode"].isInt()) {
                project.set_result_storage_mode(json["result_storage_mode"].asInt());
            }

            if (json.isMember("result_storage_flush_interval") && json["result_storage_flush_interval"].isInt()) {
                project.set_result_storage_flush_interval(json["result_storage_flush_interval"].asInt());
            }

            // 结果存储协议类型
            if (json.isMember("result_storage_protocol_type") && json["result_storage_protocol_type"].isInt()) {
                project.set_result_storage_protocol_type(static_cast<protocols::ProtocolType>(json["result_storage_protocol_type"].asInt()));
            }

            // Modbus寄存器映射
            if (json.isMember("modbus_register_map") && json["modbus_register_map"].isObject()) {
                std::unordered_map<std::string, uint16_t> registerMap;
                const auto& jsonRegisterMap = json["modbus_register_map"];
                for (auto it = jsonRegisterMap.begin(); it != jsonRegisterMap.end(); ++it) {
                    const std::string& fieldName = it.key().asString();
                    if (it->isInt()) {
                        registerMap[fieldName] = static_cast<uint16_t>(it->asInt());
                    }
                }
                project.set_modbus_register_map(registerMap);
            }

            if (json.isMember("max_frame_history") && json["max_frame_history"].isInt()) {
                project.set_max_frame_history(json["max_frame_history"].asInt());
            }

            // 用户定义的类别名称
            if (json.isMember("user_defined_class_names") && json["user_defined_class_names"].isArray()) {
                std::vector<std::string> classNames;
                for (const auto& className : json["user_defined_class_names"]) {
                    if (className.isString()) {
                        classNames.push_back(className.asString());
                    }
                }
                project.set_user_defined_class_names(classNames);
            }

            // 插件参数
            if (json.isMember("plugin_params") && json["plugin_params"].isObject()) {
                std::map<std::string, std::map<std::string, std::string>> pluginParams;
                const auto& jsonPluginParams = json["plugin_params"];
                for (auto it = jsonPluginParams.begin(); it != jsonPluginParams.end(); ++it) {
                    const std::string& pluginName = it.key().asString();
                    const auto& params = *it;

                    if (params.isObject()) {
                        std::map<std::string, std::string> paramMap;
                        for (auto paramIt = params.begin(); paramIt != params.end(); ++paramIt) {
                            const std::string& paramName = paramIt.key().asString();
                            const auto& paramValue = *paramIt;

                            if (paramValue.isString()) {
                                paramMap[paramName] = paramValue.asString();
                            } else if (paramValue.isBool()) {
                                paramMap[paramName] = paramValue.asBool() ? "true" : "false";
                            } else if (paramValue.isInt()) {
                                paramMap[paramName] = std::to_string(paramValue.asInt());
                            } else if (paramValue.isDouble()) {
                                paramMap[paramName] = std::to_string(paramValue.asDouble());
                            }
                        }

                        pluginParams[pluginName] = paramMap;
                    }
                }
                for (const auto& [pluginName, paramMap] : pluginParams) {
                    project.set_plugin_params(pluginName, paramMap);
                }
            }

            // 启用的插件列表
            if (json.isMember("enabled_plugins") && json["enabled_plugins"].isArray()) {
                std::vector<std::string> enabledPlugins;
                for (const auto& plugin : json["enabled_plugins"]) {
                    if (plugin.isString()) {
                        enabledPlugins.push_back(plugin.asString());
                    }
                }
                project.set_enabled_plugins(enabledPlugins);
            }

            // 帧处理器设置
            if (json.isMember("frame_processor_script_path") && json["frame_processor_script_path"].isString()) {
                project.set_frame_processor_script_path(json["frame_processor_script_path"].asString());
            }

            if (json.isMember("frame_processor_enabled") && json["frame_processor_enabled"].isBool()) {
                project.set_frame_processor_enabled(json["frame_processor_enabled"].asBool());
            }

            if (json.isMember("frame_processor_params") && json["frame_processor_params"].isObject()) {
                std::map<std::string, std::string> frameProcessorParams;
                const auto& params = json["frame_processor_params"];
                for (auto it = params.begin(); it != params.end(); ++it) {
                    const std::string& paramName = it.key().asString();
                    const auto& paramValue = *it;

                    if (paramValue.isString()) {
                        frameProcessorParams[paramName] = paramValue.asString();
                    } else if (paramValue.isBool()) {
                        frameProcessorParams[paramName] = paramValue.asBool() ? "true" : "false";
                    } else if (paramValue.isInt()) {
                        frameProcessorParams[paramName] = std::to_string(paramValue.asInt());
                    } else if (paramValue.isDouble()) {
                        frameProcessorParams[paramName] = std::to_string(paramValue.asDouble());
                    }
                }
                project.set_frame_processor_params(frameProcessorParams);
            }

            // C++帧处理器设置
            if (json.isMember("use_cpp_frame_processor") && json["use_cpp_frame_processor"].isBool()) {
                project.set_use_cpp_frame_processor(json["use_cpp_frame_processor"].asBool());
            }

            if (json.isMember("cpp_frame_processor_plugin_name") && json["cpp_frame_processor_plugin_name"].isString()) {
                project.set_cpp_frame_processor_plugin_name(json["cpp_frame_processor_plugin_name"].asString());
            }

            return true;
        } catch (const std::exception& e) {
            std::cerr << "加载项目JSON失败: " << e.what() << std::endl;
            return false;
        }
    }
}

// 私有辅助函数，用于将项目转换为JSON
namespace {
    Json::Value project_to_json(const Project& project) {
        Json::Value json;

        // 基本信息
        json["name"] = project.get_name();
        json["video_path"] = project.get_video_path();
        json["model_path"] = project.get_model_path();
        json["input_node_id"] = project.get_input_node_id();
        json["output_node_id"] = project.get_output_node_id();

        // 模型参数
        json["prompt"] = project.get_prompt();
        json["score_threshold"] = project.get_score_threshold();
        json["iou_threshold"] = project.get_iou_threshold();

        // 处理设置
        json["frame_skip_interval"] = project.get_frame_skip_interval();
        json["recording_output_path"] = project.get_recording_output_path();
        json["enable_result_storage"] = project.get_enable_result_storage();
        json["result_storage_tcp_port"] = project.get_result_storage_tcp_port();
        json["result_storage_mode"] = project.get_result_storage_mode();
        json["result_storage_flush_interval"] = project.get_result_storage_flush_interval();
        json["result_storage_protocol_type"] = static_cast<int>(project.get_result_storage_protocol_type());
        json["max_frame_history"] = project.get_max_frame_history();

        // Modbus寄存器映射
        Json::Value registerMap(Json::objectValue);
        for (const auto& [fieldName, address] : project.get_modbus_register_map()) {
            registerMap[fieldName] = static_cast<int>(address);
        }
        json["modbus_register_map"] = registerMap;

        // 用户定义的类别名称
        Json::Value classNames(Json::arrayValue);
        for (const auto& className : project.get_user_defined_class_names()) {
            classNames.append(className);
        }
        json["user_defined_class_names"] = classNames;

        // 插件参数
        Json::Value pluginParams(Json::objectValue);
        // 获取所有插件参数，不仅仅是启用的插件
        for (const auto& [pluginName, params] : project.get_all_plugin_params()) {
            Json::Value paramObj(Json::objectValue);
            for (const auto& [paramName, paramValue] : params) {
                paramObj[paramName] = paramValue;
            }
            pluginParams[pluginName] = paramObj;
        }
        json["plugin_params"] = pluginParams;

        // 启用的插件列表
        Json::Value enabledPlugins(Json::arrayValue);
        for (const auto& plugin : project.get_enabled_plugins()) {
            enabledPlugins.append(plugin);
        }
        json["enabled_plugins"] = enabledPlugins;

        // 帧处理器设置
        json["frame_processor_script_path"] = project.get_frame_processor_script_path();
        json["frame_processor_enabled"] = project.get_frame_processor_enabled();

        // 帧处理器参数
        Json::Value frameProcessorParams(Json::objectValue);
        for (const auto& [paramName, paramValue] : project.get_frame_processor_params()) {
            frameProcessorParams[paramName] = paramValue;
        }
        json["frame_processor_params"] = frameProcessorParams;

        // C++帧处理器设置
        json["use_cpp_frame_processor"] = project.get_use_cpp_frame_processor();
        json["cpp_frame_processor_plugin_name"] = project.get_cpp_frame_processor_plugin_name();

        return json;
    }
}

bool Project::load_from_file(const std::string& filePath) {
    try {
        // 打开文件
#ifdef _WIN32
        // 在Windows上使用宽字符路径来支持中文
        std::wstring wFilePath = utils::utf8ToWide(filePath);
        std::ifstream file(wFilePath);
#else
        std::ifstream file(filePath);
#endif
        if (!file.is_open()) {
            std::cerr << "无法打开项目文件: " << filePath << std::endl;
            return false;
        }

        // 读取整个文件内容
        std::string jsonContent((std::istreambuf_iterator<char>(file)),
                               std::istreambuf_iterator<char>());
        file.close();

        // 设置文件路径
        file_path_ = filePath;

        // 使用JSON字符串加载
        return load_from_json_string(jsonContent);
    } catch (const std::exception& e) {
        std::cerr << "加载项目文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool Project::load_from_json_string(const std::string& jsonStr) {
    try {
        // 解析JSON
        Json::Value root;
        Json::CharReaderBuilder builder;
        std::unique_ptr<Json::CharReader> reader(builder.newCharReader());
        std::string errors;

        // 使用parse方法
        bool parsingSuccessful = reader->parse(
            jsonStr.c_str(),
            jsonStr.c_str() + jsonStr.length(),
            &root,
            &errors
        );

        if (!parsingSuccessful) {
            std::cerr << "解析JSON字符串失败: " << errors << std::endl;
            return false;
        }

        // 加载JSON
        return load_project_from_json(*this, root);
    } catch (const std::exception& e) {
        std::cerr << "从JSON字符串加载项目失败: " << e.what() << std::endl;
        return false;
    }
}

std::string Project::to_json_string() const {
    try {
        // 转换为JSON
        Json::Value root = project_to_json(*this);

        // 转换为字符串
        Json::StyledWriter writer;
        return writer.write(root);
    } catch (const std::exception& e) {
        std::cerr << "转换项目为JSON字符串失败: " << e.what() << std::endl;
        return "{}";
    }
}

bool Project::save_to_file(const std::string& filePath) {
    try {
        // 确保目录存在
#ifdef _WIN32
        // 在Windows上创建目录
        std::string dirPath = filePath.substr(0, filePath.find_last_of("/\\"));
        if (!dirPath.empty()) {
            // 使用系统API创建目录
            std::wstring wDirPath = utils::utf8ToWide(dirPath);
            if (!CreateDirectoryW(wDirPath.c_str(), NULL) &&
                GetLastError() != ERROR_ALREADY_EXISTS) {
                std::cerr << "无法创建目录: " << dirPath << std::endl;
                return false;
            }
        }
#else
        // 在Unix/Linux上创建目录
        std::string dirPath = filePath.substr(0, filePath.find_last_of("/"));
        if (!dirPath.empty()) {
            // 使用系统API创建目录
            if (mkdir(dirPath.c_str(), 0755) != 0 && errno != EEXIST) {
                std::cerr << "无法创建目录: " << dirPath << std::endl;
                return false;
            }
        }
#endif

        // 打开文件
#ifdef _WIN32
        // 在Windows上使用宽字符路径来支持中文
        std::wstring wFilePath = utils::utf8ToWide(filePath);
        std::ofstream file(wFilePath);
#else
        std::ofstream file(filePath);
#endif
        if (!file.is_open()) {
            std::cerr << "无法打开项目文件进行写入: " << filePath << std::endl;
            return false;
        }

        // 获取JSON字符串
        std::string jsonStr = to_json_string();
        file << jsonStr;
        file.close();

        // 更新文件路径
        file_path_ = filePath;

        return true;
    } catch (const std::exception& e) {
        std::cerr << "保存项目文件失败: " << e.what() << std::endl;
        return false;
    }
}

} // namespace core




