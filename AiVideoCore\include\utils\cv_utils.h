#pragma once

#include <opencv2/opencv.hpp>
#include "../aivideocore_export.h"

#include <string>

namespace utils {

/**
 * @brief 在OpenCV图像上绘制中文文字
 * @param img 目标图像
 * @param text 要绘制的文字
 * @param pos 文字位置
 * @param fontSize 字体大小
 * @param color 文字颜色
 * @param thickness 文字粗细
 */
AIVIDEOCORE_API void putTextZH(cv::Mat& img,
               const std::string& text,
               cv::Point pos,
               double fontSize = 1.0,
               cv::Scalar color = cv::Scalar(255, 255, 255),
               int thickness = 1);

} // namespace utils