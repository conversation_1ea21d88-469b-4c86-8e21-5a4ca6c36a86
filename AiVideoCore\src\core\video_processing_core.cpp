#include "core/video_processing_core.h"

#include <chrono>
#include <future>
#include <iostream>
#include <stdexcept>
#include <unordered_map>
#include <filesystem>
#include <iomanip>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#endif

#include "ai/plugins/plugin_manager.h"
#include "core/protocols/result_protocol.h"
#include "utils/log_manager.h"

namespace core {

#ifdef _WIN32
// 内部字符编码转换函数，仅在Windows平台使用
namespace {
    std::wstring utf8_to_wide(const std::string& str) {
        if (str.empty()) return std::wstring();

        int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
        std::wstring wstrTo(size_needed, 0);
        MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
        return wstrTo;
    }

    std::string wide_to_ansi(const std::wstring& wstr) {
        if (wstr.empty()) return std::string();

        int size_needed = WideCharToMultiByte(CP_ACP, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
        std::string strTo(size_needed, 0);
        WideCharToMultiByte(CP_ACP, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
        return strTo;
    }

    std::string utf8_to_ansi(const std::string& str) {
        return wide_to_ansi(utf8_to_wide(str));
    }
}
#endif

VideoProcessingCore::VideoProcessingCore() {
    // 初始化核心组件
    video_provider_ = std::make_shared<utils::VideoFrameProvider>();
    model_manager_ = std::make_shared<ai::AiModelManager>();
    ai_processor_ = std::make_shared<ai::AiProcessor>(model_manager_);
    result_storage_server_ = nullptr;

    // 初始化Python脚本管理器，默认脚本目录为"plugins/python"
    python_script_manager_ = std::make_shared<ai::plugins::PythonScriptManager>("plugins/task");

    // 初始化默认插件
    initialize_default_plugins();
}

VideoProcessingCore::~VideoProcessingCore() {
    try {
        LOG_DEBUG("VideoProcessingCore destructor called");

        // 关闭视频源
        if (video_provider_) {
            LOG_DEBUG("Closing video provider");
            video_provider_->close();
        }

        // 停止结果存储服务
        LOG_DEBUG("Stopping result storage server from destructor");

        // 安全地停止结果存储服务
        try {
            // 首先检查result_storage_server_是否为nullptr
            std::shared_ptr<VideoResultStorageServer> server = result_storage_server_;
            if (server) {
                server->stop();
            }
            result_storage_server_.reset();
        } catch (...) {
            // 即使发生异常，也要确保重置指针
            result_storage_server_.reset();
        }

        LOG_DEBUG("VideoProcessingCore destructor completed");
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in VideoProcessingCore destructor: " + std::string(e.what()));
    } catch (...) {
        LOG_ERROR("Unknown exception in VideoProcessingCore destructor");
    }
}

void VideoProcessingCore::initialize_visionflow(const std::string& licenseId, const std::string& serverAddr) {
    ai::AiModelManager::initialize_visionflow(licenseId, serverAddr);
}

bool VideoProcessingCore::load_model(const std::string& modelPath, const std::string& promptValue,
                                  double scoreValue, int iouValue) {
    if (!model_manager_) {
        return false;
    }

    try {
        // 保存模型路径
        model_path_ = modelPath;
        return model_manager_->load_model(modelPath, promptValue, scoreValue, iouValue);
    } catch (const std::exception& e) {
        LOG_ERROR("模型加载失败: " + std::string(e.what()));
        return false;
    }
}

bool VideoProcessingCore::initialize_runtime(const std::string& inputNodeId, const std::string& outputNodeId) {
    if (!model_manager_ || !model_manager_->is_model_loaded()) {
        return false;
    }

    try {
        this->input_node_id_ = inputNodeId;
        this->output_node_id_ = outputNodeId;
        return model_manager_->initialize_runtime(inputNodeId, outputNodeId);
    } catch (const std::exception& e) {
        LOG_ERROR("运行时初始化失败: " + std::string(e.what()));
        return false;
    }
}

bool VideoProcessingCore::open_video_file(const std::string& filePath) {
    if (!video_provider_) {
        return false;
    }

    bool success = video_provider_->open_video_file(filePath);
    if (success && ai_processor_) {
        ai_processor_->set_fps(video_provider_->get_fps());
    }
    return success;
}

bool VideoProcessingCore::open_camera(int cameraId) {
    if (!video_provider_) {
        return false;
    }

    bool success = video_provider_->open_camera(cameraId);
    if (success && ai_processor_) {
        ai_processor_->set_fps(30.0); // 默认摄像头帧率
    }
    return success;
}

bool VideoProcessingCore::open_rtsp_stream(const std::string& url) {
    if (!video_provider_) {
        return false;
    }

    bool success = video_provider_->open_rtsp_stream(url);
    if (success && ai_processor_) {
        ai_processor_->set_fps(video_provider_->get_fps());
    }
    return success;
}

void VideoProcessingCore::close_video() {
    if (video_provider_) {
        video_provider_->close();
    }
}

ai::FrameResult VideoProcessingCore::process_next_frame(bool enableAI) {
    if (!video_provider_ || !video_provider_->is_opened()) {
        throw std::runtime_error("视频未打开");
    }

    cv::Mat frame;
    if (!video_provider_->read_frame(frame)) {
        throw std::runtime_error("无法读取下一帧");
    }

    return process_frame(frame, enableAI);
}

ai::FrameResult VideoProcessingCore::process_frame(int frameIndex, bool enableAI) {
    if (!video_provider_ || !video_provider_->is_opened()) {
        throw std::runtime_error("视频未打开");
    }

    // 设置帧位置
    video_provider_->set_frame_position(frameIndex);

    cv::Mat frame;
    if (!video_provider_->read_frame(frame)) {
        throw std::runtime_error("无法读取指定帧");
    }

    return process_frame(frame, enableAI);
}

ai::FrameResult VideoProcessingCore::process_frame(const cv::Mat& frame, bool enableAI) {
    ai::FrameResult result;

    if (frame.empty()) {
        throw std::runtime_error("输入帧为空");
    }

    if (enableAI) {
        if (!model_manager_ || !model_manager_->is_model_loaded() || !model_manager_->get_runtime()) {
            throw std::runtime_error("AI模型未正确加载或初始化");
        }

        try {
            // 使用异步方式处理AI，避免长时间阻塞
            std::future<ai::FrameResult> future_result = std::async(
                std::launch::async,
                [this, frame]() {
                    return ai_processor_->process_with_ai(
                        frame,
                        this->input_node_id_,
                        this->output_node_id_
                    );
                }
            );

            // 设置超时时间
            if (future_result.wait_for(std::chrono::milliseconds(5000)) == std::future_status::ready) {
                result = future_result.get();
                
            } else {
                // 如果处理时间过长，返回原始帧
                LOG_WARNING("AI处理超时（5000ms）");
                result.processed_frame = frame.clone();
                result.ext_info = "AI处理超时";
            }
        } catch (const std::exception& e) {
            LOG_ERROR("AI处理异常: " + std::string(e.what()));
            result.processed_frame = frame.clone();
            result.ext_info = std::string("AI处理异常: ") + e.what();
        }
    } else {
        // 不使用AI处理，直接返回原始帧
        result.processed_frame = frame.clone();
        result.task_type = "no_ai";
    }

    // 如果结果存储服务正在运行，自动添加结果
    try {
        std::shared_ptr<VideoResultStorageServer> server = result_storage_server_;
        if (server && server->is_running()) {
            bool success = server->add_result(result);
        } else {
            if (!server) {
                LOG_DEBUG( "VideoProcessingCore: Storage server is null, not adding result");
            } else if (!server->is_running()) {
                LOG_DEBUG("VideoProcessingCore: Storage server is not running, not adding result");
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Exception when adding result to storage: " + std::string(e.what()));
    }

    return result;
}

bool VideoProcessingCore::register_plugin(std::shared_ptr<ai::plugins::TaskPlugin> plugin) {
    if (!ai_processor_) {
        return false;
    }
    return ai_processor_->register_plugin(plugin);
}

bool VideoProcessingCore::unregister_plugin(const std::string& name) {
    if (!ai_processor_) {
        return false;
    }
    return ai_processor_->unregister_plugin(name);
}

bool VideoProcessingCore::enable_plugin(const std::string& name) {
    if (!ai_processor_) {
        return false;
    }
    return ai_processor_->enable_plugin(name);
}

bool VideoProcessingCore::disable_plugin(const std::string& name) {
    if (!ai_processor_) {
        return false;
    }
    return ai_processor_->disable_plugin(name);
}

bool VideoProcessingCore::set_plugin_enabled(const std::string& name, bool enabled) {
    if (!ai_processor_) {
        return false;
    }
    return enabled ? ai_processor_->enable_plugin(name) : ai_processor_->disable_plugin(name);
}

void VideoProcessingCore::disable_all_plugins() {
    if (ai_processor_) {
        // 获取所有插件并禁用它们
        auto plugins = ai_processor_->get_all_plugins();
        for (const auto& plugin : plugins) {
            ai_processor_->disable_plugin(plugin->get_name());
        }
    }
}

int VideoProcessingCore::load_plugins_from_directory(const std::string& directory) {
    if (!ai_processor_) {
        return 0;
    }

    // 使用AiProcessor的插件管理器加载插件
    return ai_processor_->load_dll_plugins_from_directory(directory);
}

std::vector<std::string> VideoProcessingCore::get_plugin_names() const {
    if (!ai_processor_) {
        return {};
    }
    std::vector<std::string> names;
    auto plugins = ai_processor_->get_all_plugins();
    for (const auto& plugin : plugins) {
        names.push_back(plugin->get_name());
    }
    return names;
}

bool VideoProcessingCore::is_plugin_enabled(const std::string& name) const {
    if (!ai_processor_) {
        return false;
    }
    auto plugin = ai_processor_->get_plugin(name);
    if (!plugin) {
        return false;
    }
    return plugin->is_enabled();
}

std::map<std::string, std::string> VideoProcessingCore::get_plugin_params(const std::string& name) const {
    if (!ai_processor_) {
        return {};
    }
    auto plugin = ai_processor_->get_plugin(name);
    if (!plugin) {
        return std::map<std::string, std::string>();
    }

    // 使用TaskPlugin基类的get_params方法获取参数
    return plugin->get_params();
}

bool VideoProcessingCore::set_plugin_params(const std::string& name, const std::map<std::string, std::string>& params) {
    if (!ai_processor_) {
        return false;
    }
    auto plugin = ai_processor_->get_plugin(name);
    if (!plugin) {
        return false;
    }

    // 使用TaskPlugin基类的set_params方法设置参数
    plugin->set_params(params);
    return true;
}

void VideoProcessingCore::initialize_python_frame_processor() {
    if (ai_processor_) {
        ai_processor_->initialize_python_frame_processor();
    }
}

void VideoProcessingCore::initialize_cpp_frame_processor() {
    if (ai_processor_) {
        ai_processor_->initialize_cpp_frame_processor();
    }
}

bool VideoProcessingCore::set_python_frame_processor_script(const std::string& scriptPath,
                                                      const std::map<std::string, std::string>& params) {
    bool success = ai_processor_->set_python_script(scriptPath);
    if (success) {
        ai_processor_->set_python_script_params(params);
    }
    return success;
}

void VideoProcessingCore::enable_python_frame_processor(bool enable) {
    if (ai_processor_) {
        ai_processor_->enable_python_frame_processing(enable);
    }
}

void VideoProcessingCore::set_frame_skip_interval(int interval) {
    if (ai_processor_) {
        ai_processor_->set_frame_skip_interval(interval);
    }
}

int VideoProcessingCore::get_frame_skip_interval() const {
    if (ai_processor_) {
        return ai_processor_->get_frame_skip_interval();
    }
    return 1; // 默认值
}

int VideoProcessingCore::get_total_frames() const {
    return video_provider_ ? video_provider_->get_total_frames() : 0;
}

int VideoProcessingCore::get_current_frame() const {
    return video_provider_ ? video_provider_->get_current_frame() : 0;
}

double VideoProcessingCore::get_fps() const {
    return video_provider_ ? video_provider_->get_fps() : 0.0;
}

bool VideoProcessingCore::is_video_opened() const {
    return video_provider_ && video_provider_->is_opened();
}

bool VideoProcessingCore::is_model_loaded() const {
    return model_manager_ && model_manager_->is_model_loaded();
}

std::shared_ptr<ai::AiProcessor> VideoProcessingCore::get_ai_processor() const {
    return ai_processor_;
}

std::shared_ptr<ai::AiModelManager> VideoProcessingCore::get_model_manager() const {
    return model_manager_;
}

std::shared_ptr<utils::VideoFrameProvider> VideoProcessingCore::get_video_provider() const {
    return video_provider_;
}

std::string VideoProcessingCore::get_model_path() const {
    return model_path_;
}

std::string VideoProcessingCore::get_input_node_id() const {
    return input_node_id_;
}

std::string VideoProcessingCore::get_output_node_id() const {
    return output_node_id_;
}

bool VideoProcessingCore::start_result_storage_server(const std::string& storage_path,
                                                   VideoResultStorageServer::StorageMode mode,
                                                   int port,
                                                   int flush_interval_ms,
                                                   protocols::ProtocolType protocol_type) {
    try {
        // 安全地停止现有的服务实例
        stop_result_storage_server();

        // 确保指针已经重置
        if (result_storage_server_) {
            LOG_WARNING("Warning: result_storage_server_ should be nullptr after stop_result_storage_server()");
            result_storage_server_.reset();
        }

        // 创建新的服务实例
        LOG_INFO("Creating new result storage server instance");
        result_storage_server_ = std::make_shared<VideoResultStorageServer>(storage_path, mode, flush_interval_ms);

        // 启动服务
        LOG_INFO("Starting result storage server with " +
                 get_protocol_type_name(protocol_type) + " protocol on port " + std::to_string(port));
        bool success = result_storage_server_->start(port, protocol_type);

        if (success) {
            LOG_INFO("Result storage server started successfully");
        } else {
            LOG_ERROR("Failed to start result storage server");
            result_storage_server_.reset();
        }

        return success;
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in start_result_storage_server: " + std::string(e.what()));
        result_storage_server_.reset();
        return false;
    } catch (...) {
        LOG_ERROR("Unknown exception in start_result_storage_server");
        result_storage_server_.reset();
        return false;
    }
}

// 辅助方法，将协议类型转换为字符串
std::string VideoProcessingCore::get_protocol_type_name(protocols::ProtocolType type) const {
    switch (type) {
        case protocols::ProtocolType::TCP:
            return "TCP";
        case protocols::ProtocolType::MODBUS:
            return "Modbus";
        case protocols::ProtocolType::MQTT:
            return "MQTT";
        case protocols::ProtocolType::CUSTOM:
            return "Custom";
        default:
            return "Unknown";
    }
}

void VideoProcessingCore::stop_result_storage_server() {
    try {
        // 首先检查result_storage_server_是否为nullptr
        if (result_storage_server_ != nullptr) {
            LOG_INFO("Stopping result storage server...");
            result_storage_server_->stop();
            LOG_INFO("Result storage server stopped, now resetting...");
        } else {
            LOG_INFO("No result storage server to stop");
        }

        // 安全地重置result_storage_server_
        result_storage_server_.reset();
        LOG_INFO("Result storage server reset complete");
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in stop_result_storage_server: " + std::string(e.what()));
        // 即使发生异常，也要确保重置指针
        result_storage_server_.reset();
    } catch (...) {
        LOG_ERROR("Unknown exception in stop_result_storage_server");
        // 即使发生异常，也要确保重置指针
        result_storage_server_.reset();
    }
}

std::shared_ptr<VideoResultStorageServer> VideoProcessingCore::get_result_storage_server() const {
    try {
        return result_storage_server_;
    } catch (...) {
        LOG_ERROR("Exception in get_result_storage_server");
        return nullptr;
    }
}

bool VideoProcessingCore::add_result_to_storage(const ai::FrameResult& result) {
    try {
        std::shared_ptr<VideoResultStorageServer> server = result_storage_server_;
        if (!server) {
            return false;
        }

        return server->add_result(result);
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in add_result_to_storage: " + std::string(e.what()));
        return false;
    } catch (...) {
        LOG_ERROR("Unknown exception in add_result_to_storage");
        return false;
    }
}

bool VideoProcessingCore::set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map) {
    try {
        std::shared_ptr<VideoResultStorageServer> server = result_storage_server_;
        if (!server) {
            LOG_ERROR("Cannot set Modbus register map: result storage server is not initialized");
            return false;
        }

        return server->set_modbus_register_map(register_map);
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in set_modbus_register_map: " + std::string(e.what()));
        return false;
    } catch (...) {
        LOG_ERROR("Unknown exception in set_modbus_register_map");
        return false;
    }
}

std::unordered_map<std::string, uint16_t> VideoProcessingCore::get_modbus_register_map() const {
    try {
        std::shared_ptr<VideoResultStorageServer> server = result_storage_server_;
        if (!server) {
            LOG_ERROR("Cannot get Modbus register map: result storage server is not initialized");
            return {};
        }

        return server->get_modbus_register_map();
    } catch (const std::exception& e) {
        LOG_ERROR("Exception in get_modbus_register_map: " + std::string(e.what()));
        return {};
    } catch (...) {
        LOG_ERROR("Unknown exception in get_modbus_register_map");
        return {};
    }
}

int VideoProcessingCore::extract_frames_by_detection(const std::string& outputPath,
                                                 const std::string& targetClass,
                                                 bool includeTarget,
                                                 std::function<bool(int, int)> progressCallback) {
    if (!video_provider_ || !video_provider_->is_opened()) {
        throw std::runtime_error("视频未打开");
    }

    if (!model_manager_ || !model_manager_->is_model_loaded() || !model_manager_->get_runtime()) {
        throw std::runtime_error("AI模型未正确加载或初始化");
    }

    // 创建输出目录
    std::filesystem::path outputDir(outputPath);
    if (!std::filesystem::exists(outputDir)) {
        std::filesystem::create_directories(outputDir);
    }

    // 保存当前帧位置
    int currentPosition = video_provider_->get_current_frame();

    // 重置视频到开头
    video_provider_->set_frame_position(0);

    int totalFrames = video_provider_->get_total_frames();
    int extractedCount = 0;
    int processedCount = 0;

    // 开始处理帧
    while (processedCount < totalFrames) {
        // 更新进度并检查是否取消
        if (progressCallback) {
            int progressPercent = (processedCount * 100) / totalFrames;
            if (!progressCallback(progressPercent, extractedCount)) {
                // 用户取消操作
                break;
            }
        }

        // 读取当前帧
        cv::Mat frame;
        if (!video_provider_->read_frame(frame)) {
            break;
        }

        // 使用AI处理帧
        ai::FrameResult result;
        try {
            result = ai_processor_->process_with_ai(frame, input_node_id_, output_node_id_);
        } catch (const std::exception& e) {
            LOG_ERROR("AI处理异常: " + std::string(e.what()));
            processedCount++;
            continue;
        }

        // 检查是否包含目标类别
        bool containsTarget = false;
        for (const auto& track : result.detection_tracks) {
            // 如果目标类别为空，则匹配任意类别
            if (targetClass.empty() || track.detect_class == targetClass) {
                containsTarget = true;
                break;
            }
        }

        // 根据包含或不包含目标的条件决定是否保存
        if ((includeTarget && containsTarget) || (!includeTarget && !containsTarget)) {
            // 保存原始帧
            std::ostringstream framePathStream;
            framePathStream << outputPath << "/frame_"
                           << std::setfill('0') << std::setw(6) << processedCount << ".png";
            std::string framePath = framePathStream.str();

#ifdef _WIN32
            std::string savePath = utf8_to_ansi(framePath);
            cv::imwrite(savePath, frame);
#else
            cv::imwrite(framePath, frame);
#endif
            extractedCount++;
        }

        processedCount++;
    }

    // 恢复原始帧位置
    video_provider_->set_frame_position(currentPosition);

    return extractedCount;
}

void VideoProcessingCore::initialize_default_plugins() {
    if (!ai_processor_) {
        return;
    }
}

bool VideoProcessingCore::load_params_from_project(const std::shared_ptr<Project>& project) {
    if (!ai_processor_ || !project) {
        return false;
    }

    // 获取所有插件
    auto plugins = ai_processor_->get_all_plugins();

    // 遍历所有插件，从项目中加载参数
    for (const auto& plugin : plugins) {
        const std::string& name = plugin->get_name();
        auto params = project->get_plugin_params(name);
        if (!params.empty()) {
            // 设置插件参数
            plugin->set_params(params);
        }
    }

    // 设置启用的插件
    auto enabled_plugins = project->get_enabled_plugins();
    for (const auto& name : enabled_plugins) {
        ai_processor_->enable_plugin(name);
    }

    return true;
}

int VideoProcessingCore::set_plugin_params_from_project(const std::shared_ptr<Project>& project) {
    if (!ai_processor_ || !project) {
        return 0;
    }

    // 获取当前已加载的插件名称
    auto current_plugins = get_plugin_names();
    std::set<std::string> current_plugin_set(current_plugins.begin(), current_plugins.end());

    // 先禁用所有插件
    disable_all_plugins();

    // 获取项目中启用的插件列表
    auto enabled_plugins = project->get_enabled_plugins();
    int configured_count = 0;

    // 加载插件参数和启用状态
    if (load_params_from_project(project)) {
        LOG_DEBUG("Successfully loaded plugin parameters and states from project");
        configured_count = current_plugins.size();
    } else {
        LOG_ERROR("Failed to load plugin parameters and states from project");

        // 如果加载失败，使用旧方法作为备选
        // 启用项目中指定的插件
        LOG_DEBUG("Enabling " + std::to_string(enabled_plugins.size()) + " plugins from project");
        for (const auto& pluginName : enabled_plugins) {
            if (current_plugin_set.find(pluginName) != current_plugin_set.end()) {
                LOG_DEBUG("  Enabling plugin: " + pluginName);
                set_plugin_enabled(pluginName, true);
                configured_count++;
            } else {
                LOG_DEBUG("  Plugin not found: " + pluginName);
            }
        }

        // 设置插件参数
        for (const auto& pluginName : current_plugins) {
            auto params = project->get_plugin_params(pluginName);
            if (!params.empty()) {
                LOG_DEBUG("Importing parameters for plugin: " + pluginName);
                for (const auto& [key, value] : params) {
                    LOG_DEBUG("  " + key + ": " + value);
                }
                set_plugin_params(pluginName, params);
            }
        }
    }

    return configured_count;
}

int VideoProcessingCore::load_plugins_from_project_directory(const std::string& plugin_directory) {
    if (!ai_processor_) {
        return 0;
    }

    // 检查插件目录是否存在
    std::filesystem::path pluginDir(plugin_directory);
    if (!std::filesystem::exists(pluginDir)) {
        LOG_ERROR("Plugin directory does not exist: " + plugin_directory);
        return 0;
    }

    // 获取当前已加载的插件名称
    auto current_plugins = get_plugin_names();
    std::set<std::string> current_plugin_set(current_plugins.begin(), current_plugins.end());

    // 如果已经有插件加载，则不尝试重新加载
    if (!current_plugin_set.empty()) {
        LOG_DEBUG("Plugins already loaded, skipping loading from directory");
        return current_plugins.size();
    }

    int loaded_count = 0;

    // 1. 从指定目录加载DLL插件
    int dll_loaded_count = load_plugins_from_directory(plugin_directory);
    LOG_DEBUG("Loaded " + std::to_string(dll_loaded_count) + " DLL plugins from directory: " + plugin_directory);
    loaded_count += dll_loaded_count;

    // 2. 加载Python脚本插件
    if (python_script_manager_) {
        // 设置Python脚本目录为插件目录下的python子目录
        std::string python_script_dir = plugin_directory;
        std::filesystem::path pythonDir(python_script_dir);

        // 如果Python脚本目录存在，则加载脚本
        if (std::filesystem::exists(pythonDir)) {
            python_script_manager_->set_script_directory(python_script_dir);
            int python_loaded_count = python_script_manager_->load_scripts_from_directory();
            LOG_DEBUG("Loaded " + std::to_string(python_loaded_count) + " Python script plugins from directory: " + python_script_dir);

            // 将加载的Python脚本注册到插件管理器
            if (python_loaded_count > 0) {
                auto plugin_manager = ai_processor_->get_plugin_manager();
                if (plugin_manager) {
                    int registered_count = python_script_manager_->register_all_scripts_to_plugin_manager(plugin_manager);
                    LOG_DEBUG("Registered " + std::to_string(registered_count) + " Python script plugins to plugin manager");
                    loaded_count += registered_count;
                }
            }
        } else {
            LOG_DEBUG("Python script directory does not exist: " + python_script_dir + ", skipping Python script loading");
        }
    }

    LOG_DEBUG("Total loaded " + std::to_string(loaded_count) + " plugins from directory: " + plugin_directory);
    return loaded_count;
}

int VideoProcessingCore::load_plugins_and_set_params(const std::string& plugin_directory, const std::shared_ptr<Project>& project) {
    if (!ai_processor_ || !project) {
        LOG_ERROR("AI processor or project is null");
        return 0;
    }

    // 第一步：从指定目录加载插件
    LOG_DEBUG("Step 1: Loading plugins from directory: " + plugin_directory);
    int loaded_count = load_plugins_from_project_directory(plugin_directory);

    // 第二步：设置插件参数和启用状态
    LOG_DEBUG("Step 2: Setting plugin parameters from project");
    set_plugin_params_from_project(project);

    return loaded_count;
}

} // namespace core




