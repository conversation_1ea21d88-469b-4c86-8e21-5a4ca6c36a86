# AiVideoCore

AiVideoCore 是一个基于 VisionFlow 的视频 AI 处理库，提供了视频处理、AI 模型加载和推理等功能。

## 功能特点

- 支持视频文件、摄像头和 RTSP 流的处理
- 集成 VisionFlow AI 推理引擎
- 支持插件系统，可扩展功能
- 提供 C++ API，可被其他应用程序调用

## 编译安装

### 依赖项

- CMake 3.19+
- Qt5
- OpenCV
- VisionFlow
- Python 3.12

### 编译步骤

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### 安装

```bash
cmake --install .
```

## 在其他项目中使用

在 CMake 项目中，可以通过以下方式使用 AiVideoCore：

```cmake
find_package(AiVideoCore REQUIRED)

add_executable(your_app main.cpp)
target_link_libraries(your_app PRIVATE AiVideoCore::AiVideoCore)
```

## 示例代码

```cpp
#include <iostream>
#include <string>
#include "core/video_processing_core.h"

int main() {
    // 初始化 VisionFlow 环境
    core::VideoProcessingCore::initialize_visionflow("your_license_id", "your_server_addr");
    
    // 创建视频处理核心
    core::VideoProcessingCore processor;
    
    // 加载 AI 模型
    if (!processor.load_model("path/to/your/model.vfmodel")) {
        std::cerr << "Failed to load model" << std::endl;
        return 1;
    }
    
    // 初始化运行时
    if (!processor.initialize_runtime("Input", "Output")) {
        std::cerr << "Failed to initialize runtime" << std::endl;
        return 1;
    }
    
    // 打开视频文件
    if (!processor.open_video_file("path/to/your/video.mp4")) {
        std::cerr << "Failed to open video file" << std::endl;
        return 1;
    }
    
    // 处理视频帧
    while (processor.is_video_opened()) {
        try {
            // 处理下一帧
            auto result = processor.process_next_frame();
            
            // 处理结果
            std::cout << "Processed frame with " << result.detection_tracks.size() << " detections" << std::endl;
            
        } catch (const std::exception& e) {
            // 到达视频末尾或发生错误
            std::cerr << "Exception: " << e.what() << std::endl;
            break;
        }
    }
    
    // 关闭视频
    processor.close_video();
    
    return 0;
}
```

## 许可证

请联系开发者获取许可证信息。
