﻿#pragma once

#include <opencv2/opencv.hpp>

#include <memory>
#include <string>
#include "../aivideocore_export.h"

namespace tracking {

/**
 * @brief 跟踪结果的数据结构
 */
class AIVIDEOCORE_API strack {
public:
    /**
     * @brief 默认构造函数
     */
    strack();

    /**
     * @brief 拷贝构造函数
     */
    strack(const strack& other);

    /**
     * @brief 赋值操作符
     */
    strack& operator=(const strack& other);

    /**
     * @brief 更新卡尔曼滤波器状态
     * @param detection 检测到的目标框
     */
    void update_kalman_filter(const cv::Rect_<float>& detection);

    /**
     * @brief 预测下一帧位置
     * @return 预测的目标框
     */
    cv::Rect_<float> predict_next_position();

    /**
     * @brief 计算两个目标框的IOU
     * @param rect1 第一个目标框
     * @param rect2 第二个目标框
     * @return IOU值
     */
    static float calculate_iou(const cv::Rect_<float>& rect1, const cv::Rect_<float>& rect2);

public:
    int track_id;               ///< 跟踪ID
    cv::Rect_<float> tlwh;      ///< 目标框 (top-left width-height)
    std::string detect_class;   ///< 检测类别
    float score;                ///< 置信度
    int state;                  ///< 状态 (0: 新目标, 1: 跟踪中, 2: 丢失, 3: 确认消失)
    int lost_frames;            ///< 丢失帧数
    int track_count;            ///< 追踪次数计数

private:
    /**
     * @brief 初始化卡尔曼滤波器
     */
    void init_kalman_filter();

    // 卡尔曼滤波器状态向量
    std::unique_ptr<cv::KalmanFilter> kf_;  ///< 卡尔曼滤波器
    cv::Mat state_kf_;                      ///< 状态向量 [x, y, w, h, vx, vy, vw, vh]
};

// 保持旧版本兼容性
typedef strack STrack;

} // namespace tracking
