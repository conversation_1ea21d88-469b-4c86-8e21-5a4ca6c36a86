#pragma once

#include <string>
#include <map>
#include <vector>
#include <opencv2/opencv.hpp>
#include "aivideocore_export.h"

namespace ai {
namespace plugins {

/**
 * @brief 帧处理器插件接口，用于在模型推理前处理多帧图像
 */
class AIVIDEOCORE_API FrameProcessorPlugin {
public:
    /**
     * @brief 构造函数
     * @param name 插件名称
     */
    FrameProcessorPlugin(const std::string& name) : name_(name), enabled_(false) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~FrameProcessorPlugin() = default;

    /**
     * @brief 获取插件名称
     * @return 插件名称
     */
    std::string get_name() const { return name_; }

    /**
     * @brief 设置插件名称
     * @param name 插件名称
     */
    void set_name(const std::string& name) { name_ = name; }

    /**
     * @brief 设置插件是否启用
     * @param enabled 是否启用
     */
    void set_enabled(bool enabled) {
        enabled_ = enabled;
    }

    /**
     * @brief 获取插件是否启用
     * @return 是否启用
     */
    bool is_enabled() const {
        return enabled_;
    }

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    virtual bool initialize() = 0;

    /**
     * @brief 处理多帧图像
     * @param frames 输入帧列表
     * @return 处理后的单帧图像
     */
    virtual cv::Mat process_frames(const std::vector<cv::Mat>& frames) = 0;

    /**
     * @brief 设置插件参数
     * @param params 参数字典
     */
    virtual void set_params(const std::map<std::string, std::string>& params) {
        params_ = params;
    }

    /**
     * @brief 获取插件参数
     * @return 参数字典
     */
    virtual std::map<std::string, std::string> get_params() const {
        return params_;
    }

    /**
     * @brief 获取需要的帧数
     * @return 需要的帧数
     */
    virtual int get_required_frames() const = 0;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    virtual std::string get_description() const = 0;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    virtual std::string get_author() const = 0;

    /**
     * @brief 获取错误信息
     * @return 错误信息
     */
    std::string get_error_message() const { return error_message_; }

protected:
    std::string name_;                                  ///< 插件名称
    bool enabled_;                                      ///< 是否启用
    std::map<std::string, std::string> params_;         ///< 插件参数
    std::string error_message_;                         ///< 错误信息
};

} // namespace plugins
} // namespace ai
