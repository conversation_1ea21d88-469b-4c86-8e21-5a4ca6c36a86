#pragma once

// 定义导出宏
#if defined(_MSC_VER)
    #if defined(XINAO_PROJECT_EXPORTS)
        #define XINAO_API __declspec(dllexport)
    #else
        #define XINAO_API __declspec(dllimport)
    #endif
#else
    #define XINAO_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

// C接口包装器，避免C++依赖问题
// 这个接口将在运行时动态加载AiVideoCore功能

typedef struct AnomalyDetectionHandle* AnomalyDetectionHandle_t;

/**
 * 创建异常检测实例
 * @param project_path 项目配置文件路径
 * @return 异常检测句柄，失败返回NULL
 */
XINAO_API AnomalyDetectionHandle_t anomaly_detection_create(const char* project_path);

/**
 * 销毁异常检测实例
 * @param handle 异常检测句柄
 */
XINAO_API void anomaly_detection_destroy(AnomalyDetectionHandle_t handle);

/**
 * 执行异常检测推理
 * @param handle 异常检测句柄
 * @param video_path 视频文件路径
 * @param save_path 结果保存路径
 * @param keep_images 是否保留图像文件
 * @return 1表示成功，0表示失败
 */
XINAO_API int anomaly_detection_infer(AnomalyDetectionHandle_t handle,
                                     const char* video_path,
                                     const char* save_path,
                                     int keep_images);

/**
 * 获取最后的错误信息
 * @return 错误信息字符串
 */
XINAO_API const char* anomaly_detection_get_last_error(void);

#ifdef __cplusplus
}
#endif
