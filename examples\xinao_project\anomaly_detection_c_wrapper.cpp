#include "anomaly_detection_c_wrapper.h"
#include "anomaly_detection_interface.h"
#include <string>
#include <memory>

// 全局错误信息
static std::string g_last_error;

// 内部句柄结构
struct AnomalyDetectionHandle {
    std::unique_ptr<AnomalyDetectionInterface> interface;
    
    AnomalyDetectionHandle(const char* project_path) {
        try {
            interface = std::make_unique<AnomalyDetectionInterface>(project_path);
        } catch (const std::exception& e) {
            g_last_error = "Failed to create AnomalyDetectionInterface: " + std::string(e.what());
            interface = nullptr;
        }
    }
    
    ~AnomalyDetectionHandle() = default;
    
    bool is_valid() const {
        return interface != nullptr;
    }
};

extern "C" {

AnomalyDetectionHandle_t anomaly_detection_create(const char* project_path) {
    if (!project_path) {
        g_last_error = "Project path cannot be null";
        return nullptr;
    }
    
    try {
        auto handle = new AnomalyDetectionHandle(project_path);
        if (handle->is_valid()) {
            g_last_error.clear();
            return handle;
        } else {
            delete handle;
            return nullptr;
        }
    } catch (const std::exception& e) {
        g_last_error = "Exception in anomaly_detection_create: " + std::string(e.what());
        return nullptr;
    }
}

void anomaly_detection_destroy(AnomalyDetectionHandle_t handle) {
    if (handle) {
        delete handle;
    }
}

int anomaly_detection_infer(AnomalyDetectionHandle_t handle, 
                           const char* video_path, 
                           const char* save_path, 
                           int keep_images) {
    if (!handle || !handle->is_valid()) {
        g_last_error = "Invalid handle";
        return 0;
    }
    
    if (!video_path || !save_path) {
        g_last_error = "Video path and save path cannot be null";
        return 0;
    }
    
    try {
        bool result = handle->interface->infer(video_path, save_path, keep_images != 0);
        if (result) {
            g_last_error.clear();
            return 1;
        } else {
            g_last_error = "Inference failed";
            return 0;
        }
    } catch (const std::exception& e) {
        g_last_error = "Exception in anomaly_detection_infer: " + std::string(e.what());
        return 0;
    }
}

const char* anomaly_detection_get_last_error(void) {
    return g_last_error.c_str();
}

} // extern "C"
