#include "ai/frame_memory.h"

namespace ai {

FrameMemory::FrameMemory(int max_history)
    : max_history_(max_history) {
    // 初始化历史缓存
    frame_history_.clear();
    track_history_.clear();
}

FrameMemory::~FrameMemory() {
    // 清空历史缓存
    clear();
}

void FrameMemory::add_frame(const cv::Mat& frame, const std::vector<tracking::strack>& tracks) {
    // 添加新帧到历史缓存
    frame_history_.push_front(frame.clone());

    // 限制帧历史缓存大小
    while (frame_history_.size() > static_cast<size_t>(max_history_)) {
        frame_history_.pop_back();
    }

    // 如果提供了跟踪结果，则调用add_tracks方法
    if (!tracks.empty()) {
        add_tracks(tracks);
    }
}

void FrameMemory::add_tracks(const std::vector<tracking::strack>& tracks) {
    // 如果没有帧历史，不添加跟踪结果
    if (frame_history_.empty()) {
        return;
    }

    // 添加跟踪结果到历史缓存
    track_history_.push_front(tracks);

    // 确保跟踪结果历史不超过帧历史
    while (track_history_.size() > frame_history_.size()) {
        track_history_.pop_back();
    }

    // 限制历史缓存大小
    while (track_history_.size() > static_cast<size_t>(max_history_)) {
        track_history_.pop_back();
    }
}

std::vector<cv::Mat> FrameMemory::get_frames(int count) const {
    // 获取指定数量的历史帧
    std::vector<cv::Mat> frames;
    size_t actual_count = std::min(static_cast<size_t>(count), frame_history_.size());
    frames.reserve(actual_count);

    for (size_t i = 0; i < actual_count; ++i) {
        frames.push_back(frame_history_[i]);
    }

    return frames;
}

std::vector<std::vector<tracking::strack>> FrameMemory::get_tracks(int count) const {
    // 获取指定数量的历史跟踪结果
    std::vector<std::vector<tracking::strack>> tracks;
    size_t actual_count = std::min(static_cast<size_t>(count), track_history_.size());
    tracks.reserve(count); // 预分配请求的数量，因为可能需要填充空跟踪结果

    // 添加实际存在的跟踪结果
    for (size_t i = 0; i < actual_count; ++i) {
        tracks.push_back(track_history_[i]);
    }

    // 如果请求的数量超过实际存在的跟踪结果数量，填充空跟踪结果
    for (size_t i = actual_count; i < static_cast<size_t>(count); ++i) {
        tracks.push_back(std::vector<tracking::strack>());
    }

    return tracks;
}

const std::deque<cv::Mat>& FrameMemory::get_all_frames() const {
    return frame_history_;
}

const std::deque<std::vector<tracking::strack>>& FrameMemory::get_all_tracks() const {
    return track_history_;
}

void FrameMemory::clear() {
    frame_history_.clear();
    track_history_.clear();
}

void FrameMemory::set_max_history(int count) {
    if (count > 0) {
        max_history_ = count;
        // 如果当前历史帧数超过新的最大值，删除多余的帧
        while (frame_history_.size() > static_cast<size_t>(max_history_)) {
            frame_history_.pop_back();
        }
        while (track_history_.size() > static_cast<size_t>(max_history_)) {
            track_history_.pop_back();
        }
    }
}

int FrameMemory::get_max_history() const {
    return max_history_;
}

size_t FrameMemory::get_frame_count() const {
    return frame_history_.size();
}

size_t FrameMemory::get_track_count() const {
    return track_history_.size();
}

bool FrameMemory::is_matched() const {
    return frame_history_.size() == track_history_.size();
}

} // namespace ai
