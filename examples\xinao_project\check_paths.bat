@echo off
echo ========================================
echo 检查文件路径和依赖项
echo ========================================

echo 当前目录: %CD%

echo.
echo 检查关键头文件:
if exist "..\..\AiVideoCore\include\aivideocore_export.h" (
    echo ✓ aivideocore_export.h 存在
) else (
    echo ✗ aivideocore_export.h 不存在
    echo 路径: ..\..\AiVideoCore\include\aivideocore_export.h
)

if exist "anomaly_detection_interface.h" (
    echo ✓ anomaly_detection_interface.h 存在
) else (
    echo ✗ anomaly_detection_interface.h 不存在
)

if exist "anomaly_detection.h" (
    echo ✓ anomaly_detection.h 存在
) else (
    echo ✗ anomaly_detection.h 不存在
)

echo.
echo 检查AiVideoCore目录结构:
if exist "..\..\AiVideoCore" (
    echo ✓ AiVideoCore 目录存在
    dir /b "..\..\AiVideoCore"
) else (
    echo ✗ AiVideoCore 目录不存在
)

echo.
echo 检查include目录:
if exist "..\..\AiVideoCore\include" (
    echo ✓ include 目录存在
    echo include目录内容:
    dir /b "..\..\AiVideoCore\include"
) else (
    echo ✗ include 目录不存在
)

echo.
echo 检查构建目录:
if exist "..\..\build" (
    echo ✓ build 目录存在
) else (
    echo ✗ build 目录不存在
    echo 请先运行: mkdir build && cd build && cmake ..
)

pause
