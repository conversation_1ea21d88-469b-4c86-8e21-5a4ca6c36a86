#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include "anomaly_detection_interface.h"

namespace py = pybind11;

PYBIND11_MODULE(xinao_anomaly_detection, m) {
    m.doc() = "Xinao Anomaly Detection Python Bindings";
    
    // 绑定 AnomalyDetectionInterface 类
    py::class_<AnomalyDetectionInterface>(m, "AnomalyDetectionInterface")
        .def(py::init<const char*>(), 
             "构造函数，初始化异常检测接口",
             py::arg("project_path"))
        .def("infer", &AnomalyDetectionInterface::infer,
             "执行异常检测推理",
             py::arg("video_path"), 
             py::arg("save_path"), 
             py::arg("keep_images") = false,
             "参数:\n"
             "  video_path: 输入视频文件路径\n"
             "  save_path: 结果保存路径\n"
             "  keep_images: 是否保留处理过程中的图像文件，默认为False\n"
             "返回:\n"
             "  bool: 检测是否成功");
    
    // 添加模块级别的函数和常量
    m.attr("__version__") = "1.0.0";
    m.attr("__author__") = "AiVideoCore Team";
}
