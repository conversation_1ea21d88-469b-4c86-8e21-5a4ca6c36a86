#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include "anomaly_detection_c_wrapper.h"
#include <stdexcept>
#include <string>

namespace py = pybind11;

// Python包装类，使用C接口避免循环依赖
class AnomalyDetectionPython {
private:
    AnomalyDetectionHandle_t handle_;

public:
    AnomalyDetectionPython(const std::string& project_path) {
        handle_ = anomaly_detection_create(project_path.c_str());
        if (!handle_) {
            std::string error = "Failed to create anomaly detection instance: ";
            const char* last_error = anomaly_detection_get_last_error();
            if (last_error && strlen(last_error) > 0) {
                error += last_error;
            }
            throw std::runtime_error(error);
        }
    }

    ~AnomalyDetectionPython() {
        if (handle_) {
            anomaly_detection_destroy(handle_);
        }
    }

    bool infer(const std::string& video_path, const std::string& save_path, bool keep_images = false) {
        if (!handle_) {
            throw std::runtime_error("Invalid anomaly detection handle");
        }

        int result = anomaly_detection_infer(handle_, video_path.c_str(), save_path.c_str(), keep_images ? 1 : 0);
        if (result == 0) {
            const char* last_error = anomaly_detection_get_last_error();
            if (last_error && strlen(last_error) > 0) {
                throw std::runtime_error("Inference failed: " + std::string(last_error));
            }
        }
        return result != 0;
    }
};

PYBIND11_MODULE(xinao_anomaly_detection, m) {
    m.doc() = "Xinao Anomaly Detection Python Bindings (C Wrapper)";

    // 绑定包装类
    py::class_<AnomalyDetectionPython>(m, "AnomalyDetectionInterface")
        .def(py::init<const std::string&>(),
             "构造函数，初始化异常检测接口",
             py::arg("project_path"))
        .def("infer", &AnomalyDetectionPython::infer,
             "执行异常检测推理",
             py::arg("video_path"),
             py::arg("save_path"),
             py::arg("keep_images") = false,
             "参数:\n"
             "  video_path: 输入视频文件路径\n"
             "  save_path: 结果保存路径\n"
             "  keep_images: 是否保留处理过程中的图像文件，默认为False\n"
             "返回:\n"
             "  bool: 检测是否成功");

    // C接口函数（可选，用于调试）
    m.def("get_last_error", &anomaly_detection_get_last_error, "获取最后的错误信息");

    // 添加模块级别的函数和常量
    m.attr("__version__") = "1.0.0";
    m.attr("__author__") = "AiVideoCore Team";
}
