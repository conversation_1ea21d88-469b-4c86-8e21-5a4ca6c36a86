﻿#pragma once

#include <vector>

#include "tracking/object.h"
#include "tracking/strack.h"
#include "../aivideocore_export.h"

namespace tracking {

/**
 * @brief 改进的ByteTracker类，用于目标跟踪
 */
class AIVIDEOCORE_API BYTETracker {
public:
    /**
     * @brief 构造函数
     * @param frame_rate 视频帧率
     * @param track_buffer 跟踪缓冲区大小
     */
    BYTETracker(int frame_rate = 30, int track_buffer = 30);

    /**
     * @brief 更新跟踪状态
     * @param objects 当前帧检测到的目标
     * @return 跟踪结果
     */
    std::vector<strack> update(const std::vector<object>& objects);

private:
    int frame_id_;               ///< 当前帧ID
    int track_id_count_;         ///< 跟踪ID计数器
    int max_time_lost_;          ///< 最大丢失时间
    float min_iou_threshold_;    ///< 最小IOU阈值
    std::vector<strack> tracked_stracks_;  ///< 正在跟踪的目标
};

} // namespace tracking
