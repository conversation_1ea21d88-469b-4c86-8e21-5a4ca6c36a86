﻿#pragma once

#include <json/json.h>
#include <opencv2/opencv.hpp>

#include <deque>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "ai/ai_model_manager.h"
#include "ai/cpp_frame_processor.h"
#include "ai/frame_memory.h"
#include "ai/frame_result.h"
#include "ai/plugins/plugin_manager.h"
#include "ai/plugins/task_plugin.h"
#include "ai/python_frame_processor.h"
#include "tracking/byte_tracker.h"
#include "aivideocore_export.h"

namespace ai {

/**
 * @brief AI处理类，负责使用AI模型处理图像
 */
class AIVIDEOCORE_API AiProcessor {
public:
    /**
     * @brief 构造函数
     * @param modelManager AI模型管理器
     */
    AiProcessor(std::shared_ptr<AiModelManager> modelManager);

    /**
     * @brief 析构函数
     */
    ~AiProcessor();

    /**
     * @brief 使用AI模型处理图像
     * @param inputFrame 输入图像
     * @param inputNodeId 输入节点ID
     * @param outputNodeId 输出节点ID
     * @return 处理结果，包含处理后的图像和各种分析结果
     */
    FrameResult process_with_ai(const cv::Mat& inputFrame, const std::string& inputNodeId,
                         const std::string& outputNodeId);

    /**
     * @brief 注册任务处理插件
     * @param plugin 插件指针
     * @return 是否注册成功
     */
    bool register_plugin(std::shared_ptr<plugins::TaskPlugin> plugin);

    /**
     * @brief 注销任务处理插件
     * @param name 插件名称
     * @return 是否注销成功
     */
    bool unregister_plugin(const std::string& name);

    /**
     * @brief 获取插件
     * @param name 插件名称
     * @return 插件指针，如果不存在则返回nullptr
     */
    std::shared_ptr<plugins::TaskPlugin> get_plugin(const std::string& name);

    /**
     * @brief 获取所有插件
     * @return 所有插件的列表
     */
    std::vector<std::shared_ptr<plugins::TaskPlugin>> get_all_plugins();

    /**
     * @brief 启用插件
     * @param name 插件名称
     * @return 是否成功启用
     */
    bool enable_plugin(const std::string& name);

    /**
     * @brief 禁用插件
     * @param name 插件名称
     * @return 是否成功禁用
     */
    bool disable_plugin(const std::string& name);

    /**
     * @brief 加载DLL任务插件
     * @param dll_path DLL文件路径
     * @return 是否加载成功
     */
    bool load_dll_plugin(const std::string& dll_path);

    /**
     * @brief 从目录加载所有DLL任务插件
     * @param directory 目录路径
     * @return 成功加载的插件数量
     */
    int load_dll_plugins_from_directory(const std::string& directory);

    /**
     * @brief 重新加载DLL插件
     * @param directory 插件目录，如果为空则使用默认目录
     * @return 成功加载的插件数量
     */
    int reload_dll_plugins(const std::string& directory = "");

    /**
     * @brief 重置计数
     */
    void reset_counts();

    /**
     * @brief 获取总计数
     * @return 总计数
     */
    int get_total_count() const;

    /**
     * @brief 获取各类别计数
     * @return 各类别计数
     */
    const std::map<std::string, int>& get_class_counts() const;

    /**
     * @brief 设置视频帧率，用于目标跟踪
     * @param fps 帧率
     */
    void set_fps(double fps);

    /**
     * @brief 设置抽帧间隔，即每隔多少帧进行一次AI检测
     * @param interval 抽帧间隔，默认为1（每帧都检测）
     */
    void set_frame_skip_interval(int interval);

    /**
     * @brief 获取当前抽帧间隔
     * @return 抽帧间隔
     */
    int get_frame_skip_interval() const;



    /**
     * @brief 设置最大帧历史数量
     * @param count 最大帧数
     */
    void set_max_frame_history(int count);

    /**
     * @brief 获取最大帧历史数量
     * @return 最大帧数
     */
    int get_max_frame_history() const;

    /**
     * @brief 获取Python帧处理器
     * @return Python帧处理器指针
     */
    std::shared_ptr<PythonFrameProcessor> get_python_frame_processor() const;

    /**
     * @brief 获取C++帧处理器
     * @return C++帧处理器指针
     */
    std::shared_ptr<CppFrameProcessor> get_cpp_frame_processor() const;

    /**
     * @brief 初始C++帧处理器
     * @return 是否初始化成功
     */
    bool initialize_cpp_frame_processor();

    /**
     * @brief 设置是否使用C++帧处理器
     * @param use 是否使用
     */
    void set_use_cpp_frame_processor(bool use);

    /**
     * @brief 获取是否使用C++帧处理器
     * @return 是否使用
     */
    bool is_using_cpp_frame_processor() const;

    /**
     * @brief 获取帧内存管理器
     * @return 帧内存管理器
     */
    std::shared_ptr<FrameMemory> get_frame_memory() const;

    /**
     * @brief 获取帧历史缓存
     * @return 帧历史缓存
     * @deprecated 请使用getFrameMemory()->get_all_frames()代替
     */
    const std::deque<cv::Mat>& get_frame_history() const;

    /**
     * @brief 更新帧历史缓存
     * @param frame 新帧
     * @param tracks 跟踪结果
     * @deprecated 请使用getFrameMemory()->add_frame()代替
     */
    void update_frame_history(const cv::Mat& frame, const std::vector<tracking::strack>& tracks = {});

    /**
     * @brief 设置Python脚本路径
     * @param script_path 脚本路径
     * @return 是否设置成功
     */
    bool set_python_script(const std::string& script_path);

    /**
     * @brief 启用/禁用Python帧处理
     * @param enable 是否启用
     */
    void enable_python_frame_processing(bool enable);

    /**
     * @brief 设置Python脚本参数
     * @param params 参数字典
     */
    void set_python_script_params(const std::map<std::string, std::string>& params);

    /**
     * @brief 获取Python脚本参数
     * @return 参数字典
     */
    std::map<std::string, std::string> get_python_script_params() const;

    /**
     * @brief 初始化Python帧处理器
     * @return 是否初始化成功
     */
    bool initialize_python_frame_processor();

    /**
     * @brief 获取插件管理器
     * @return 插件管理器指针
     */
    std::shared_ptr<plugins::PluginManager> get_plugin_manager() const;

private:
    /**
     * @brief 执行模型推理
     * @param inputFrame 输入图像
     * @param inputNodeId 输入节点ID
     * @param outputNodeId 输出节点ID
     * @return 模型推理结果
     */
    visionflow::props::PolygonRegionList perform_model_inference(const cv::Mat& inputFrame,
                                                              const std::string& inputNodeId,
                                                              const std::string& outputNodeId);

    /**
     * @brief 执行目标跟踪
     * @param segment_pred 模型推理结果
     * @return 跟踪结果
     */
    std::vector<tracking::strack> perform_tracking(const visionflow::props::PolygonRegionList& segment_pred);

    /**
     * @brief 执行任务处理（使用插件）
     * @param tmp_mat 待处理图像
     * @param output_stracks 跟踪结果
     * @param result 处理结果结构
     * @param segment_pred 模型推理结果（用于普通模式绘制）
     */
    void perform_tasks(cv::Mat& tmp_mat,
                      const std::vector<tracking::strack>& output_stracks,
                      FrameResult& result,
                      const visionflow::props::PolygonRegionList& segment_pred);

    /**
     * @brief 初始化默认插件
     */
    void initialize_default_plugins();

    std::shared_ptr<AiModelManager> model_manager_;  ///< AI模型管理器

    // 计数相关
    std::map<int, std::string> track_class_map_;      ///< 跟踪ID到类别的映射
    std::map<std::string, int> class_counts_;       ///< 每个类别的计数
    int total_count_;                               ///< 总计数

    // ByteTrack相关
    std::unique_ptr<tracking::BYTETracker> tracker_;  ///< 目标跟踪器
    double fps_;                                      ///< 视频帧率

    int frame_index_;                                  ///< 当前帧索引

    // 抽帧检测相关
    int frame_skip_interval_;                           ///< 抽帧间隔，每隔多少帧进行一次AI检测
    visionflow::props::PolygonRegionList last_detection_result_; ///< 上次检测结果
    std::vector<tracking::strack> last_tracking_result_; ///< 上次跟踪结果

    // 帧历史相关
    std::shared_ptr<FrameMemory> frame_memory_;        ///< 帧内存管理器

    // 帧处理器相关
    std::shared_ptr<PythonFrameProcessor> python_frame_processor_;  ///< Python帧处理器
    std::shared_ptr<CppFrameProcessor> cpp_frame_processor_;        ///< C++帧处理器
    bool use_cpp_frame_processor_;                                  ///< 是否使用C++帧处理器

    std::shared_ptr<plugins::PluginManager> plugin_manager_;        ///< 插件管理器

    // 绘制辅助函数
    void draw_counting_info(cv::Mat& frame, const std::vector<tracking::strack>& tracks);
    void draw_basic_tracking_info(cv::Mat& frame,
                              const std::vector<tracking::strack>& tracks);

    // 统一渲染函数
    void render_frame_result(cv::Mat& frame, const FrameResult& result);
};

} // namespace ai





