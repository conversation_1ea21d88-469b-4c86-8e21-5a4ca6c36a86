#include "ai/plugins/dll_plugin_loader.h"
#include <iostream>
#include <filesystem>

namespace ai {
namespace plugins {

// 定义插件API函数类型
typedef FrameProcessorPlugin* (*CreatePluginFunc)();
typedef void (*DestroyPluginFunc)(FrameProcessorPlugin*);
typedef int (*GetPluginApiVersionFunc)();

// 自定义删除器，用于在shared_ptr销毁时正确释放DLL插件
class PluginDeleter {
public:
    PluginDeleter(HMODULE handle, DestroyPluginFunc destroy_func)
        : handle_(handle), destroy_func_(destroy_func) {}

    void operator()(FrameProcessorPlugin* plugin) {
        if (plugin && destroy_func_) {
            destroy_func_(plugin);
        }
        if (handle_) {
            FreeLibrary(handle_);
        }
    }

private:
    HMODULE handle_;
    DestroyPluginFunc destroy_func_;
};

DllPluginLoader::DllPluginLoader() {
}

DllPluginLoader::~DllPluginLoader() {
}

std::shared_ptr<FrameProcessorPlugin> DllPluginLoader::load_plugin(const std::string& dll_path) {
    error_message_.clear();

    // 加载DLL
    HMODULE handle = LoadLibraryA(dll_path.c_str());
    if (!handle) {
        error_message_ = "Failed to load DLL: " + dll_path + ", error: " + get_windows_error_message();
        return nullptr;
    }

    // 检查是否是有效的插件
    if (!is_valid_plugin(handle)) {
        FreeLibrary(handle);
        return nullptr;
    }

    // 获取导出函数
    CreatePluginFunc create_func = (CreatePluginFunc)GetProcAddress(handle, "CreatePlugin");
    DestroyPluginFunc destroy_func = (DestroyPluginFunc)GetProcAddress(handle, "DestroyPlugin");
    GetPluginApiVersionFunc version_func = (GetPluginApiVersionFunc)GetProcAddress(handle, "GetPluginApiVersion");

    if (!create_func || !destroy_func || !version_func) {
        error_message_ = "Invalid plugin DLL: " + dll_path + ", missing required export functions";
        FreeLibrary(handle);
        return nullptr;
    }

    // 检查API版本
    int api_version = version_func();
    if (api_version != PLUGIN_API_VERSION) {
        error_message_ = "Plugin API version mismatch: expected " + std::to_string(PLUGIN_API_VERSION) + 
                         ", got " + std::to_string(api_version);
        FreeLibrary(handle);
        return nullptr;
    }

    // 创建插件实例
    FrameProcessorPlugin* plugin = create_func();
    if (!plugin) {
        error_message_ = "Failed to create plugin instance";
        FreeLibrary(handle);
        return nullptr;
    }

    // 创建带自定义删除器的shared_ptr
    return std::shared_ptr<FrameProcessorPlugin>(plugin, PluginDeleter(handle, destroy_func));
}

std::vector<std::shared_ptr<FrameProcessorPlugin>> DllPluginLoader::load_plugins_from_directory(const std::string& directory) {
    std::vector<std::shared_ptr<FrameProcessorPlugin>> plugins;

    try {
        // 检查目录是否存在
        if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
            error_message_ = "Directory does not exist or is not a directory: " + directory;
            return plugins;
        }

        // 遍历目录中的所有DLL文件
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file() && entry.path().extension() == ".dll") {
                std::string dll_path = entry.path().string();
                std::cout << "Loading plugin from: " << dll_path << std::endl;

                auto plugin = load_plugin(dll_path);
                if (plugin) {
                    std::cout << "Successfully loaded plugin: " << plugin->get_name() << std::endl;
                    plugins.push_back(plugin);
                } else {
                    std::cout << "Failed to load plugin: " << error_message_ << std::endl;
                }
            }
        }
    } catch (const std::exception& e) {
        error_message_ = "Exception while loading plugins: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
    }

    return plugins;
}

bool DllPluginLoader::is_valid_plugin(HMODULE handle) {
    if (!handle) {
        error_message_ = "Invalid DLL handle";
        return false;
    }

    // 检查是否包含必要的导出函数
    if (!GetProcAddress(handle, "CreatePlugin")) {
        error_message_ = "Missing CreatePlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "DestroyPlugin")) {
        error_message_ = "Missing DestroyPlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "GetPluginApiVersion")) {
        error_message_ = "Missing GetPluginApiVersion export function";
        return false;
    }

    return true;
}

std::string DllPluginLoader::get_windows_error_message() {
    DWORD error_code = GetLastError();
    if (error_code == 0) {
        return "No error";
    }

    LPSTR message_buffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, error_code, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), (LPSTR)&message_buffer, 0, NULL);

    std::string message(message_buffer, size);
    LocalFree(message_buffer);

    return message;
}

std::string DllPluginLoader::get_error_message() const {
    return error_message_;
}

} // namespace plugins
} // namespace ai
