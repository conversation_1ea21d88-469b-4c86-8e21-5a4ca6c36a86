#pragma once

#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <opencv2/opencv.hpp>
#include <json/json.h>

#include "ai/plugins/task_plugin.h"
#include "tracking/byte_tracker.h"

// 定义导出宏
#ifdef BEHAVIOR_RETRIEVAL_PLUGIN_EXPORTS
#define BEHAVIOR_RETRIEVAL_PLUGIN_API __declspec(dllexport)
#else
#define BEHAVIOR_RETRIEVAL_PLUGIN_API __declspec(dllimport)
#endif

namespace ai {
namespace plugins {

/**
 * @brief 行为检索插件，提取目标类别名，按帧顺序排列，搜索指定步骤模式
 */
class BEHAVIOR_RETRIEVAL_PLUGIN_API BehaviorRetrievalPlugin : public TaskPlugin {
public:
    /**
     * @brief 构造函数
     */
    BehaviorRetrievalPlugin();

    /**
     * @brief 析构函数
     */
    ~BehaviorRetrievalPlugin() override = default;

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    bool initialize() override;

    /**
     * @brief 批量处理多帧任务
     * @param frames 输入/输出图像列表，包含当前帧及前序帧
     * @param tracks_list 跟踪结果列表，每个元素对应一帧的跟踪结果
     * @param result 处理结果，包含当前帧的处理结果
     * @return 是否处理成功
     */
    bool process_batch(const std::vector<cv::Mat>& frames,
                     const std::vector<std::vector<tracking::strack>>& tracks_list,
                     ai::FrameResult& result) override;                                         

    /**
     * @brief 重置插件状态
     */
    void reset() override;

    /**
     * @brief 获取插件需要处理的帧数
     * @return 需要处理的帧数
     */
    int get_required_frames() const override;

    /**
     * @brief 获取插件类型
     * @return 插件类型
     */
    std::string get_type() const override;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    std::string get_description() const override;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    std::string get_version() const override;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    std::string get_author() const override;

    /**
     * @brief 获取插件默认参数
     * @return 默认参数映射
     */
    std::map<std::string, std::string> get_default_params() const override;

private:
    /**
     * @brief 从参数加载插件配置
     * 重写TaskPlugin::load_parameters方法
     */
    void load_parameters() override;

    /**
     * @brief 创建渲染信息
     * @param frame 当前帧
     * @param tracks 当前帧的跟踪结果
     * @param should_render 是否需要渲染
     * @return 渲染信息
     */
    Json::Value create_render_info(const cv::Mat& frame,
                                  const std::vector<tracking::strack>& tracks,
                                  bool should_render);

private:
    int required_frames_;                  ///< 需要的帧数
    int frame_count_;                      ///< 帧计数
    std::vector<std::string> category_history_; ///< 类别历史记录
    std::string current_step_;             ///< 当前步骤
    std::string current_status_;           ///< 当前状态
    std::string last_detected_step_;       ///< 上一个检测到的步骤
    std::vector<std::string> completed_steps_; ///< 已完成的步骤
    std::vector<std::string> step_patterns_; ///< 步骤模式定义
    std::map<std::string, int> step_counts_; ///< 步骤计数器
    std::string current_executing_step_;   ///< 当前正在执行的步骤
    int render_interval_;                  ///< 渲染间隔
    int last_render_frame_;                ///< 上次渲染的帧
    bool debug_mode_;                      ///< 是否启用调试模式
    int max_history_length_;               ///< 历史记录最大长度

    int valid_action_num_;                ///< 判定有效动作的连续数量
    std::vector<std::string> valid_actions_;    ///< 有效动作列表
    std::vector<std::string> continue_actions_; ///< 连续动作列表

    std::map<std::string, int> action_start_frames_; ///< 记录每个动作的起始帧
    std::map<std::string, int> action_end_frames_; ///< 记录每个动作的结束帧
    double fps_; ///< 视频帧率，用于从帧数推算自然时间

    // 动作结构数据，包括动作名称和起止帧数
    struct ActionInfo {
        std::string action_name;
        int start_frame;
        int end_frame;
        std::string formatted_start_time;
        std::string formatted_end_time;
        double duration;  // 持续时间(秒)
    };
    std::vector<ActionInfo> valid_actions_info_;
    std::vector<ActionInfo> continue_actions_info_; 
    std::vector<ActionInfo> valid_continue_actions_info_; 
    ActionInfo current_frame_action_info_;
    std::string errMsg_;
    std::string expected_next_step_;
    int current_step_index_;
    int finished_cycle_num_;
    bool has_error_;

    std::string format_time(const int);
    ActionInfo new_action_info(const std::string& action_name);
    bool BehaviorRetrievalPlugin::update_valid_continue_actions_info_(const std::string& cation_name);
    bool BehaviorRetrievalPlugin::check_valid_continue_actions_info_();
    void BehaviorRetrievalPlugin::rollback_valid_continue_actions_info_(const int sequence_start_index, const int err_index);
};

} // namespace plugins
} // namespace ai

// 导出函数
extern "C" {
    /**
     * @brief 创建任务插件实例
     * @return 插件指针
     */
    BEHAVIOR_RETRIEVAL_PLUGIN_API ai::plugins::TaskPlugin* CreateTaskPlugin();

    /**
     * @brief 销毁任务插件实例
     * @param plugin 插件指针
     */
    BEHAVIOR_RETRIEVAL_PLUGIN_API void DestroyTaskPlugin(ai::plugins::TaskPlugin* plugin);

    /**
     * @brief 获取任务插件API版本
     * @return 插件API版本
     */
    BEHAVIOR_RETRIEVAL_PLUGIN_API int GetTaskPluginApiVersion();
}
