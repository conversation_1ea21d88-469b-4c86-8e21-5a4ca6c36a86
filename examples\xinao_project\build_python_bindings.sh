#!/bin/bash
# Xinao异常检测Python绑定构建脚本
# 使用方法: ./build_python_bindings.sh

echo "========================================"
echo "Xinao异常检测Python绑定构建脚本"
echo "========================================"

# 检查是否在正确的目录
if [ ! -f "python_bindings.cpp" ]; then
    echo "错误: 请在xinao_project目录下运行此脚本"
    exit 1
fi

# 进入项目根目录
cd ../..

# 检查构建目录
if [ ! -d "build" ]; then
    echo "创建构建目录..."
    mkdir build
fi

cd build

echo "正在配置CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "CMake配置失败"
    exit 1
fi

echo "正在编译项目..."
cmake --build . --config Release --target xinao_anomaly_detection

if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

echo "正在安装..."
cmake --install .

if [ $? -ne 0 ]; then
    echo "安装失败"
    exit 1
fi

echo "========================================"
echo "构建完成！"
echo "========================================"
echo "Python模块位置: bin/release/xinao_anomaly_detection.so"
echo ""
echo "测试方法:"
echo "1. 将 bin/release 目录添加到Python路径"
echo "2. 运行: python examples/xinao_project/test_python_bindings.py"
echo "========================================"
