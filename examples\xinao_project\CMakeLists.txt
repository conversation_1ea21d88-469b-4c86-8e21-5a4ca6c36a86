cmake_minimum_required(VERSION 3.19)
project(ProjectVideoExample)


# 收集C++源文件（排除Python绑定文件）
file(GLOB XINAO_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection_interface.cpp"
)

file(GLOB XINAO_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection_interface.h"
)

# 添加共享库
add_library(xinao_project SHARED ${XINAO_SOURCES} ${XINAO_HEADERS})

# 包含头文件目录
target_include_directories(xinao_project PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

# 链接库
target_link_libraries(xinao_project PRIVATE
    AiVideoCore
    AIDIOPENCV
)

# 定义导出宏
target_compile_definitions(xinao_project
    PRIVATE AIVIDEOCORE_EXPORTS
    PUBLIC AIVIDEOCORE_SHARED
)

# 创建Python绑定模块
pybind11_add_module(xinao_anomaly_detection
    "${CMAKE_CURRENT_SOURCE_DIR}/python_bindings.cpp"
)

# Python模块的包含目录
target_include_directories(xinao_anomaly_detection PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Python模块链接库
target_link_libraries(xinao_anomaly_detection PRIVATE
    xinao_project
    AiVideoCore
    AIDIOPENCV
)

# 设置Python模块属性
set_target_properties(xinao_anomaly_detection PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    VISIBILITY_INLINES_HIDDEN YES
)

# 安装目标
install(TARGETS xinao_project xinao_anomaly_detection
    RUNTIME DESTINATION release
    LIBRARY DESTINATION release
)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    add_custom_command(TARGET xinao_project POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:xinao_project>
    )
endif()
