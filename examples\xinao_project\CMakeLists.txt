cmake_minimum_required(VERSION 3.19)
project(ProjectVideoExample)


# 收集其他源文件
file(GLOB_RECURSE XINAO_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.h"
)
# 添加可执行文件
add_library(xinao_project SHARED ${XINAO_SOURCES})

# 包含头文件目录
target_include_directories(xinao_project PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

# 链接库
target_link_libraries(xinao_project PRIVATE
    AiVideoCore
    AIDIOPENCV
    pybind11
    python3
)

# 安装目标
install(TARGETS  xinao_project DESTINATION release)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    add_custom_command(TARGET xinao_project POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:xinao_project>
    )
endif()
