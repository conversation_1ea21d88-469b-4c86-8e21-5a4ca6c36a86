cmake_minimum_required(VERSION 3.19)
project(ProjectVideoExample)


# 收集C++源文件（排除Python绑定文件）
file(GLOB XINAO_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection_interface.cpp"
)

file(GLOB XINAO_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/anomaly_detection_interface.h"
)

# 添加共享库
add_library(xinao_project SHARED ${XINAO_SOURCES} ${XINAO_HEADERS})

# 包含头文件目录
target_include_directories(xinao_project PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

# 链接库 - 使用延迟加载避免Python循环依赖
target_link_libraries(xinao_project PRIVATE
    AIDIOPENCV
)

# 正常链接AiVideoCore
target_link_libraries(xinao_project PRIVATE
    AiVideoCore
)

# 定义导出宏
target_compile_definitions(xinao_project
    PRIVATE XINAO_PROJECT_EXPORTS
    PUBLIC XINAO_PROJECT_SHARED
)

# 创建Python绑定模块 - 使用pybind11::module而不是embed
pybind11_add_module(xinao_anomaly_detection
    "${CMAKE_CURRENT_SOURCE_DIR}/python_bindings.cpp"
)

# Python模块的包含目录
target_include_directories(xinao_anomaly_detection PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Python模块链接库 - 使用延迟加载避免循环依赖
target_link_libraries(xinao_anomaly_detection PRIVATE
    AIDIOPENCV
)

# 在Windows上使用延迟加载
if(WIN32)
    # 添加xinao_project的导入库，但使用延迟加载
    target_link_libraries(xinao_anomaly_detection PRIVATE
        xinao_project
    )
    target_link_options(xinao_anomaly_detection PRIVATE
        "/DELAYLOAD:xinao_project.dll"
        "/DELAYLOAD:AiVideoCore.dll"
    )
    target_link_libraries(xinao_anomaly_detection PRIVATE
        delayimp.lib
    )
else()
    target_link_libraries(xinao_anomaly_detection PRIVATE
        xinao_project
    )
endif()

# 定义导出宏 - Python模块需要导入符号
target_compile_definitions(xinao_anomaly_detection
    PRIVATE XINAO_PROJECT_SHARED
)

# 设置Python模块属性
set_target_properties(xinao_anomaly_detection PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    VISIBILITY_INLINES_HIDDEN YES
)

# 安装目标
install(TARGETS xinao_project xinao_anomaly_detection
    RUNTIME DESTINATION release
    LIBRARY DESTINATION release
)

# 安装时复制依赖的DLL文件
if(WIN32)
    # 安装AiVideoCore DLL
    install(FILES $<TARGET_FILE:AiVideoCore>
        DESTINATION release
    )

    # 安装OpenCV DLL
    if(TARGET AIDIOPENCV)
        install(FILES $<TARGET_FILE:AIDIOPENCV>
            DESTINATION release
        )
    endif()

    # 安装其他依赖项
    if(TARGET VisionFlow)
        install(FILES $<TARGET_FILE:VisionFlow>
            DESTINATION release
        )
    endif()

    if(TARGET AIDIJSONCPP)
        install(FILES $<TARGET_FILE:AIDIJSONCPP>
            DESTINATION release
        )
    endif()
endif()

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    # 为xinao_project复制依赖项
    add_custom_command(TARGET xinao_project POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:xinao_project>
    )

    # 为Python模块复制所有必要的DLL文件
    add_custom_command(TARGET xinao_anomaly_detection POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:xinao_anomaly_detection>
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:xinao_project>
            $<TARGET_FILE_DIR:xinao_anomaly_detection>
        COMMENT "Copying DLL dependencies for Python module"
    )

    # 复制OpenCV DLL
    if(TARGET AIDIOPENCV)
        add_custom_command(TARGET xinao_anomaly_detection POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                $<TARGET_FILE:AIDIOPENCV>
                $<TARGET_FILE_DIR:xinao_anomaly_detection>
            COMMENT "Copying OpenCV DLL"
        )
    endif()

    # 复制其他重要的依赖项
    if(TARGET VisionFlow)
        add_custom_command(TARGET xinao_anomaly_detection POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                $<TARGET_FILE:VisionFlow>
                $<TARGET_FILE_DIR:xinao_anomaly_detection>
            COMMENT "Copying VisionFlow DLL"
        )
    endif()

    if(TARGET AIDIJSONCPP)
        add_custom_command(TARGET xinao_anomaly_detection POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                $<TARGET_FILE:AIDIJSONCPP>
                $<TARGET_FILE_DIR:xinao_anomaly_detection>
            COMMENT "Copying JsonCpp DLL"
        )
    endif()
endif()
