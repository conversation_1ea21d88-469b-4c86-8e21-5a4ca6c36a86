#include "ai/plugins/frame_processors/motion_blur_processor.h"
#include <iostream>

namespace ai {
namespace plugins {
namespace frame_processors {

MotionBlurProcessor::MotionBlurProcessor()
    : FrameProcessorPlugin("motion_blur"), alpha_(50), frames_to_blend_(3) {
    // 默认启用插件
    set_enabled(true);

    // 设置默认参数
    params_["alpha"] = "50";
    params_["frames_to_blend"] = "3";
}

bool MotionBlurProcessor::initialize() {
    try {
        // 从参数中读取混合权重
        if (params_.find("alpha") != params_.end()) {
            alpha_ = std::stoi(params_["alpha"]);
            // 确保alpha在0-100范围内
            alpha_ = std::max(0, std::min(100, alpha_));
        }

        // 从参数中读取要混合的帧数
        if (params_.find("frames_to_blend") != params_.end()) {
            frames_to_blend_ = std::stoi(params_["frames_to_blend"]);
            // 确保frames_to_blend至少为2
            frames_to_blend_ = std::max(2, frames_to_blend_);
        }

        return true;
    } catch (const std::exception& e) {
        error_message_ = "初始化失败: " + std::string(e.what());
        return false;
    }
}

cv::Mat MotionBlurProcessor::process_frames(const std::vector<cv::Mat>& frames) {
    if (frames.empty()) {
        error_message_ = "没有输入帧";
        return cv::Mat();
    }

    try {
        // 如果只有一帧，直接返回
        if (frames.size() == 1) {
            return frames[0].clone();
        }

        // 确定要使用的帧数
        int num_frames = std::min(static_cast<int>(frames.size()), frames_to_blend_);

        // 创建结果图像
        cv::Mat result = frames[0].clone();

        // 计算混合权重
        float alpha = alpha_ / 100.0f;

        // 混合多帧
        for (int i = 1; i < num_frames; ++i) {
            // 确保帧大小一致
            if (frames[i].size() != result.size()) {
                cv::Mat resized;
                cv::resize(frames[i], resized, result.size());
                cv::addWeighted(result, 1.0 - alpha, resized, alpha, 0, result);
            } else {
                cv::addWeighted(result, 1.0 - alpha, frames[i], alpha, 0, result);
            }
        }

        return result;
    } catch (const std::exception& e) {
        error_message_ = "处理帧失败: " + std::string(e.what());
        return frames[0].clone();
    }
}

int MotionBlurProcessor::get_required_frames() const {
    return frames_to_blend_;
}

std::string MotionBlurProcessor::get_description() const {
    return "运动模糊帧处理器，用于在多帧之间创建运动模糊效果";
}

std::string MotionBlurProcessor::get_version() const {
    return "1.0.0";
}

std::string MotionBlurProcessor::get_author() const {
    return "AiVideo Team";
}

} // namespace frame_processors
} // namespace plugins
} // namespace ai
