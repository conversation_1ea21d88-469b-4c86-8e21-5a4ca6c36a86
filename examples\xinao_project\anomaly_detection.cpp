#include "anomaly_detection.h"

AnomalyDetection::AnomalyDetection(std::string projectPath) {
    this->projectPath = projectPath;
}

/**
 * 将文件夹中的图像保存成视频
 */
void AnomalyDetection::encode_images_to_video(const std::string& image_folder, const std::string& output_video, bool keep_images) {
    // 获取文件夹中的所有图像文件
    std::vector<std::string> image_files;
    for (const auto& entry : std::filesystem::directory_iterator(image_folder)) {
        if (entry.is_regular_file() && entry.path().extension() == ".jpg") {
            image_files.push_back(entry.path().string());
        }
    }
    // 按文件名排序
    std::sort(image_files.begin(), image_files.end());

    // 创建视频写入对象
    cv::VideoWriter video_writer;
    int frame_count = 0;
    for (const auto& image_file : image_files) {
        // 读取图像，图像路径为image_folder + "/" + image_file
        cv::Mat frame = cv::imread(image_file);
        if (frame.empty()) {
            std::cerr << "无法读取图像: " << image_file << std::endl;
            continue;
        }

        if (!video_writer.isOpened()) {
            video_writer.open(output_video, cv::VideoWriter::fourcc('H', '2', '6', '4'), 25, frame.size());
            if (!video_writer.isOpened()) {
                std::cerr << "无法创建视频文件: " << output_video << std::endl;
                return;
            }
        }
        video_writer.write(frame);

        // 显示进度
        frame_count ++;
        std::cout << "正在处理图像: " << image_file << " (" 
                  << (static_cast<float>(frame_count) / image_files.size() * 100) << "%)" << std::endl;
    }
    video_writer.release();

    if (keep_images) {
        std::cout << "所有图像已编码成视频: " << output_video << std::endl;
    } else {
        // 删除图像文件
        for (const auto& image_file : image_files) {
            std::filesystem::remove(image_file);
        }
        std::cout << "所有图像已编码成视频并删除原图像: " << output_video << std::endl;
    }
}

bool AnomalyDetection::anomaly_detection(std::string videoPath, std::string savePath, bool keep_images) {
    bool liveMode = false; // 是否为实时模式
    
    // 如果savePath文件夹不存在，则创建该文件夹
    if (!std::filesystem::exists(savePath)) {
        std::filesystem::create_directories(savePath);
    }

    // 从videoPath中获取视频文件名，不包含路径
    std::string videoFileName = std::filesystem::path(videoPath).filename().string();
    int NG_frame_threshold = 3; // 异常帧数阈值，超过该值则认为检测到异常

    try {
        // 初始化VisionFlow环境（需要替换为有效的许可证ID和服务器地址）
        core::VideoProcessingCore::initialize_visionflow("9733c801000702014f0d000200130023", "192.168.0.169");

        std::string projectPath = this->projectPath;

        std::cout << "正在打开项目: " << projectPath << std::endl;

        // 获取ProjectManager单例
        core::ProjectManager& projectManager = core::ProjectManager::get_instance();

        // 打开现有项目
        std::shared_ptr<core::Project> project = projectManager.open_project(projectPath);
        if (!project) {
            std::cerr << "无法打开项目: " << projectPath << std::endl;
            return 1;
        }

        std::cout << "项目名称: " << project->get_name() << std::endl;
        // std::cout << "视频路径: " << project->get_video_path() << std::endl;
        std::cout << "视频路径: " << videoPath << std::endl;

        // 创建VideoProcessingCore实例
        std::shared_ptr<core::VideoProcessingCore> processor = std::make_shared<core::VideoProcessingCore>();

        // 将项目配置导入到VideoProcessingCore
        if (!projectManager.import_to_video_processing_core(project, processor)) {
            std::cerr << "导入项目配置失败" << std::endl;
            return 1;
        }

        // 打开视频

        if (!processor->open_video_file(videoPath)) {
            std::cerr << "无法打开视频: " << videoPath << std::endl;
            return 1;
        }

        // 创建插件渲染器
        utils::PluginRenderer renderer;

        // 获取视频信息
        int frameCount = processor->get_total_frames();
        int fps = processor->get_fps();

        std::cout << "视频总帧数: " << frameCount << std::endl;
        std::cout << "视频帧率: " << fps << std::endl;

        // 创建窗口
        if (liveMode) {
            cv::namedWindow("处理后视频", cv::WINDOW_NORMAL);
        }

        // 处理视频
        bool isNG = false; // 是否检测到异常
        int NG_frame_count = 0;
        while (true) {
            try {
                // 获取当前帧索引
                int currentFrame = processor->get_current_frame();

                // 处理当前帧（启用插件）
                auto result = processor->process_frame(currentFrame);
                if (result.detection_tracks.size() > 0) {
                    NG_frame_count ++;
                }

                // 获取处理后的帧
                cv::Mat processedFrame = result.processed_frame;

                // 渲染结果
                renderer.render_frame_result(processedFrame, result);

                // 显示处理后的帧
                if (liveMode) {
                    cv::imshow("处理后视频", processedFrame);
                }
                
                // 把processedFrame保存至存储路径文件夹，每张图像的文件名为六个数字的当前帧索引
                std::string frameFileName = savePath + "/" + 
                                            std::to_string(currentFrame).insert(0, 6 - std::to_string(currentFrame).length(), '0') + 
                                            ".jpg";
                cv::imwrite(frameFileName, processedFrame);
                std::cout << "保存帧: " << frameFileName << std::endl;

                // 显示当前帧信息
                // currentFrame = processor->get_current_frame();
                std::cout << "/n处理进度: " << currentFrame << "/" << frameCount
                          << " (" << (currentFrame * 100 / frameCount) << "%)" << std::endl;

                // 等待按键，按ESC键退出
                int key = cv::waitKey(30);
                if (key == 27) { // ESC键
                    break;
                }

                // 读取下一帧
                // processor->process_next_frame(false);

            } catch (const std::exception& e) {
                std::cerr << "/n处理异常: " << e.what() << std::endl;
                break;
            }
        }

        std::cout << std::endl;

        // 关闭视频和窗口
        processor->close_video();
        cv::destroyAllWindows();

        // 将处理器的最终配置导出到项目中
        // projectManager.export_from_video_processing_core(processor, project);

        // 保存项目的最终状态
        // projectManager.save_project(project);

        // 把存储路径中的所有图像编码成视频
        if (NG_frame_count > NG_frame_threshold) {
            isNG = true;
            std::cout << "检测到异常，异常帧数: " << NG_frame_count << std::endl;
        } else {
            std::cout << "未检测到异常" << std::endl;
        }

        std::string saveVideoPath = savePath + "/" + videoFileName;
        saveVideoPath = std::filesystem::path(saveVideoPath).replace_extension("").string();  // 去掉原视频的扩展名
        // 如果是异常视频，则在文件名后添加"_NG"，否则添加"_OK"
        if (isNG) {
            saveVideoPath = saveVideoPath + "_NG.mp4";
        } else {
            saveVideoPath = saveVideoPath + "_OK.mp4";
        }
        
        encode_images_to_video(savePath, saveVideoPath, keep_images);

        std::cout << "视频处理完成，项目已保存" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return false;
    }

    return true;
}
