#pragma once

#include <memory>
#include <string>
#include <vector>
#include "core/project.h"
#include "core/video_processing_core.h"
#include "../aivideocore_export.h"

namespace core {

/**
 * @brief 项目管理器类，负责项目的创建、打开、保存等操作
 */
class AIVIDEOCORE_API ProjectManager {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static ProjectManager& get_instance();

    /**
     * @brief 创建新项目
     * @return 新项目指针
     */
    std::shared_ptr<Project> create_project();

    /**
     * @brief 打开项目
     * @param filePath 项目文件路径
     * @return 项目指针，如果打开失败则返回nullptr
     */
    std::shared_ptr<Project> open_project(const std::string& filePath);

    /**
     * @brief 保存项目
     * @param project 项目指针
     * @param filePath 项目文件路径，如果为空则使用项目当前路径
     * @return 是否保存成功
     */
    bool save_project(std::shared_ptr<Project> project, const std::string& filePath = "");

    /**
     * @brief 获取当前项目
     * @return 当前项目指针
     */
    std::shared_ptr<Project> get_current_project() const;

    /**
     * @brief 设置当前项目
     * @param project 项目指针
     */
    void set_current_project(std::shared_ptr<Project> project);

    /**
     * @brief 从VideoProcessingCore导出项目设置
     * @param videoProcessingCore VideoProcessingCore指针
     * @param project 项目指针
     * @return 是否导出成功
     */
    bool export_from_video_processing_core(std::shared_ptr<VideoProcessingCore> videoProcessingCore, std::shared_ptr<Project> project);

    /**
     * @brief 导入项目设置到VideoProcessingCore
     * @param project 项目指针
     * @param videoProcessingCore VideoProcessingCore指针
     * @param plugin_directory 可选参数，任务插件的目录路径，如果提供则自动加载插件并设置参数
     * @return 是否导入成功
     */
    bool import_to_video_processing_core(std::shared_ptr<Project> project,
                                      std::shared_ptr<VideoProcessingCore> videoProcessingCore,
                                      const std::string& plugin_directory = "plugins/task");

    /**
     * @brief 获取最近打开的项目列表
     * @return 项目文件路径列表
     */
    std::vector<std::string> get_recent_projects() const;

    /**
     * @brief 添加项目到最近打开列表
     * @param filePath 项目文件路径
     */
    void add_to_recent_projects(const std::string& filePath);

    /**
     * @brief 清空最近打开的项目列表
     */
    void clear_recent_projects();

private:
    /**
     * @brief 构造函数
     */
    ProjectManager();

    /**
     * @brief 析构函数
     */
    ~ProjectManager();

    /**
     * @brief 复制构造函数（禁用）
     */
    ProjectManager(const ProjectManager&) = delete;

    /**
     * @brief 赋值运算符（禁用）
     */
    ProjectManager& operator=(const ProjectManager&) = delete;

    /**
     * @brief 加载最近打开的项目列表
     */
    void load_recent_projects();

    /**
     * @brief 保存最近打开的项目列表
     */
    void save_recent_projects();

private:
    std::shared_ptr<Project> current_project_;  ///< 当前项目
    std::vector<std::string> recent_projects_;  ///< 最近打开的项目列表
    static constexpr int MAX_RECENT_PROJECTS = 10;  ///< 最近打开项目的最大数量
};

} // namespace core
