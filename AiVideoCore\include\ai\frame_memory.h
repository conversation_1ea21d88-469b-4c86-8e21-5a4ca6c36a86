#pragma once

#include <opencv2/opencv.hpp>

#include <deque>
#include <memory>
#include <vector>

#include "tracking/strack.h"
#include "aivideocore_export.h"

namespace ai {

/**
 * @brief 帧内存类，用于管理多帧图像和跟踪结果的累积
 */
class AIVIDEOCORE_API FrameMemory {
public:
    /**
     * @brief 构造函数
     * @param max_history 最大历史帧数
     */
    FrameMemory(int max_history = 30);

    /**
     * @brief 析构函数
     */
    ~FrameMemory();

    /**
     * @brief 添加新帧和跟踪结果
     * @param frame 新帧
     * @param tracks 新帧的跟踪结果
     */
    void add_frame(const cv::Mat& frame, const std::vector<tracking::strack>& tracks = {});

    /**
     * @brief 添加跟踪结果
     * @param tracks 跟踪结果
     */
    void add_tracks(const std::vector<tracking::strack>& tracks);

    /**
     * @brief 获取当前跟踪结果数量
     * @return 当前跟踪结果数量
     */
    size_t get_track_count() const;

    /**
     * @brief 获取指定数量的历史帧
     * @param count 需要的帧数
     * @return 历史帧列表，最新的帧在前
     */
    std::vector<cv::Mat> get_frames(int count) const;

    /**
     * @brief 获取指定数量的历史跟踪结果
     * @param count 需要的跟踪结果数量
     * @return 历史跟踪结果列表，最新的结果在前
     */
    std::vector<std::vector<tracking::strack>> get_tracks(int count) const;

    /**
     * @brief 获取所有历史帧
     * @return 所有历史帧列表，最新的帧在前
     */
    const std::deque<cv::Mat>& get_all_frames() const;

    /**
     * @brief 获取所有历史跟踪结果
     * @return 所有历史跟踪结果列表，最新的结果在前
     */
    const std::deque<std::vector<tracking::strack>>& get_all_tracks() const;

    /**
     * @brief 清空历史
     */
    void clear();

    /**
     * @brief 设置最大历史帧数
     * @param count 最大历史帧数
     */
    void set_max_history(int count);

    /**
     * @brief 获取最大历史帧数
     * @return 最大历史帧数
     */
    int get_max_history() const;

    /**
     * @brief 获取当前历史帧数
     * @return 当前历史帧数
     */
    size_t get_frame_count() const;

    /**
     * @brief 检查帧和跟踪结果数量是否匹配
     * @return 是否匹配
     */
    bool is_matched() const;

private:
    int max_history_;                                  ///< 最大历史帧数
    std::deque<cv::Mat> frame_history_;                ///< 帧历史缓存
    std::deque<std::vector<tracking::strack>> track_history_;  ///< 跟踪结果历史缓存
};

} // namespace ai
