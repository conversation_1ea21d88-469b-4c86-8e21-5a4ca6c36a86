#include "utils/text_utils.h"
#include <opencv2/imgproc.hpp>

namespace utils {

void putTextZH(cv::Mat& img, 
               const std::string& text, 
               cv::Point pos, 
               double fontSize, 
               const cv::Scalar& color,
               int thickness) {
    // If text is empty, return
    if (text.empty()) return;

    // Fallback to OpenCV's putText for non-Chinese text
    cv::putText(img, text, pos, cv::FONT_HERSHEY_SIMPLEX, 
                fontSize, color, thickness);
}

} // namespace utils