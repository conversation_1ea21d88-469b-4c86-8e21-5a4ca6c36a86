#include "utils/settings_manager.h"
#include "utils/log_manager.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <set>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#else
#include <unistd.h>
#include <pwd.h>
#endif

namespace utils {

// SettingsValue 实现
bool SettingsValue::toBool() const {
    if (std::holds_alternative<bool>(value_)) {
        return std::get<bool>(value_);
    }
    if (std::holds_alternative<std::string>(value_)) {
        const std::string& str = std::get<std::string>(value_);
        return str == "true" || str == "1" || str == "yes";
    }
    if (std::holds_alternative<int>(value_)) {
        return std::get<int>(value_) != 0;
    }
    return false;
}

int SettingsValue::toInt() const {
    if (std::holds_alternative<int>(value_)) {
        return std::get<int>(value_);
    }
    if (std::holds_alternative<bool>(value_)) {
        return std::get<bool>(value_) ? 1 : 0;
    }
    if (std::holds_alternative<double>(value_)) {
        return static_cast<int>(std::get<double>(value_));
    }
    if (std::holds_alternative<std::string>(value_)) {
        try {
            return std::stoi(std::get<std::string>(value_));
        } catch (...) {
            return 0;
        }
    }
    return 0;
}

double SettingsValue::toDouble() const {
    if (std::holds_alternative<double>(value_)) {
        return std::get<double>(value_);
    }
    if (std::holds_alternative<int>(value_)) {
        return static_cast<double>(std::get<int>(value_));
    }
    if (std::holds_alternative<bool>(value_)) {
        return std::get<bool>(value_) ? 1.0 : 0.0;
    }
    if (std::holds_alternative<std::string>(value_)) {
        try {
            return std::stod(std::get<std::string>(value_));
        } catch (...) {
            return 0.0;
        }
    }
    return 0.0;
}

std::string SettingsValue::toString() const {
    if (std::holds_alternative<std::string>(value_)) {
        return std::get<std::string>(value_);
    }
    if (std::holds_alternative<bool>(value_)) {
        return std::get<bool>(value_) ? "true" : "false";
    }
    if (std::holds_alternative<int>(value_)) {
        return std::to_string(std::get<int>(value_));
    }
    if (std::holds_alternative<double>(value_)) {
        return std::to_string(std::get<double>(value_));
    }
    return "";
}

const char* SettingsValue::typeName() const {
    if (std::holds_alternative<bool>(value_)) return "bool";
    if (std::holds_alternative<int>(value_)) return "int";
    if (std::holds_alternative<double>(value_)) return "double";
    if (std::holds_alternative<std::string>(value_)) return "string";
    return "unknown";
}

// IniParser 类实现
class SettingsManager::IniParser {
public:
    IniParser(const std::string& filename) : filename_(filename) {
        load();
    }

    void load() {
        data_.clear();
        std::ifstream file(filename_);
        if (!file.is_open()) {
            return;
        }

        std::string line;
        std::string current_section;

        while (std::getline(file, line)) {
            // 去除首尾空白
            line.erase(0, line.find_first_not_of(" \t\r\n"));
            line.erase(line.find_last_not_of(" \t\r\n") + 1);

            // 跳过空行和注释
            if (line.empty() || line[0] == ';' || line[0] == '#') {
                continue;
            }

            // 处理节
            if (line[0] == '[' && line.back() == ']') {
                current_section = line.substr(1, line.length() - 2);
                continue;
            }

            // 处理键值对
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);

                // 去除键值的首尾空白
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t") + 1);

                std::string full_key = current_section.empty() ? key : current_section + "/" + key;
                data_[full_key] = value;
            }
        }
    }

    void save() {
        // 确保目录存在
        std::filesystem::path file_path(filename_);
        std::filesystem::path dir_path = file_path.parent_path();
        if (!dir_path.empty() && !std::filesystem::exists(dir_path)) {
            std::filesystem::create_directories(dir_path);
        }

        std::ofstream file(filename_);
        if (!file.is_open()) {
            return;
        }

        // 按节组织数据
        std::map<std::string, std::map<std::string, std::string>> sections;

        for (const auto& [full_key, value] : data_) {
            size_t pos = full_key.find('/');
            if (pos != std::string::npos) {
                std::string section = full_key.substr(0, pos);
                std::string key = full_key.substr(pos + 1);
                sections[section][key] = value;
            } else {
                sections[""][full_key] = value;
            }
        }

        // 写入文件
        for (const auto& [section_name, section_data] : sections) {
            if (!section_name.empty()) {
                file << "[" << section_name << "]\n";
            }

            for (const auto& [key, value] : section_data) {
                file << key << "=" << value << "\n";
            }

            if (!section_name.empty()) {
                file << "\n";
            }
        }
    }

    SettingsValue getValue(const std::string& key, const SettingsValue& defaultValue) const {
        auto it = data_.find(key);
        if (it != data_.end()) {
            return SettingsValue(it->second);
        }
        return defaultValue;
    }

    void setValue(const std::string& key, const SettingsValue& value) {
        data_[key] = value.toString();
    }

    bool contains(const std::string& key) const {
        return data_.find(key) != data_.end();
    }

    void remove(const std::string& key) {
        data_.erase(key);
    }

    std::vector<std::string> getKeys(const std::string& prefix) const {
        std::vector<std::string> keys;
        for (const auto& [key, value] : data_) {
            if (key.find(prefix) == 0) {
                std::string relative_key = key.substr(prefix.length());
                if (relative_key.find('/') == std::string::npos) {
                    keys.push_back(relative_key);
                }
            }
        }
        return keys;
    }

    std::vector<std::string> getGroups(const std::string& prefix) const {
        std::set<std::string> groups_set;
        for (const auto& [key, value] : data_) {
            if (key.find(prefix) == 0) {
                std::string relative_key = key.substr(prefix.length());
                size_t pos = relative_key.find('/');
                if (pos != std::string::npos) {
                    groups_set.insert(relative_key.substr(0, pos));
                }
            }
        }
        return std::vector<std::string>(groups_set.begin(), groups_set.end());
    }

private:
    std::string filename_;
    std::map<std::string, std::string> data_;
};

SettingsManager& SettingsManager::get_instance() {
    static SettingsManager instance;
    return instance;
}

// 获取应用数据目录的辅助函数
std::string get_app_data_directory() {
#ifdef _WIN32
    wchar_t* path = nullptr;
    if (SUCCEEDED(SHGetKnownFolderPath(FOLDERID_RoamingAppData, 0, nullptr, &path))) {
        std::wstring wpath(path);
        CoTaskMemFree(path);

        // 转换为 UTF-8
        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wpath.c_str(), -1, nullptr, 0, nullptr, nullptr);
        std::string result(size_needed - 1, 0);
        WideCharToMultiByte(CP_UTF8, 0, wpath.c_str(), -1, &result[0], size_needed, nullptr, nullptr);
        return result;
    }
    return "";
#else
    const char* home = getenv("HOME");
    if (home) {
        return std::string(home) + "/.config";
    }
    return "";
#endif
}

SettingsManager::SettingsManager() {
}

SettingsManager::~SettingsManager() {
}

void SettingsManager::initialize(const std::string& organization, const std::string& application) {
    if (initialized_) {
        return;
    }

    // 获取应用数据目录
    std::string app_data_dir = get_app_data_directory();
    if (app_data_dir.empty()) {
        LOG_ERROR("Failed to get application data directory");
        return;
    }

    // 构建配置文件路径
    std::filesystem::path config_dir(app_data_dir);
    config_dir = config_dir / organization / application;

    // 确保目录存在
    if (!std::filesystem::exists(config_dir)) {
        std::filesystem::create_directories(config_dir);
    }

    // 设置配置文件路径
    config_path_ = (config_dir / (application + ".ini")).string();

    // 创建解析器
    parser_ = std::make_unique<IniParser>(config_path_);

    LOG_DEBUG("Settings initialized at: " + config_path_);

    // 从注册表迁移设置
    migrateFromRegistry(organization, application);

    initialized_ = true;
}

SettingsValue SettingsManager::value(const std::string& key, const SettingsValue& defaultValue) const {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return defaultValue;
    }

    std::string full_key = current_group_.empty() ? key : current_group_ + "/" + key;
    return parser_->getValue(full_key, defaultValue);
}

void SettingsManager::setValue(const std::string& key, const SettingsValue& value) {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return;
    }

    std::string full_key = current_group_.empty() ? key : current_group_ + "/" + key;
    parser_->setValue(full_key, value);
    parser_->save();
}

void SettingsManager::beginGroup(const std::string& prefix) {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return;
    }

    if (current_group_.empty()) {
        current_group_ = prefix;
    } else {
        current_group_ += "/" + prefix;
    }
}

void SettingsManager::endGroup() {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return;
    }

    size_t pos = current_group_.find_last_of('/');
    if (pos != std::string::npos) {
        current_group_ = current_group_.substr(0, pos);
    } else {
        current_group_.clear();
    }
}

std::vector<std::string> SettingsManager::childKeys() const {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return {};
    }

    std::string prefix = current_group_.empty() ? "" : current_group_ + "/";
    return parser_->getKeys(prefix);
}

std::vector<std::string> SettingsManager::childGroups() const {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return {};
    }

    std::string prefix = current_group_.empty() ? "" : current_group_ + "/";
    return parser_->getGroups(prefix);
}

bool SettingsManager::contains(const std::string& key) const {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return false;
    }

    std::string full_key = current_group_.empty() ? key : current_group_ + "/" + key;
    return parser_->contains(full_key);
}

void SettingsManager::remove(const std::string& key) {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return;
    }

    std::string full_key = current_group_.empty() ? key : current_group_ + "/" + key;
    parser_->remove(full_key);
    parser_->save();
}

std::string SettingsManager::fileName() const {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return "";
    }
    return config_path_;
}

void SettingsManager::sync() {
    if (!initialized_ || !parser_) {
        LOG_DEBUG("SettingsManager not initialized");
        return;
    }
    LOG_DEBUG("Syncing settings to disk...");
    parser_->save();
}

void SettingsManager::migrateFromRegistry(const std::string& organization, const std::string& application) {
    // 注册表迁移功能简化实现
    // 在实际应用中，如果需要从注册表迁移设置，可以在这里实现
    // 目前只记录日志，表示不需要迁移
    LOG_DEBUG("Registry migration not implemented in standard library version");

#ifdef _WIN32
    // 在 Windows 平台上，可以在这里添加注册表读取代码
    // 但为了简化，我们暂时跳过这个功能
    LOG_DEBUG("Windows registry migration skipped for now");
#endif
}

} // namespace utils
