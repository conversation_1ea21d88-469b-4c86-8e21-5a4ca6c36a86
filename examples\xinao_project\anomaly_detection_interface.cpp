#include "anomaly_detection_interface.h"
#include "anomaly_detection.h"

AnomalyDetectionInterface::AnomalyDetectionInterface(const char* projectPath) {
    // 创建AnomalyDetection实例
    interface_client = new AnomalyDetection(projectPath);
}

bool AnomalyDetectionInterface::infer(const char* videoPath, const char* savePath, bool keep_images) {
    std::string videoPathStr = std::string(videoPath);
    std::string savePathStr = std::string(savePath);
    // 调用AnomalyDetection的anomaly_detection方法
    return interface_client->anomaly_detection(videoPathStr, savePathStr, keep_images);
}

AnomalyDetectionInterface::~AnomalyDetectionInterface(){
    // 释放AnomalyDetection实例
    delete interface_client;
}