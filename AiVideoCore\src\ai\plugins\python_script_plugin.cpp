#include "ai/plugins/python_script_plugin.h"

#include <Python.h>
#include <json/json.h>
#include <json/value.h>
#include <pybind11/embed.h>
#include <pybind11/stl.h>

#include <fstream>
#include <iostream>
#include <sstream>

// 定义日志宏，如果没有定义LOG_DEBUG
#ifndef LOG_DEBUG
#define LOG_DEBUG(message) std::cout << "[DEBUG] " << __FILE__ << ":" << __LINE__ << " - " << message << std::endl
#endif

namespace py = pybind11;

class PyStdOutRedirect {
public:
    void write(const std::string& str) {
        std::cout << str << std::flush;
    }

    void flush() {
        std::cout.flush();
    }
};

// 在 Python 中注册重定向类
PYBIND11_EMBEDDED_MODULE(stdout_redirect, m) {
    py::class_<PyStdOutRedirect>(m, "StdOutRedirect")
        .def(py::init<>())
        .def("write", &PyStdOutRedirect::write)
        .def("flush", &PyStdOutRedirect::flush);
}

namespace ai {
namespace plugins {

// Python 脚本插件实现
PythonScriptPlugin::PythonScriptPlugin(const std::string& script_path)
    : TaskPlugin(script_path), script_path_(script_path), is_initialized_(false) {
    get_required_frames_func_ = py::none();  // 初始化为空

    // 初始化文件时间戳
    if (std::filesystem::exists(script_path_)) {
        last_file_time_ = std::filesystem::last_write_time(script_path_);
    }
}

PythonScriptPlugin::~PythonScriptPlugin() {
    // 释放 Python 对象
    initialize_func_ = py::none();
    process_func_ = py::none();
    reset_func_ = py::none();
    get_info_func_ = py::none();
    get_required_frames_func_ = py::none();
    script_module_ = py::none();

    // 注意：我们不在析构函数中关闭 Python 解释器
    // 因为这可能导致崩溃
}

bool PythonScriptPlugin::initialize() {
    if (is_initialized_) {
        return true;
    }

    // 初始化 Python 解释器
    try {
        if (!Py_IsInitialized()) {
            // 使用 Python C API 初始化解释器
            Py_Initialize();

            if (!Py_IsInitialized()) {
                error_message_ = "Failed to initialize Python interpreter";
                return false;
            }
        }
    } catch (const std::exception& e) {
        error_message_ = "Failed to initialize Python interpreter: " + std::string(e.what());
        return false;
    }

    if (!load_script()) {
        return false;
    }

    // 调用脚本的初始化函数
    try {
        if (!initialize_func_.is_none()) {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            py::dict py_params;  // 创建参数字典

            // 打印调试信息
            std::cout << "Initializing script with parameters:" << std::endl;
            for (const auto& [key, value] : params_) {
                std::cout << "  " << key << ": " << value << std::endl;
                py_params[py::str(key)] = py::str(value);
            }

            try {
                // 检查initialize函数是否存在且可调用
                if (!py::hasattr(script_module_, "initialize")) {
                    throw std::runtime_error("initialize function not found in script");
                }

                initialize_func_ = script_module_.attr("initialize");

                // 检查是否为可调用对象
                if (!py::isinstance<py::function>(initialize_func_)) {
                    throw std::runtime_error("initialize is not a callable function");
                }

                // 调用initialize函数
                initialize_func_(py_params);

                std::cout << "Script initialized successfully with " << params_.size() << " parameters" << std::endl;
            }
            catch (const py::error_already_set& e) {
                error_message_ = "Python initialize error: " + std::string(e.what());
                std::cout << error_message_ << std::endl;
                return false;
            }
        }
        is_initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        error_message_ = "Failed to initialize script: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
        return false;
    }
}

bool PythonScriptPlugin::process(cv::Mat& frame,
                               const std::vector<tracking::strack>& tracks,
                               FrameResult& result) {
    if (!is_enabled()) {
        std::cout << "Plugin " << get_name() << " is not enabled" << std::endl;
        return false;
    }

    // 调用多帧接口
    std::vector<cv::Mat> frames = {frame};
    std::vector<std::vector<tracking::strack>> tracks_list = {tracks};
    bool success = process_batch(frames, tracks_list, result);
    if (success) {
        frame = result.processed_frame;
    }
    return success;
}

bool PythonScriptPlugin::process_batch(const std::vector<cv::Mat>& frames,
                                     const std::vector<std::vector<tracking::strack>>& tracks_list,
                                     ai::FrameResult& result) {
    if (!is_enabled()) {
        std::cout << "Plugin " << get_name() << " is not enabled" << std::endl;
        return false;
    }

    if (!is_initialized_) {
        std::cout << "Plugin " << get_name() << " is not initialized" << std::endl;
        return false;
    }

    if (process_func_.is_none()) {
        std::cout << "Plugin " << get_name() << " process function is none" << std::endl;
        // 打印Python堆栈信息
        try {
            py::gil_scoped_acquire gil;
            py::module traceback = py::module::import("traceback");
            py::module sys = py::module::import("sys");
            traceback.attr("print_stack")();
            std::cout << "Current Python module attributes:" << std::endl;
            py::print(script_module_.attr("__dict__"));
        } catch (const std::exception& e) {
            std::cout << "Error printing stack trace: " << e.what() << std::endl;
        }
        return false;
    }

    try {
        py::gil_scoped_acquire gil;  // 确保获取GIL

        // 获取脚本需要的帧数
        const int required_frames = get_required_frames();

        // 限制处理帧数，避免处理过多帧
        const size_t max_frames_to_process = std::min(frames.size(), static_cast<size_t>(required_frames));

        // 创建帧列表和跟踪结果列表
        py::list py_frames;
        py::list py_tracks_list;

        // 转换每一帧和对应的跟踪结果，只处理最多 max_frames_to_process 帧
        for (size_t i = 0; i < max_frames_to_process; ++i) {
            py_frames.append(mat_to_numpy(frames[i]));

            // 确保 tracks_list 的大小与 frames 相同
            if (i < tracks_list.size()) {
                py_tracks_list.append(tracks_to_python(tracks_list[i]));
            } else {
                // 如果没有对应的跟踪结果，添加空列表
                py_tracks_list.append(py::list());
            }
        }

        // 创建一个作用域来控制临时对象的生命周期
        {
            // 安全调用处理函数
            py::object result_obj = process_func_(py_frames, py_tracks_list);
            if (result_obj.is_none()) {
                throw std::runtime_error("Process function returned None");
            }

            // 检查返回值是否是列表
            if (!py::isinstance<py::list>(result_obj)) {
                throw std::runtime_error("Process function must return a list of results");
            }

            // 转换返回的结果列表
            py::list py_results = result_obj.cast<py::list>();

            // 检查返回的结果列表中是否有元素
            if (py_results.size() > 0) {
                // 只处理第一个结果（当前帧）
                py::dict py_result = py_results[0].cast<py::dict>();

                // 转换输出数据
                python_to_result(py_result, result);

                // 处理processed_frame（如果存在）
                if (py_result.contains("processed_frame")) {
                    py::array_t<uint8_t> py_processed_frame = py_result["processed_frame"].cast<py::array_t<uint8_t>>();
                    if (py_processed_frame.size() > 0) {
                        cv::Mat processed = numpy_to_mat(py_processed_frame);
                        if (!processed.empty() && processed.size() == frames[0].size()) {
                            // 直接使用处理后的帧，避免不必要的复制
                            result.processed_frame = processed;
                        }
                    }
                }
            } else {
                // 如果没有结果，创建一个空的结果
                result.task_type = get_name();
                result.frame_id = 0;
                result.ext_info = "no_result";
            }
        }  // 作用域结束，临时Python对象在这里被释放

        return true;
    } catch (const std::exception& e) {
        error_message_ = "Failed to process frames: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
        return false;
    }
}

void PythonScriptPlugin::reset() {
    try {
        // 保存当前参数，以便在重新加载后应用
        std::map<std::string, std::string> current_params = params_;

        // 检查文件是否被修改
        bool file_modified = false;
        if (std::filesystem::exists(script_path_)) {
            auto current_file_time = std::filesystem::last_write_time(script_path_);
            file_modified = current_file_time != last_file_time_;
        }

        // 如果文件被修改，优先使用importlib.reload重新加载脚本
        if (file_modified) {
            LOG_DEBUG("File has been modified, reloading script: " + script_path_);

            // 重新加载脚本
            if (reload_script()) {
                // 重新应用参数
                set_params(current_params);
                LOG_DEBUG("Python script reloaded successfully after file modification: " + script_path_);
                return;
            } else {
                error_message_ = "Failed to reload modified script: " + error_message_;
                LOG_DEBUG(error_message_);
            }
        }

        // 如果文件未修改或重新加载失败，则尝试调用脚本的reset函数
        if (is_initialized_ && !reset_func_.is_none()) {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            if (py::isinstance<py::function>(reset_func_)) {
                try {
                    // 调用脚本的reset函数
                    reset_func_();
                    LOG_DEBUG("Python script reset function called: " + script_path_);

                    // 重新应用参数，确保reset后参数仍然有效
                    if (!initialize_func_.is_none() && py::isinstance<py::function>(initialize_func_)) {
                        py::dict py_params;
                        for (const auto& [key, value] : current_params) {
                            py_params[py::str(key)] = py::str(value);
                        }
                        initialize_func_(py_params);
                        LOG_DEBUG("Parameters reapplied after reset");
                    }

                    return;
                } catch (const py::error_already_set& e) {
                    error_message_ = "Error calling reset function: " + std::string(e.what());
                    LOG_DEBUG(error_message_);
                }
            }
        }

        // 如果上述方法都失败，尝试完全重新加载脚本
        LOG_DEBUG("Attempting full script reload as fallback");
        if (reload_script()) {
            // 重新应用参数
            set_params(current_params);
            LOG_DEBUG("Python script fully reloaded as fallback: " + script_path_);
        } else {
            error_message_ = "Failed to reset or reload script: " + error_message_;
            LOG_DEBUG(error_message_);
        }
    } catch (const std::exception& e) {
        error_message_ = "Failed to reset script: " + std::string(e.what());
        LOG_DEBUG(error_message_);
    }
}

std::string PythonScriptPlugin::get_type() const {
    if (!is_initialized_ || get_info_func_.is_none()) {
        return "python_script";
    }

    try {
        py::gil_scoped_acquire gil;  // 获取GIL
        py::object result = get_info_func_();
        if (!result.is_none()) {
            py::dict info = result.cast<py::dict>();
            if (info.contains("type")) {
                return info["type"].cast<std::string>();
            }
        }
    } catch (const std::exception& e) {
        // 忽略错误
    }

    return "python_script";
}

std::string PythonScriptPlugin::get_description() const {
    if (!is_initialized_ || get_info_func_.is_none()) {
        return "Python script plugin";
    }

    try {
        py::gil_scoped_acquire gil;  // 获取GIL
        py::object result = get_info_func_();
        if (!result.is_none()) {
            py::dict info = result.cast<py::dict>();
            if (info.contains("description")) {
                return info["description"].cast<std::string>();
            }
        }
    } catch (const std::exception& e) {
        // 忽略错误
    }

    return "Python script plugin";
}

std::string PythonScriptPlugin::get_version() const {
    if (!is_initialized_ || get_info_func_.is_none()) {
        return "1.0.0";
    }

    try {
        py::gil_scoped_acquire gil;  // 获取GIL
        py::object result = get_info_func_();
        if (!result.is_none()) {
            py::dict info = result.cast<py::dict>();
            if (info.contains("version")) {
                return info["version"].cast<std::string>();
            }
        }
    } catch (const std::exception& e) {
        // 忽略错误
    }

    return "1.0.0";
}

std::string PythonScriptPlugin::get_author() const {
    if (!is_initialized_ || get_info_func_.is_none()) {
        return "Unknown";
    }

    try {
        py::gil_scoped_acquire gil;  // 获取GIL
        py::object result = get_info_func_();
        if (!result.is_none()) {
            py::dict info = result.cast<py::dict>();
            if (info.contains("author")) {
                return info["author"].cast<std::string>();
            }
        }
    } catch (const std::exception& e) {
        // 忽略错误
    }

    return "Unknown";
}


std::string PythonScriptPlugin::get_display_name() const {
    std::string name;

    // 尝试从get_info获取name
    if (!is_initialized_ || get_info_func_.is_none()) {
        name = std::filesystem::path(script_path_).stem().string();
    } else {
        try {
            py::gil_scoped_acquire gil;  // 获取GIL
            py::object result = get_info_func_();
            if (!result.is_none()) {
                py::dict info = result.cast<py::dict>();
                if (info.contains("name")) {
                    name = info["name"].cast<std::string>();
                } else {
                    // 如果没有name字段，使用文件名
                    name = std::filesystem::path(script_path_).stem().string();
                }
            }
        } catch (const std::exception& e) {
            // 发生错误时使用文件名
            name = std::filesystem::path(script_path_).stem().string();
        }
    }

    // 添加(Python)后缀
    return name + " (Python)";
}

bool PythonScriptPlugin::set_script_path(const std::string& path) {
    // 保存旧路径
    std::string old_path = script_path_;

    // 设置新路径
    script_path_ = path;

    // 重新加载脚本
    bool result = reload_script();

    return result;
}

std::string PythonScriptPlugin::get_script_path() const {
    return script_path_;
}

bool PythonScriptPlugin::reload_script() {
    try {
        // 检查文件是否存在
        if (!std::filesystem::exists(script_path_)) {
            error_message_ = "Script file does not exist: " + script_path_;
            return false;
        }

        // 检查文件是否被修改
        auto current_file_time = std::filesystem::last_write_time(script_path_);
        bool file_modified = current_file_time != last_file_time_;

        // 获取脚本目录和模块名
        std::string script_dir = std::filesystem::path(script_path_).parent_path().string();
        std::string module_name = std::filesystem::path(script_path_).stem().string();

        py::gil_scoped_acquire gil;  // 确保获取GIL

        // 如果模块已经加载，使用importlib.reload重新加载
        if (!script_module_.is_none() && file_modified) {
            LOG_DEBUG("Reloading modified Python script: " + script_path_);

            try {
                // 导入importlib模块
                py::module importlib = py::module::import("importlib");

                // 使用importlib.reload重新加载模块
                script_module_ = importlib.attr("reload")(script_module_);

                // 更新文件时间戳
                last_file_time_ = current_file_time;

                // 重新获取函数引用
                if (!script_module_.attr("process").is_none()) {
                    process_func_ = script_module_.attr("process");
                } else {
                    error_message_ = "Script must define a 'process' function";
                    return false;
                }

                // 获取可选的函数
                if (!script_module_.attr("initialize").is_none()) {
                    initialize_func_ = script_module_.attr("initialize");
                } else {
                    initialize_func_ = py::none();
                }

                if (!script_module_.attr("reset").is_none()) {
                    reset_func_ = script_module_.attr("reset");
                } else {
                    reset_func_ = py::none();
                }

                if (!script_module_.attr("get_info").is_none()) {
                    get_info_func_ = script_module_.attr("get_info");
                } else {
                    get_info_func_ = py::none();
                }

                // 检查是否实现了get_required_frames函数
                if (py::hasattr(script_module_, "get_required_frames")) {
                    get_required_frames_func_ = script_module_.attr("get_required_frames");
                } else {
                    get_required_frames_func_ = py::none();
                }

                // 提取脚本中的默认参数和参数描述
                extract_default_params();

                LOG_DEBUG("Python script reloaded successfully using importlib.reload: " + script_path_);
                return true;
            } catch (const py::error_already_set& e) {
                error_message_ = "Failed to reload module with importlib.reload: " + std::string(e.what());
                LOG_DEBUG(error_message_);

                // 如果使用importlib.reload失败，回退到完全重新加载
                LOG_DEBUG("Falling back to full module reload");
            }
        }

        // 如果模块未加载或importlib.reload失败，则完全重新加载
        // 释放旧的 Python 对象
        initialize_func_ = py::none();
        process_func_ = py::none();
        reset_func_ = py::none();
        get_info_func_ = py::none();
        get_required_frames_func_ = py::none();
        script_module_ = py::none();

        is_initialized_ = false;
        error_message_.clear();

        return load_script() && initialize();
    } catch (const std::exception& e) {
        error_message_ = "Failed to reload script: " + std::string(e.what());
        LOG_DEBUG(error_message_);
        return false;
    }
}

std::string PythonScriptPlugin::get_error_message() const {
    return error_message_;
}

void PythonScriptPlugin::set_script_params(const std::map<std::string, std::string>& params) {
    // 调用基类的set_params方法
    set_params(params);
}

std::map<std::string, std::string> PythonScriptPlugin::get_script_params() const {
    // 调用基类的get_params方法
    return get_params();
}

std::map<std::string, std::string> PythonScriptPlugin::get_default_params() const {
    return default_params_;
}

void PythonScriptPlugin::load_parameters() {
    // 如果插件已初始化，则应用参数
    if (is_initialized_ && !initialize_func_.is_none()) {
        try {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            py::dict py_params;  // 创建参数字典
            for (const auto& [key, value] : params_) {
                py_params[py::str(key)] = py::str(value);
            }
            initialize_func_(py_params);
        } catch (const std::exception& e) {
            error_message_ = "Failed to apply parameters: " + std::string(e.what());
            std::cout << error_message_ << std::endl;
        }
    }
}

std::map<std::string, std::string> PythonScriptPlugin::get_param_descriptions() const {
    return param_descriptions_;
}

void PythonScriptPlugin::extract_default_params() {
    // 清空默认参数和参数描述
    default_params_.clear();
    param_descriptions_.clear();

    try {
        py::gil_scoped_acquire gil;  // 确保获取GIL

        // 检查脚本模块是否定义了DEFAULT_PARAMS字典
        if (py::hasattr(script_module_, "DEFAULT_PARAMS")) {
            py::dict default_params = script_module_.attr("DEFAULT_PARAMS").cast<py::dict>();
            for (auto item : default_params) {
                std::string key = item.first.cast<std::string>();

                // 如果值是字典，则包含默认值和描述
                if (py::isinstance<py::dict>(item.second)) {
                    py::dict param_info = item.second.cast<py::dict>();

                    // 提取默认值
                    if (param_info.contains("default")) {
                        py::object default_value = param_info["default"];
                        if (py::isinstance<py::str>(default_value)) {
                            default_params_[key] = default_value.cast<std::string>();
                        } else if (py::isinstance<py::int_>(default_value)) {
                            default_params_[key] = std::to_string(default_value.cast<int>());
                        } else if (py::isinstance<py::float_>(default_value)) {
                            default_params_[key] = std::to_string(default_value.cast<double>());
                        } else if (py::isinstance<py::bool_>(default_value)) {
                            default_params_[key] = default_value.cast<bool>() ? "true" : "false";
                        } else {
                            // 尝试转换为字符串
                            try {
                                default_params_[key] = py::str(default_value).cast<std::string>();
                            } catch (const std::exception& e) {
                                std::cout << "Failed to convert default value for " << key << ": " << e.what() << std::endl;
                                default_params_[key] = "";
                            }
                        }
                    }

                    // 提取描述
                    if (param_info.contains("description")) {
                        param_descriptions_[key] = param_info["description"].cast<std::string>();
                    }
                } else {
                    // 如果值不是字典，则直接作为默认值
                    try {
                        if (py::isinstance<py::str>(item.second)) {
                            default_params_[key] = item.second.cast<std::string>();
                        } else if (py::isinstance<py::int_>(item.second)) {
                            default_params_[key] = std::to_string(item.second.cast<int>());
                        } else if (py::isinstance<py::float_>(item.second)) {
                            default_params_[key] = std::to_string(item.second.cast<double>());
                        } else if (py::isinstance<py::bool_>(item.second)) {
                            default_params_[key] = item.second.cast<bool>() ? "true" : "false";
                        } else {
                            // 尝试转换为字符串
                            default_params_[key] = py::str(item.second).cast<std::string>();
                        }
                    } catch (const std::exception& e) {
                        std::cout << "Failed to convert default value for " << key << ": " << e.what() << std::endl;
                        default_params_[key] = "";
                    }
                }
            }
        }

        // 检查脚本模块是否定义了PARAM_DESCRIPTIONS字典
        if (py::hasattr(script_module_, "PARAM_DESCRIPTIONS")) {
            py::dict descriptions = script_module_.attr("PARAM_DESCRIPTIONS").cast<py::dict>();
            for (auto item : descriptions) {
                std::string key = item.first.cast<std::string>();
                param_descriptions_[key] = item.second.cast<std::string>();
            }
        }

        // 从脚本注释中提取参数信息
        if (py::hasattr(script_module_, "__doc__") && !script_module_.attr("__doc__").is_none()) {
            std::string doc_string = script_module_.attr("__doc__").cast<std::string>();
            extract_params_from_docstring(doc_string);
        }

        // 从脚本中提取全局变量作为默认参数
        // 常见的参数变量名
        std::vector<std::string> common_param_vars = {
            "required_frames", "count_line_y", "threshold", "alpha", "mode"
        };

        for (const auto& var_name : common_param_vars) {
            try {
                // 检查脚本模块是否有该属性
                if (py::hasattr(script_module_, var_name.c_str())) {
                    py::object var_value = script_module_.attr(var_name.c_str());
                    if (py::isinstance<py::int_>(var_value)) {
                        default_params_[var_name] = std::to_string(var_value.cast<int>());
                    } else if (py::isinstance<py::float_>(var_value)) {
                        default_params_[var_name] = std::to_string(var_value.cast<double>());
                    } else if (py::isinstance<py::str>(var_value)) {
                        default_params_[var_name] = var_value.cast<std::string>();
                    } else if (py::isinstance<py::bool_>(var_value)) {
                        default_params_[var_name] = var_value.cast<bool>() ? "true" : "false";
                    }
                }
            } catch (const std::exception& e) {
                std::cout << "Failed to extract global variable " << var_name << ": " << e.what() << std::endl;
            }
        }

        // 打印提取到的默认参数和描述
        std::cout << "Extracted default parameters:" << std::endl;
        for (const auto& [key, value] : default_params_) {
            std::cout << "  " << key << ": " << value << std::endl;
        }

        std::cout << "Extracted parameter descriptions:" << std::endl;
        for (const auto& [key, value] : param_descriptions_) {
            std::cout << "  " << key << ": " << value << std::endl;
        }

    } catch (const std::exception& e) {
        std::cout << "Error extracting default parameters: " << e.what() << std::endl;
    }
}

void PythonScriptPlugin::extract_params_from_docstring(const std::string& doc_string) {
    // 从文档字符串中提取参数信息
    // 常见的模式是参数名后跟着冒号或等号，然后是描述或默认值

    std::istringstream iss(doc_string);
    std::string line;
    bool in_params_section = false;

    while (std::getline(iss, line)) {
        // 去除前后空白
        line = trim(line);

        // 检查是否进入参数部分
        if (line.find("参数") != std::string::npos ||
            line.find("Parameters") != std::string::npos ||
            line.find("params") != std::string::npos) {
            in_params_section = true;
            continue;
        }

        // 如果在参数部分，则处理每一行
        if (in_params_section && !line.empty()) {
            // 如果行以连字符或数字开头，可能是新的参数
            if (std::isalpha(line[0]) || line[0] == '_' || std::isdigit(line[0])) {
                // 尝试提取参数名和描述
                size_t colon_pos = line.find(':');
                size_t equal_pos = line.find('=');
                size_t dash_pos = line.find('-');
                size_t split_pos = std::string::npos;

                if (colon_pos != std::string::npos) {
                    split_pos = colon_pos;
                } else if (equal_pos != std::string::npos) {
                    split_pos = equal_pos;
                } else if (dash_pos != std::string::npos && dash_pos > 0 && dash_pos < line.size() - 1) {
                    split_pos = dash_pos;
                }

                if (split_pos != std::string::npos) {
                    std::string param_name = trim(line.substr(0, split_pos));
                    std::string param_desc = trim(line.substr(split_pos + 1));

                    // 检查是否包含默认值
                    size_t default_pos = param_desc.find("default");
                    if (default_pos != std::string::npos) {
                        size_t value_start = param_desc.find_first_of("=:", default_pos);
                        if (value_start != std::string::npos) {
                            value_start = param_desc.find_first_not_of(" \t", value_start + 1);
                            if (value_start != std::string::npos) {
                                size_t value_end = param_desc.find_first_of(",;)", value_start);
                                if (value_end == std::string::npos) {
                                    value_end = param_desc.length();
                                }
                                std::string default_value = trim(param_desc.substr(value_start, value_end - value_start));

                                // 如果值包含引号，去除引号
                                if (default_value.front() == '\'' && default_value.back() == '\'' ||
                                    default_value.front() == '"' && default_value.back() == '"') {
                                    default_value = default_value.substr(1, default_value.length() - 2);
                                }

                                default_params_[param_name] = default_value;
                            }
                        }
                    }

                    // 添加参数描述
                    param_descriptions_[param_name] = param_desc;
                }
            } else if (line.find("---") != std::string::npos || line.empty()) {
                // 遇到分隔符或空行，可能离开了参数部分
                in_params_section = false;
            }
        }
    }
}

std::string PythonScriptPlugin::trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r\f\v");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(first, last - first + 1);
}

int PythonScriptPlugin::get_required_frames() const {
    // 默认值为5
    int required_frames = 5;

    // 如果脚本实现了get_required_frames函数，则调用它
    if (is_initialized_ && !get_required_frames_func_.is_none()) {
        try {
            py::gil_scoped_acquire gil;  // 确保获取GIL
            py::object result = get_required_frames_func_();
            if (!result.is_none()) {
                // 尝试将结果转换为整数
                required_frames = result.cast<int>();

                // 限制帧数在合理范围内
                if (required_frames < 1) {
                    required_frames = 1;
                } else if (required_frames > 100) {
                    required_frames = 100;
                }
            }
        } catch (const std::exception& e) {
            // 如果出错，使用默认值
            std::cout << "Error getting required frame count: " << e.what() << std::endl;
        }
    }

    return required_frames;
}

bool PythonScriptPlugin::load_script() {
    if (script_path_.empty()) {
        error_message_ = "Script path is empty";
        return false;
    }

    if (!std::filesystem::exists(script_path_)) {
        error_message_ = "Script file does not exist: " + script_path_;
        return false;
    }

    try {
        // 获取脚本目录
        std::string script_dir = std::filesystem::path(script_path_).parent_path().string();

        // 导入 sys 模块并添加脚本目录到 sys.path
        py::gil_scoped_acquire gil;

        // 重定向标准输出
        py::module sys = py::module_::import("sys");
        py::module stdout_redirect = py::module_::import("stdout_redirect");
        py::object redirector = stdout_redirect.attr("StdOutRedirect")();
        sys.attr("stdout") = redirector;

        // 添加脚本目录到 sys.path
        py::list sys_path = sys.attr("path").cast<py::list>();
        bool path_exists = false;
        for (const auto& path : sys_path) {
            if (path.cast<std::string>() == script_dir) {
                path_exists = true;
                break;
            }
        }

        if (!path_exists) {
            sys_path.append(script_dir);
        }

        // 导入脚本模块
        std::string module_name = std::filesystem::path(script_path_).stem().string();
        py::print(" import module:", module_name);
        try {
            // 检查模块是否已经在sys.modules中
            py::dict modules = sys.attr("modules").cast<py::dict>();
            bool module_exists = false;

            if (modules.contains(module_name.c_str())) {
                module_exists = true;
                LOG_DEBUG("Module already exists in sys.modules, will use importlib.reload");
            }

            if (module_exists) {
                // 如果模块已存在，使用importlib.reload重新加载
                py::module importlib = py::module::import("importlib");
                script_module_ = modules[module_name.c_str()];
                script_module_ = importlib.attr("reload")(script_module_);
                LOG_DEBUG("Reloaded existing module: " + module_name);
            } else {
                // 如果模块不存在，正常导入
                script_module_ = py::module_::import(module_name.c_str());
                LOG_DEBUG("Imported new module: " + module_name);
            }

            // 获取必需的函数
            if (!script_module_.attr("process").is_none()) {
                process_func_ = script_module_.attr("process");
            } else {
                error_message_ = "Script must define a 'process' function";
                return false;
            }

            // 获取可选的函数
            if (!script_module_.attr("initialize").is_none()) {
                initialize_func_ = script_module_.attr("initialize");
            }

            if (!script_module_.attr("reset").is_none()) {
                reset_func_ = script_module_.attr("reset");
            }

            if (!script_module_.attr("get_info").is_none()) {
                get_info_func_ = script_module_.attr("get_info");
            }

            // 检查是否实现了get_required_frames函数
            if (py::hasattr(script_module_, "get_required_frames")) {
                get_required_frames_func_ = script_module_.attr("get_required_frames");
            } else {
                get_required_frames_func_ = py::none();
            }

            // 提取脚本中的默认参数和参数描述
            extract_default_params();

            // 更新文件时间戳
            last_file_time_ = std::filesystem::last_write_time(script_path_);
            LOG_DEBUG("Updated script file timestamp for: " + script_path_);

        } catch (const py::error_already_set& e) {
            error_message_ = "Import error: " + std::string(e.what());
            std::cout << "Error at import: " << error_message_ << std::endl;
            LOG_DEBUG("Python Import Error: " + error_message_);
            return false;
        }

        return true;
    } catch (const py::error_already_set& e) {
        error_message_ = "Python error: " + std::string(e.what());
        std::cout << "Error at load_script: " << error_message_ << std::endl;
        return false;
    } catch (const std::exception& e) {
        error_message_ = "Failed to load script: " + std::string(e.what());
        std::cout << "Error at load_script: " << error_message_ << std::endl;
        return false;
    }
}

bool PythonScriptPlugin::is_valid_script() const {
    return !script_module_.is_none() && !process_func_.is_none();
}

py::array_t<uint8_t> PythonScriptPlugin::mat_to_numpy(const cv::Mat& mat) const {
    if (mat.empty()) {
        throw std::runtime_error("Input matrix is empty");
    }

    py::gil_scoped_acquire gil;

    // 确保图像连续
    cv::Mat continuous = mat.isContinuous() ? mat : mat.clone();

    std::vector<std::ptrdiff_t> shape;
    std::vector<std::ptrdiff_t> strides;

    if (continuous.channels() == 1) {
        shape = {continuous.rows, continuous.cols};
        strides = {static_cast<std::ptrdiff_t>(continuous.step[0]), static_cast<std::ptrdiff_t>(continuous.step[1])};
    } else {
        shape = {continuous.rows, continuous.cols, continuous.channels()};
        strides = {static_cast<std::ptrdiff_t>(continuous.step[0]), static_cast<std::ptrdiff_t>(continuous.step[1]), static_cast<std::ptrdiff_t>(continuous.elemSize1())};
    }

    // 使用零复制模式，直接引用原始数据
    // 创建一个管理数据生命周期的capsule
    auto cleanup = [](void* data) {}; // 空函数，因为我们不需要释放内存

    return py::array_t<uint8_t>(
        shape,
        strides,
        continuous.data,
        py::capsule(continuous.data, cleanup)
    );
}

cv::Mat PythonScriptPlugin::numpy_to_mat(const py::array_t<uint8_t>& array) const {
    py::buffer_info info = array.request();
    cv::Mat temp;

    // 创建临时Mat对象
    if (info.ndim == 2) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC1, info.ptr);
    } else if (info.ndim == 3 && info.shape[2] == 3) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC3, info.ptr);
    } else if (info.ndim == 3 && info.shape[2] == 4) {
        temp = cv::Mat(info.shape[0], info.shape[1], CV_8UC4, info.ptr);
    } else {
        throw std::runtime_error("Unsupported array shape");
    }

    // 始终进行深拷贝
    return temp.clone();
}

py::list PythonScriptPlugin::tracks_to_python(const std::vector<tracking::strack>& tracks) const {
    py::gil_scoped_acquire gil;  // 获取GIL

    try {
        py::list py_tracks;

        for (const auto& track : tracks) {
            py::dict py_track;
            // 使用 py::cast 进行安全的类型转换
            py_track["track_id"] = py::cast(track.track_id);
            py_track["detect_class"] = py::cast(track.detect_class);
            py_track["score"] = py::cast(track.score);
            py_track["state"] = py::cast(track.state);

            py::dict py_tlwh;
            py_tlwh["x"] = py::cast(track.tlwh.x);
            py_tlwh["y"] = py::cast(track.tlwh.y);
            py_tlwh["width"] = py::cast(track.tlwh.width);
            py_tlwh["height"] = py::cast(track.tlwh.height);

            py_track["tlwh"] = py_tlwh;
            py_tracks.append(py_track);
        }

        return py_tracks;
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to convert tracks to Python: " + std::string(e.what()));
    }
}

void PythonScriptPlugin::python_to_result(const py::dict& py_result, FrameResult& result) {
    // 设置任务类型
    if (py_result.contains("task_type")) {
        result.task_type = py_result["task_type"].cast<std::string>();
    } else {
        result.task_type = "python_script";
    }

    // 设置帧 ID
    if (py_result.contains("frame_id")) {
        result.frame_id = py_result["frame_id"].cast<int>();
    }

    // 设置类别计数
    if (py_result.contains("class_counts")) {
        py::dict py_class_counts = py_result["class_counts"].cast<py::dict>();
        for (auto item : py_class_counts) {
            std::string key = item.first.cast<std::string>();
            int value = item.second.cast<int>();
            result.class_counts[key] = value;
        }
    }

    // 设置总计数
    if (py_result.contains("total_count")) {
        result.total_count = py_result["total_count"].cast<int>();
    }

    // 设置自定义数据
    if (py_result.contains("ext_info")) {
        py::dict py_ext_info = py_result["ext_info"].cast<py::dict>();

        // 将自定义数据转换为JSON字符串存储在ext_info中
        Json::Value custom_json;

        for (auto item : py_ext_info) {
            std::string key = item.first.cast<std::string>();

            // 根据类型转换
            try {
                if (py::isinstance<py::int_>(item.second)) {
                    custom_json[key] = item.second.cast<int>();
                } else if (py::isinstance<py::float_>(item.second)) {
                    custom_json[key] = item.second.cast<double>();
                } else if (py::isinstance<py::str>(item.second)) {
                    custom_json[key] = item.second.cast<std::string>();
                } else if (py::isinstance<py::bool_>(item.second)) {
                    custom_json[key] = item.second.cast<bool>();
                } else if (py::isinstance<py::dict>(item.second)) {
                    // 处理嵌套字典，如render_info
                    custom_json[key] = python_dict_to_json(item.second.cast<py::dict>());
                } else if (py::isinstance<py::list>(item.second)) {
                    // 处理列表
                    custom_json[key] = python_list_to_json(item.second.cast<py::list>());
                }
            } catch (const std::exception& e) {
                // 忽略转换错误
                std::cout << "Error converting custom data: " << e.what() << std::endl;
            }
        }

        // 将JSON对象转换为字符串
        Json::FastWriter writer;
        result.ext_info = writer.write(custom_json);
    }
}

// 将Python字典转换为Json::Value
Json::Value PythonScriptPlugin::python_dict_to_json(const py::dict& dict) {
    Json::Value json;

    for (auto item : dict) {
        std::string key = item.first.cast<std::string>();

        try {
            if (py::isinstance<py::int_>(item.second)) {
                json[key] = item.second.cast<int>();
            } else if (py::isinstance<py::float_>(item.second)) {
                json[key] = item.second.cast<double>();
            } else if (py::isinstance<py::str>(item.second)) {
                json[key] = item.second.cast<std::string>();
            } else if (py::isinstance<py::bool_>(item.second)) {
                json[key] = item.second.cast<bool>();
            } else if (py::isinstance<py::dict>(item.second)) {
                // 递归处理嵌套字典
                json[key] = python_dict_to_json(item.second.cast<py::dict>());
            } else if (py::isinstance<py::list>(item.second)) {
                // 处理列表
                json[key] = python_list_to_json(item.second.cast<py::list>());
            } else if (item.second.is_none()) {
                // 处理None值
                // json[key] = Json::Value::null;
            }
        } catch (const std::exception& e) {
            std::cout << "Error converting dict item: " << key << ", " << e.what() << std::endl;
            // 当转换失败时，使用null值
            // json[key] = Json::Value::null;
        }
    }

    return json;
}

// 将Python列表转换为Json::Value
Json::Value PythonScriptPlugin::python_list_to_json(const py::list& list) {
    Json::Value json(Json::arrayValue);

    for (size_t i = 0; i < py::len(list); ++i) {
        try {
            py::object item = list[i];

            if (py::isinstance<py::int_>(item)) {
                json.append(item.cast<int>());
            } else if (py::isinstance<py::float_>(item)) {
                json.append(item.cast<double>());
            } else if (py::isinstance<py::str>(item)) {
                json.append(item.cast<std::string>());
            } else if (py::isinstance<py::bool_>(item)) {
                json.append(item.cast<bool>());
            } else if (py::isinstance<py::dict>(item)) {
                // 递归处理嵌套字典
                json.append(python_dict_to_json(item.cast<py::dict>()));
            } else if (py::isinstance<py::list>(item)) {
                // 递归处理嵌套列表
                json.append(python_list_to_json(item.cast<py::list>()));
            } else if (item.is_none()) {
                // 处理None值
                // json.append(Json::Value::null);
            }
        } catch (const std::exception& e) {
            std::cout << "Error converting list item at index " << i << ": " << e.what() << std::endl;
            // 当转换失败时，使用null值
            // json.append(Json::Value::null);
        }
    }

    return json;
}

} // namespace plugins
} // namespace ai




























































