#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DLL问题快速修复脚本
自动检测和修复Python模块的DLL依赖问题
"""

import os
import sys
import shutil
import subprocess
import platform

def find_files(directory, pattern):
    """在目录中查找匹配模式的文件"""
    found_files = []
    if os.path.exists(directory):
        for root, dirs, files in os.walk(directory):
            for file in files:
                if pattern.lower() in file.lower():
                    found_files.append(os.path.join(root, file))
    return found_files

def copy_file_safe(src, dst_dir):
    """安全复制文件"""
    try:
        if not os.path.exists(dst_dir):
            os.makedirs(dst_dir)
        
        dst_file = os.path.join(dst_dir, os.path.basename(src))
        shutil.copy2(src, dst_file)
        print(f"✓ 复制: {os.path.basename(src)}")
        return True
    except Exception as e:
        print(f"✗ 复制失败 {os.path.basename(src)}: {e}")
        return False

def fix_dll_dependencies():
    """修复DLL依赖问题"""
    print("=" * 60)
    print("DLL依赖问题自动修复")
    print("=" * 60)
    
    # 确定目标目录
    target_dir = os.path.abspath("../../bin/release")
    build_dir = os.path.abspath("../../build")
    
    print(f"目标目录: {target_dir}")
    print(f"构建目录: {build_dir}")
    
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"创建目标目录: {target_dir}")
    
    success_count = 0
    total_count = 0
    
    # 1. 复制Python模块
    print("\n1. 查找并复制Python模块...")
    pyd_files = find_files(build_dir, "xinao_anomaly_detection.pyd")
    for pyd_file in pyd_files:
        total_count += 1
        if copy_file_safe(pyd_file, target_dir):
            success_count += 1
    
    # 2. 复制主要DLL文件
    print("\n2. 查找并复制主要DLL文件...")
    main_dlls = [
        "AiVideoCore.dll",
        "xinao_project.dll"
    ]
    
    for dll_name in main_dlls:
        dll_files = find_files(build_dir, dll_name)
        if dll_files:
            total_count += 1
            if copy_file_safe(dll_files[0], target_dir):
                success_count += 1
        else:
            print(f"✗ 未找到: {dll_name}")
    
    # 3. 复制OpenCV DLL
    print("\n3. 查找并复制OpenCV DLL...")
    opencv_files = find_files(build_dir, "opencv_world")
    for opencv_file in opencv_files:
        if opencv_file.endswith('.dll'):
            total_count += 1
            if copy_file_safe(opencv_file, target_dir):
                success_count += 1
    
    # 4. 复制其他依赖项
    print("\n4. 查找并复制其他依赖项...")
    other_dlls = [
        "jsoncpp",
        "boost",
        "visionflow"
    ]
    
    for dll_pattern in other_dlls:
        dll_files = find_files(build_dir, dll_pattern)
        for dll_file in dll_files:
            if dll_file.endswith('.dll'):
                total_count += 1
                if copy_file_safe(dll_file, target_dir):
                    success_count += 1
    
    # 5. 从系统目录复制运行时库
    print("\n5. 检查运行时库...")
    runtime_dlls = [
        "msvcp140.dll",
        "vcruntime140.dll", 
        "vcruntime140_1.dll"
    ]
    
    for runtime_dll in runtime_dlls:
        try:
            result = subprocess.run(
                ["where", runtime_dll],
                capture_output=True,
                text=True,
                check=True
            )
            dll_path = result.stdout.strip().split('\n')[0]
            total_count += 1
            if copy_file_safe(dll_path, target_dir):
                success_count += 1
        except subprocess.CalledProcessError:
            print(f"✗ 系统中未找到: {runtime_dll}")
    
    print(f"\n复制完成: {success_count}/{total_count} 个文件")
    return success_count > 0

def test_import():
    """测试模块导入"""
    print("\n" + "=" * 60)
    print("测试模块导入")
    print("=" * 60)
    
    target_dir = os.path.abspath("../../bin/release")
    
    # 添加目标目录到Python路径
    if target_dir not in sys.path:
        sys.path.insert(0, target_dir)
    
    # 切换到目标目录（确保DLL能被找到）
    original_cwd = os.getcwd()
    try:
        os.chdir(target_dir)
        
        # 尝试导入模块
        import xinao_anomaly_detection
        print("✓ 模块导入成功!")
        
        # 测试基本功能
        print(f"模块版本: {getattr(xinao_anomaly_detection, '__version__', '未知')}")
        
        # 尝试创建实例
        try:
            detector = xinao_anomaly_detection.AnomalyDetectionInterface("dummy")
            print("✓ 类实例化成功!")
            return True
        except Exception as e:
            print(f"✗ 类实例化失败: {e}")
            print("注意: 这可能是因为项目文件不存在，但模块本身工作正常")
            return True
            
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 未知错误: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def provide_manual_steps():
    """提供手动修复步骤"""
    print("\n" + "=" * 60)
    print("手动修复步骤")
    print("=" * 60)
    
    print("如果自动修复失败，请尝试以下手动步骤:")
    print()
    print("1. 重新编译项目:")
    print("   cd ../../build")
    print("   cmake --build . --config Release")
    print()
    print("2. 检查Python版本:")
    print("   python --version")
    print("   (必须是Python 3.12)")
    print()
    print("3. 安装Visual C++ Redistributable:")
    print("   下载并安装最新的VC++ Redistributable")
    print()
    print("4. 使用依赖检查工具:")
    print("   使用Dependency Walker检查缺失的依赖项")
    print()
    print("5. 手动复制DLL:")
    print("   运行 copy_dlls.bat 脚本")

def main():
    """主函数"""
    print("Xinao异常检测Python绑定 - DLL问题快速修复工具")
    
    if platform.system() != "Windows":
        print("此工具仅支持Windows系统")
        return
    
    # 修复DLL依赖
    fix_success = fix_dll_dependencies()
    
    if fix_success:
        # 测试导入
        import_success = test_import()
        
        if import_success:
            print("\n✓ 修复成功！模块现在应该可以正常使用。")
        else:
            print("\n✗ 修复未完全成功，请查看错误信息。")
            provide_manual_steps()
    else:
        print("\n✗ 自动修复失败")
        provide_manual_steps()

if __name__ == "__main__":
    main()
