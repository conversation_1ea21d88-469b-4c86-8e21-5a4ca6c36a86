#pragma once

#include "result_protocol.h"

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

namespace core {
// Forward declarations
class TcpConnection;
class TcpServer;

namespace protocols {

/**
 * @brief TCP协议实现类
 */
class AIVIDEOCORE_API TcpResultProtocol : public IResultProtocol {
public:
    /**
     * @brief 构造函数
     */
    TcpResultProtocol();

    /**
     * @brief 析构函数
     */
    ~TcpResultProtocol() override;

    /**
     * @brief 启动协议服务
     * @param port TCP服务端口
     * @return 是否成功启动
     */
    bool start(int port) override;

    /**
     * @brief 停止协议服务
     */
    void stop() override;

    /**
     * @brief 发送结果数据
     * @param json_str JSON格式的结果数据
     * @return 是否成功发送
     */
    bool send_result(const std::string& json_str) override;

    /**
     * @brief 获取连接客户端数量
     * @return 连接客户端数量
     */
    int get_client_count() const override;

    /**
     * @brief 获取服务端口
     * @return 服务端口
     */
    int get_port() const override;

    /**
     * @brief 获取协议名称
     * @return 协议名称
     */
    std::string get_protocol_name() const override;

    /**
     * @brief 获取协议状态
     * @return 协议是否正在运行
     */
    bool is_running() const override;

    /**
     * @brief 设置新连接回调函数
     * @param callback 新连接回调函数
     */
    void set_new_connection_callback(std::function<void(void*)> callback) override;

private:
    /**
     * @brief TCP服务线程函数
     */
    void tcp_server_thread_func();

    /**
     * @brief 处理新的客户端连接
     * @param connection 新的客户端连接
     */
    void handle_new_connection(std::shared_ptr<TcpConnection> connection);

    /**
     * @brief 向所有客户端发送结果
     * @param json_str JSON字符串
     */
    void broadcast_to_clients(const std::string& json_str);

private:
    std::atomic<bool> running_;                 ///< 服务是否正在运行
    std::atomic<int> port_;                     ///< TCP服务端口
    std::atomic<int> client_count_;             ///< 连接客户端数量

    std::thread tcp_server_thread_;             ///< TCP服务线程

    // TCP服务相关
    std::vector<std::shared_ptr<TcpConnection>> connections_;  ///< 客户端连接列表
    std::mutex connections_mutex_;              ///< 连接列表互斥锁
    std::unique_ptr<TcpServer> tcp_server_;     ///< TCP服务器

    std::function<void(void*)> new_connection_callback_; ///< 新连接回调函数
};

} // namespace protocols
} // namespace core
