#pragma once

#include <opencv2/opencv.hpp>
#include <string>
#include "../aivideocore_export.h"

namespace utils {

/**
 * @brief Put Chinese text on OpenCV image
 * @param img Target image
 * @param text Text to draw
 * @param pos Position to draw text (x,y)
 * @param fontSize Font size scale
 * @param color Text color in BGR format
 * @param thickness Text thickness
 */
AIVIDEOCORE_API void putTextZH(cv::Mat& img,
               const std::string& text,
               cv::Point pos,
               double fontSize = 1.0,
               const cv::Scalar& color = cv::Scalar(0, 255, 0),
               int thickness = 1);

} // namespace utils