#include "ai/plugins/frame_processors/xingpeng_frame_processor.h"
#include <iostream>
#include <ctime>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <filesystem>

namespace ai {
namespace plugins {
namespace frame_processors {

XingpengFrameProcessor::XingpengFrameProcessor()
    : FrameProcessorPlugin("xingpeng_v3"), required_frames_(5), save_result_image_(false), save_path_("") {
    // 确保插件名称与注册名称一致
    name_ = "xingpeng_v3";

    // 默认启用插件
    set_enabled(true);

    // 设置默认参数
    params_["required_frames"] = "5";
    params_["save_result_image"] = "false";
    params_["save_path"] = "";
}

bool XingpengFrameProcessor::initialize() {
    try {
        // 从参数中读取需要的帧数
        if (params_.find("required_frames") != params_.end()) {
            required_frames_ = std::stoi(params_["required_frames"]);
            // 确保帧数在合理范围内
            if (required_frames_ < 3) {
                required_frames_ = 3;
                std::cout << "帧差累加模式至少需要3帧，已自动调整" << std::endl;
            } else if (required_frames_ > 100) {
                required_frames_ = 100;
            }
        }

        // 从参数中读取是否保存结果图像
        if (params_.find("save_result_image") != params_.end()) {
            save_result_image_ = (params_["save_result_image"] == "true");
        }

        // 从参数中读取保存路径
        if (params_.find("save_path") != params_.end()) {
            save_path_ = params_["save_path"];
            // 确保路径以斜杠结尾
            if (!save_path_.empty() && save_path_.back() != '/' && save_path_.back() != '\\') {
                save_path_ += '/';
            }
            // 确保目录存在
            if (save_result_image_ && !save_path_.empty()) {
                std::filesystem::create_directories(save_path_);
            }
        }

        std::cout << "星鹏帧差累加处理器初始化，需要帧数: " << required_frames_
                  << "，保存结果图像: " << (save_result_image_ ? "是" : "否");
        if (save_result_image_) {
            std::cout << "，保存路径: " << save_path_;
        }
        std::cout << std::endl;

        return true;
    } catch (const std::exception& e) {
        error_message_ = "初始化失败: " + std::string(e.what());
        std::cerr << error_message_ << std::endl;
        return false;
    }
}

cv::Mat XingpengFrameProcessor::process_frames(const std::vector<cv::Mat>& frames) {
    if (frames.empty()) {
        error_message_ = "没有输入帧";
        return cv::Mat();
    }

    try {
        // 如果帧数不足，直接返回第一帧
        // if (frames.size() < 3) {
        //     return frames[0].clone();
        // }

        // 计算所有相邻帧的帧差
        std::vector<cv::Mat> frame_diff_clip;
        for (size_t i = 1; i < frames.size(); ++i) {
            cv::Mat frame_diff = calculate_frame_diff(frames[i-1], frames[i], "gray");
            frame_diff_clip.push_back(frame_diff);
        }

        // 处理帧差序列并生成彩色结果
        cv::Mat result = process_frame_clip_color(frame_diff_clip);

        // 如果需要保存结果图像
        if (save_result_image_ && !save_path_.empty()) {
            // 生成时间戳文件名
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);
            auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
            std::stringstream ss;
            ss << std::put_time(std::localtime(&now_time_t), "%Y%m%d_%H%M%S_") << std::setfill('0') << std::setw(3) << now_ms.count() << ".png";
            std::string filename = save_path_ + ss.str();

            cv::imwrite(filename, result);
            std::cout << "已保存结果图像: " << filename << std::endl;
        }

        return result;
    } catch (const std::exception& e) {
        error_message_ = "处理帧失败: " + std::string(e.what());
        std::cerr << error_message_ << std::endl;
        return frames[0].clone();
    }
}

cv::Mat XingpengFrameProcessor::preprocessing(const cv::Mat& image) {
    cv::Mat gray_image;
    cv::cvtColor(image, gray_image, cv::COLOR_BGR2GRAY);
    cv::GaussianBlur(gray_image, gray_image, cv::Size(5, 5), 0);
    return gray_image;
}

cv::Mat XingpengFrameProcessor::calculate_frame_diff(const cv::Mat& pre_frame, const cv::Mat& cur_frame, const std::string& mode) {
    cv::Mat pre_gray = preprocessing(pre_frame);
    cv::Mat cur_gray = preprocessing(cur_frame);

    pre_gray.convertTo(pre_gray, CV_16S);
    cur_gray.convertTo(cur_gray, CV_16S);

    if (mode == "gray") {
        cv::Mat diff_gray;
        cv::absdiff(pre_gray, cur_gray, diff_gray);
        return diff_gray;
    } else {
        // 光流模式 - 这部分在Python代码中有但在这个实现中我们暂时不需要
        // 如果需要，可以在这里添加光流计算代码
        cv::Mat diff_gray;
        cv::absdiff(pre_gray, cur_gray, diff_gray);
        return diff_gray;
    }
}

cv::Mat XingpengFrameProcessor::colorize(const cv::Mat& bw_img) {
    cv::Mat normalized;
    bw_img.convertTo(normalized, CV_8U, 255.0 / 5.0);
    cv::Mat rgb_img;
    cv::applyColorMap(normalized, rgb_img, cv::COLORMAP_JET);
    return rgb_img;
}

cv::Mat XingpengFrameProcessor::process_frame_clip_color(const std::vector<cv::Mat>& frame_diff_clip) {
    if (frame_diff_clip.empty()) {
        return cv::Mat();
    }

    // 创建累加图
    cv::Mat diff_map = cv::Mat::zeros(frame_diff_clip[0].size(), CV_32F);

    // 累加所有帧差
    for (const auto& f : frame_diff_clip) {
        cv::Mat f_float;
        f.convertTo(f_float, CV_32F);
        diff_map += f_float;
    }

    // 计算平均帧差
    diff_map /= 5.0;

    // 计算阈值
    double mean_th = cv::mean(diff_map)[0];
    double th = mean_th * 10.0;
    if (th < 1.0) {
        th = 1.0;
    }

    // 创建掩码
    cv::Mat diff_map_mask;
    cv::threshold(diff_map, diff_map_mask, th, 255, cv::THRESH_BINARY);
    diff_map_mask.convertTo(diff_map_mask, CV_8U);

    // 形态学操作
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(3, 3));
    cv::morphologyEx(diff_map_mask, diff_map_mask, cv::MORPH_OPEN, kernel);
    cv::morphologyEx(diff_map_mask, diff_map_mask, cv::MORPH_CLOSE, kernel);

    // 创建二值化帧差图
    cv::Mat diff_map_bw = cv::Mat::zeros(frame_diff_clip[0].size(), CV_16S);

    // 对每个帧差进行处理
    for (size_t idx = 0; idx < frame_diff_clip.size(); ++idx) {
        cv::Mat f = frame_diff_clip[idx];
        cv::Mat f_bw;
        cv::threshold(f, f_bw, th, idx + 1, cv::THRESH_BINARY);
        f_bw.convertTo(f_bw, CV_16S);

        // 取最大值
        cv::max(diff_map_bw, f_bw, diff_map_bw);
    }

    // 着色
    cv::Mat diff_map_color = colorize(diff_map_bw);

    // 应用掩码
    cv::Mat mask;
    cv::cvtColor(diff_map_mask, mask, cv::COLOR_GRAY2BGR);
    cv::bitwise_and(diff_map_color, mask, diff_map_color);

    return diff_map_color;
}

int XingpengFrameProcessor::get_required_frames() const {
    return required_frames_;
}

std::string XingpengFrameProcessor::get_description() const {
    return "星鹏帧差累加处理器，用于检测视频中的运动区域";
}

std::string XingpengFrameProcessor::get_version() const {
    return "1.0.0";
}

std::string XingpengFrameProcessor::get_author() const {
    return "AiVideo Team";
}

} // namespace frame_processors
} // namespace plugins
} // namespace ai
