#pragma once

#include <string>
#include <memory>
#include <vector>
#include <Windows.h>
#include "ai/plugins/task_plugin.h"

namespace ai {
namespace plugins {

/**
 * @brief DLL任务插件加载器，用于加载DLL任务插件
 */
class TaskPluginDllLoader {
public:
    /**
     * @brief 构造函数
     */
    TaskPluginDllLoader();

    /**
     * @brief 析构函数
     */
    ~TaskPluginDllLoader();

    /**
     * @brief 加载DLL任务插件
     * @param dll_path DLL文件路径
     * @return 插件指针，如果加载失败则返回nullptr
     */
    std::shared_ptr<TaskPlugin> load_plugin(const std::string& dll_path);

    /**
     * @brief 扫描目录加载所有DLL任务插件
     * @param directory 目录路径
     * @return 加载的插件列表
     */
    std::vector<std::shared_ptr<TaskPlugin>> load_plugins_from_directory(const std::string& directory);

    /**
     * @brief 获取错误信息
     * @return 错误信息
     */
    std::string get_error_message() const;

private:
    /**
     * @brief 检查DLL是否是有效的任务插件
     * @param handle DLL句柄
     * @return 是否是有效的任务插件
     */
    bool is_valid_plugin(HMODULE handle);

    /**
     * @brief 获取Windows错误信息
     * @return 错误信息
     */
    std::string get_windows_error_message();

    std::string error_message_; ///< 错误信息
    static constexpr int TASK_PLUGIN_API_VERSION = 1; ///< 任务插件API版本
};

} // namespace plugins
} // namespace ai
