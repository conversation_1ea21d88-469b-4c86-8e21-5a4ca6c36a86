﻿#pragma once

#include <visionflow/visionflow.hpp>

#include <functional>
#include <map>
#include <memory>
#include <string>
#include "../aivideocore_export.h"

namespace ai {

/**
 * @brief AI模型管理类，负责加载和管理VisionFlow模型
 */
class AIVIDEOCORE_API AiModelManager {
public:
    /**
     * @brief 构造函数
     */
    AiModelManager();

    /**
     * @brief 析构函数
     */
    ~AiModelManager();

    /**
     * @brief 加载模型（不初始化运行时）
     * @param modelPath 模型文件路径
     * @param promptValue 提示词
     * @param scoreValue 置信度阈值
     * @param iouValue IOU阈值
     * @return 是否加载成功
     */
    bool load_model(const std::string& modelPath, const std::string& promptValue, double scoreValue, int iouValue = 70);

    /**
     * @brief 初始化模型运行时
     * @param inputNodeId 输入节点ID
     * @param outputNodeId 输出节点ID
     * @return 是否初始化成功
     */
    bool initialize_runtime(const std::string& inputNodeId, const std::string& outputNodeId);

    /**
     * @brief 更新模型参数
     * @param promptValue 提示词
     * @param scoreValue 置信度阈值
     * @param iouValue IOU阈值
     * @return 是否更新成功
     */
    bool update_model_params(const std::string& promptValue, double scoreValue, int iouValue = 70);

    /**
     * @brief 检查模型是否已加载
     * @return 模型是否已加载
     */
    bool is_model_loaded() const;

    /**
     * @brief 获取模型
     * @return 模型指针
     */
    visionflow::Model* get_model() const;

    /**
     * @brief 获取运行时
     * @return 运行时指针
     */
    visionflow::Runtime* get_runtime() const;

    /**
     * @brief 初始化VisionFlow环境
     * @param licenseId 许可证ID
     * @param serverAddr 服务器地址
     */
    static void initialize_visionflow(const std::string& licenseId, const std::string& serverAddr);

private:
    visionflow::Model* model;       ///< VisionFlow模型
    visionflow::Runtime* runtime;   ///< VisionFlow运行时
    bool modelLoaded;               ///< 模型是否已加载
    bool hasVLM;                    ///< 是否包含万物检测大模型
};

} // namespace ai
