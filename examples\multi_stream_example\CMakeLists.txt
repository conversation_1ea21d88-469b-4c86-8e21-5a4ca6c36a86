cmake_minimum_required(VERSION 3.10)

# 设置项目名称
set(PROJECT_NAME multi_stream_example)

# 添加可执行文件
add_executable(${PROJECT_NAME} multi_stream_example.cpp)

# 链接AiVideoCore库和OpenCV库
target_link_libraries(${PROJECT_NAME} PRIVATE AiVideoCore AIDIOPENCV)

# 设置C++标准
set_target_properties(${PROJECT_NAME} PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 安装目标
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION release
)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
endif()
