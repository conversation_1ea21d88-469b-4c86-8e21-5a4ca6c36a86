﻿#include "tracking/byte_tracker.h"

#include <algorithm>

namespace tracking {

BYTETracker::BYTETracker(int frame_rate, int track_buffer)
    : frame_id_(0), track_id_count_(0),
      max_time_lost_(frame_rate / 30.0 * track_buffer),
      min_iou_threshold_(0.2f) {}

std::vector<strack> BYTETracker::update(const std::vector<object>& objects) {
    frame_id_++;

    // 当前帧的跟踪结果
    std::vector<strack> output_stracks;

    // 处理当前检测结果
    std::vector<strack> detections;
    for (const auto& obj : objects) {
        strack track;
        track.tlwh = obj.rect;
        track.detect_class = obj.label;
        track.score = obj.prob;
        track.state = 0;  // 新检测到的目标初始状态为0
        detections.push_back(track);
    }

    // 对现有轨迹进行预测
    for (auto& track : tracked_stracks_) {
        if (track.state != 3) { // 如果不是已确认消失的目标
            track.tlwh = track.predict_next_position();
        }
    }

    // 关联现有轨迹和新检测结果
    std::vector<std::pair<int, int>> matches;  // (track_idx, detection_idx)
    std::vector<int> unmatched_tracks;
    std::vector<int> unmatched_detections;

    // 初始化未匹配的轨迹
    for (size_t i = 0; i < tracked_stracks_.size(); i++) {
        if (tracked_stracks_[i].state != 3) { // 只考虑未消失的目标
            unmatched_tracks.push_back(i);
        }
    }

    // 初始化未匹配的检测
    for (size_t i = 0; i < detections.size(); i++) {
        unmatched_detections.push_back(i);
    }

    // 对每个现有轨迹，寻找最佳匹配的检测结果
    for (size_t i = 0; i < unmatched_tracks.size(); i++) {
        int track_idx = unmatched_tracks[i];
        float max_iou = min_iou_threshold_;
        int best_detection = -1;

        for (size_t j = 0; j < unmatched_detections.size(); j++) {
            int det_idx = unmatched_detections[j];
            // 只关联相同类别的目标
            if (tracked_stracks_[track_idx].detect_class != detections[det_idx].detect_class)
                continue;

            float iou = strack::calculate_iou(tracked_stracks_[track_idx].tlwh, detections[det_idx].tlwh);
            if (iou > max_iou) {
                max_iou = iou;
                best_detection = det_idx;
            }
        }

        if (best_detection != -1) {
            matches.push_back({track_idx, best_detection});
            // 从未匹配列表中移除已匹配的检测
            unmatched_detections.erase(
                std::remove(unmatched_detections.begin(), unmatched_detections.end(), best_detection),
                unmatched_detections.end()
            );
            // 从未匹配列表中移除已匹配的轨迹
            unmatched_tracks[i] = -1;
        }
    }

    // 清理未匹配轨迹列表
    unmatched_tracks.erase(
        std::remove(unmatched_tracks.begin(), unmatched_tracks.end(), -1),
        unmatched_tracks.end()
    );

    // 处理匹配的轨迹
    for (const auto& [track_idx, det_idx] : matches) {
        // 使用卡尔曼滤波器更新轨迹状态
        tracked_stracks_[track_idx].update_kalman_filter(detections[det_idx].tlwh);
        tracked_stracks_[track_idx].score = detections[det_idx].score;
        tracked_stracks_[track_idx].lost_frames = 0;

        if (tracked_stracks_[track_idx].state == 2) { // 如果之前是丢失状态
            tracked_stracks_[track_idx].state = 1;    // 恢复为跟踪状态
        } else if (tracked_stracks_[track_idx].state == 0) {
            tracked_stracks_[track_idx].state = 1;    // 新目标转为跟踪状态
        }

        // 更新追踪次数
        tracked_stracks_[track_idx].track_count++;

        output_stracks.push_back(tracked_stracks_[track_idx]);
    }

    // 处理未匹配的轨迹（丢失的目标）
    for (int track_idx : unmatched_tracks) {
        tracked_stracks_[track_idx].lost_frames++;

        // 使用卡尔曼滤波器预测位置
        if (tracked_stracks_[track_idx].lost_frames <= max_time_lost_) {
            tracked_stracks_[track_idx].tlwh = tracked_stracks_[track_idx].predict_next_position();
        }

        if (tracked_stracks_[track_idx].lost_frames > max_time_lost_) {
            if (tracked_stracks_[track_idx].state != 3) {  // 如果还没有被标记为消失
                tracked_stracks_[track_idx].state = 3;     // 标记为确认消失
                output_stracks.push_back(tracked_stracks_[track_idx]); // 添加到输出以触发计数
            }
        } else {
            tracked_stracks_[track_idx].state = 2;  // 标记为暂时丢失
            output_stracks.push_back(tracked_stracks_[track_idx]);
        }
    }

    // 处理未匹配的检测（新目标）
    for (int det_idx : unmatched_detections) {
        strack new_track = detections[det_idx];
        new_track.track_id = track_id_count_++;
        new_track.state = 0;  // 新目标
        // 初始化新目标的卡尔曼滤波器
        new_track.update_kalman_filter(new_track.tlwh);
        tracked_stracks_.push_back(new_track);
        output_stracks.push_back(new_track);
    }

    // 更新轨迹列表，移除确认消失的目标
    std::vector<strack> new_tracked_stracks;
    for (const auto& track : tracked_stracks_) {
        if (track.state != 3) {  // 保留未消失的目标
            new_tracked_stracks.push_back(track);
        }
    }
    tracked_stracks_ = new_tracked_stracks;

    return output_stracks;
}

} // namespace tracking
