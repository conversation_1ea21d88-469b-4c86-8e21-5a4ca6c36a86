﻿#include "ai/ai_processor.h"

#include <json/json.h>
#include <visionflow/common/exceptions.hpp>

#include <memory>
#include <set>

#include "utils/cv_utils.h"

namespace ai {

AiProcessor::AiProcessor(std::shared_ptr<AiModelManager> model_manager)
    : model_manager_(model_manager), total_count_(0), fps_(30.0), frame_index_(0),
      frame_skip_interval_(1), use_cpp_frame_processor_(false) {
    // 初始化跟踪器
    tracker_ = std::make_unique<tracking::BYTETracker>(fps_, 2);
    // 初始化插件管理器
    plugin_manager_ = std::make_shared<plugins::PluginManager>();

    // 初始化默认插件
    initialize_default_plugins();

    // 初始化帧内存管理器
    frame_memory_ = std::make_shared<FrameMemory>(30);

    // 初始化Python帧处理器
    python_frame_processor_ = std::make_shared<PythonFrameProcessor>();
    // 注意：不在构造函数中初始化，而是在visionflow初始化后再初始化

    // 初始化C++帧处理器
    cpp_frame_processor_ = std::make_shared<CppFrameProcessor>();
    // 注意：不在构造函数中初始化，而是在需要时再初始化
}

AiProcessor::~AiProcessor() {
}

FrameResult AiProcessor::process_with_ai(const cv::Mat& inputFrame, const std::string& inputNodeId,
                                 const std::string& outputNodeId) {
    if (!model_manager_ || !model_manager_->is_model_loaded() || !model_manager_->get_runtime()) {
        throw std::runtime_error("AI模型未正确加载或初始化");
    }

    try {
        // 更新帧索引
        frame_index_++;

        // 初始化结果结构
        FrameResult result;
        result.frame_id = frame_index_;
        result.timestamp = std::chrono::system_clock::now().time_since_epoch().count();
        result.total_count = total_count_;
        result.class_counts = class_counts_;
        result.task_type = "detection";

        cv::Mat tmp_mat = inputFrame.clone();
        frame_memory_->add_frame(tmp_mat);

        cv::Mat process_image;
        // 如果C++帧处理器已启用，先使用C++帧处理器处理帧
        if (use_cpp_frame_processor_ && cpp_frame_processor_ && cpp_frame_processor_->is_enabled()) {
            // 获取需要的帧数
            std::vector<cv::Mat> process_frames;
            int required_frames = cpp_frame_processor_->get_required_frames();
            if (required_frames > 1) {
                // 如果需要多帧，获取历史帧
                process_frames = frame_memory_->get_frames(required_frames);
            }

            // 处理帧
            process_image = cpp_frame_processor_->process_frames(process_frames);
        }
        // 如果Python帧处理器已启用，使用Python帧处理器处理帧
        else if (python_frame_processor_ && python_frame_processor_->is_enabled() && python_frame_processor_->is_initialized()) {
            // 获取需要的帧数
            std::vector<cv::Mat> process_frames;
            int required_frames = python_frame_processor_->get_required_frames();
            if (required_frames > 1) {
                // 如果需要多帧，获取历史帧
                process_frames = frame_memory_->get_frames(required_frames);
            }

            // 处理帧
            process_image = python_frame_processor_->process_frames(process_frames);
        } else {
            process_image = tmp_mat.clone();
        }

        // 抽帧检测逻辑
        visionflow::props::PolygonRegionList segment_pred;
        std::vector<tracking::strack> output_stracks;

        // 如果是第一帧或者达到抽帧间隔，执行检测
        if (frame_index_ % frame_skip_interval_ == 0) {
            // 第一段：模型推理
            segment_pred = perform_model_inference(process_image, inputNodeId, outputNodeId);

            // 第二段：目标跟踪
            output_stracks = perform_tracking(segment_pred);

            // 更新上次检测结果
            last_detection_result_ = segment_pred;
            last_tracking_result_ = output_stracks;
        } else {
            // 使用上次的检测结果
            segment_pred = last_detection_result_;

            // 使用上次的跟踪结果，并进行简单的位置更新
            output_stracks = last_tracking_result_;
        }

        // 设置检测结果
        result.detection_tracks = output_stracks;

        // 更新跟踪结果
        frame_memory_->add_tracks(output_stracks);

        // 获取历史帧
        const std::vector<cv::Mat>& history_frames = frame_memory_->get_frames(frame_memory_->get_frame_count());

        // 获取历史跟踪结果
        const std::vector<std::vector<tracking::strack>>& history_tracks = frame_memory_->get_tracks(frame_memory_->get_track_count());

        // 第三段：执行任务处理（使用插件）
        plugin_manager_->process_all_plugins_batch(history_frames, history_tracks, result);

        // 如果没有插件处理，使用默认的绘制方式
        if (result.task_type == "detection" || result.task_type.empty()) {
            // 普通AI处理模式的绘制
            visionflow::DrawOptions draw_opts;
            draw_opts.line_thickness = 2;
            draw_opts.with_name = true;
            draw_opts.color = {0, 0, 255};

            // 创建一个新的图像对象用于绘制
            visionflow::Image draw_image;
            draw_image.from_mat(tmp_mat);
            for (auto &[id, region] : segment_pred) {
                // std::cout << "Found a defect: " << id << ", name: " << region.name()
                //             << ", area: " << region.area() << std::endl;
                auto region_name = region.name();
                std::replace(region_name.begin(), region_name.end(), ' ', '_');
                region.set_name(region_name);
                region.draw_on(draw_image, draw_opts);
            }
            result.processed_frame = draw_image.to_mat();
        }

        return result;
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("AI处理错误: ") + e.what());
    } catch (...) {
        throw std::runtime_error("AI处理时发生未知错误");
    }
}

void AiProcessor::reset_counts() {
    class_counts_.clear();
    track_class_map_.clear();
    total_count_ = 0;

    // 重置帧索引
    frame_index_ = 0;

    // 重新创建跟踪器
    tracker_ = std::make_unique<tracking::BYTETracker>(fps_, 2);

    // 重置抽帧检测相关变量
    last_detection_result_ = visionflow::props::PolygonRegionList(); // 创建新的空对象
    last_tracking_result_.clear();

    // 清空帧历史
    if (frame_memory_) {
        frame_memory_->clear();
    }
}

int AiProcessor::get_total_count() const {
    return total_count_;
}

const std::map<std::string, int>& AiProcessor::get_class_counts() const {
    return class_counts_;
}

void AiProcessor::set_fps(double fps) {
    this->fps_ = fps;
    // 重新创建跟踪器以更新帧率
    tracker_ = std::make_unique<tracking::BYTETracker>(fps_, 2);
}

void AiProcessor::set_frame_skip_interval(int interval) {
    if (interval < 1) {
        interval = 1; // 确保间隔至少为1
    }
    this->frame_skip_interval_ = interval;
}

int AiProcessor::get_frame_skip_interval() const {
    return this->frame_skip_interval_;
}



visionflow::props::PolygonRegionList AiProcessor::perform_model_inference(const cv::Mat& inputFrame,
                                                                      const std::string& inputNodeId,
                                                                      const std::string& outputNodeId) {
    // 使用模型处理图像
    auto sample = model_manager_->get_runtime()->create_sample();
    auto image = visionflow::Image();
    image.from_mat(inputFrame);

    visionflow::helper::add_image_to_sample(sample, image, inputNodeId);
    model_manager_->get_runtime()->execute(sample);

    auto model_result = sample.get(outputNodeId);
    if (!model_result) {
        throw std::runtime_error("无法获取模型输出结果");
    }

    auto segment_pred = model_result->as<visionflow::props::PolygonRegionList>();
    for (auto &[id, region] : segment_pred) {
        auto region_name = region.name();
        std::replace(region_name.begin(), region_name.end(), ' ', '_');
        region.set_name(region_name);
    }

    return segment_pred;
}

std::vector<tracking::strack> AiProcessor::perform_tracking(const visionflow::props::PolygonRegionList& segment_pred) {
    std::vector<tracking::object> objects;
    for (auto &[id, region] : segment_pred) {
        tracking::object obj;
        auto bbox = region.polygon().bounding_box();
        obj.rect.x = bbox.xmin_ymin().x;
        obj.rect.y = bbox.xmin_ymin().y;
        obj.rect.width = bbox.size.w;
        obj.rect.height = bbox.size.h;
        obj.prob = region.score();
        obj.label = region.name();
        objects.push_back(obj);
    }

    // 使用改进的ByteTrack进行追踪
    if (!tracker_) {
        tracker_ = std::make_unique<tracking::BYTETracker>(fps_, 2);
    }

    return tracker_->update(objects);
}

void AiProcessor::perform_tasks(cv::Mat& tmp_mat,
                              const std::vector<tracking::strack>& output_stracks,
                              FrameResult& result,
                              const visionflow::props::PolygonRegionList& segment_pred) {
    // 使用插件管理器处理所有启用的插件
    std::vector<cv::Mat> frames = {tmp_mat};
    std::vector<std::vector<tracking::strack>> tracks_list = {output_stracks};
    plugin_manager_->process_all_plugins_batch(frames, tracks_list, result);

    // 更新输入帧
    if (!frames.empty()) {
        tmp_mat = frames[0];
    }

    // 如果没有插件处理，或者所有插件都未启用，则使用默认的绘制方式
    if (result.processed_frame.empty() && (result.task_type == "detection" || result.task_type.empty())) {
        // 普通AI处理模式的绘制
        visionflow::DrawOptions draw_opts;
        draw_opts.line_thickness = 2;
        draw_opts.with_name = true;
        draw_opts.color = {0, 0, 255};

        // 创建一个新的图像对象用于绘制
        visionflow::Image draw_image;
        draw_image.from_mat(tmp_mat);
        for (auto &[id, region] : segment_pred) {
            region.draw_on(draw_image, draw_opts);
        }
        result.processed_frame = draw_image.to_mat();
    } else if (result.processed_frame.empty()) {
        // 已经由插件处理过，使用插件处理后的图像
        result.processed_frame = tmp_mat;
    }
}

void AiProcessor::initialize_default_plugins() {

}

bool AiProcessor::register_plugin(std::shared_ptr<plugins::TaskPlugin> plugin) {
    return plugin_manager_->register_plugin(plugin);
}

bool AiProcessor::unregister_plugin(const std::string& name) {
    return plugin_manager_->unregister_plugin(name);
}

std::shared_ptr<plugins::TaskPlugin> AiProcessor::get_plugin(const std::string& name) {
    return plugin_manager_->get_plugin(name);
}

std::vector<std::shared_ptr<plugins::TaskPlugin>> AiProcessor::get_all_plugins() {
    return plugin_manager_->get_all_plugins();
}

bool AiProcessor::enable_plugin(const std::string& name) {
    return plugin_manager_->enable_plugin(name);
}

bool AiProcessor::disable_plugin(const std::string& name) {
    return plugin_manager_->disable_plugin(name);
}

bool AiProcessor::load_dll_plugin(const std::string& dll_path) {
    return plugin_manager_->load_dll_plugin(dll_path);
}

int AiProcessor::load_dll_plugins_from_directory(const std::string& directory) {
    return plugin_manager_->load_dll_plugins_from_directory(directory);
}

int AiProcessor::reload_dll_plugins(const std::string& directory) {
    // 如果目录为空，使用默认目录
    std::string plugin_dir = directory.empty() ?
        plugin_manager_->get_plugin_path() : directory;

    // 设置插件目录
    if (!directory.empty()) {
        plugin_manager_->set_plugin_path(plugin_dir);
    }

    // 加载DLL插件
    return load_dll_plugins_from_directory(plugin_dir);
}

// 新版本绘制函数
void AiProcessor::draw_counting_info(cv::Mat& frame, const std::vector<tracking::strack>& tracks) {
    // 绘制跟踪框和计数信息
    for (const auto& strack : tracks) {
        cv::Scalar color(0, 255, 255); // 黄色用于计数模式
        cv::Rect_<float> tlwh = strack.tlwh;
        cv::rectangle(frame, cv::Rect(tlwh.x, tlwh.y, tlwh.width, tlwh.height), color, 2);

        std::string label = "ID:" + std::to_string(strack.track_id) + " " + strack.detect_class;
        utils::putTextZH(frame, label,
                    cv::Point(tlwh.x, tlwh.y - 5),
                     0.6, color, 2);
    }

    // 在画面左上角显示计数信息
    int y = 30;
    for (const auto& [className, count] : class_counts_) {
        std::string countText = className + ": " + std::to_string(count);
        utils::putTextZH(frame, countText, cv::Point(10, y), 0.6, cv::Scalar(255, 255, 255), 2);
        y += 25;
    }
    utils::putTextZH(frame, "Total: " + std::to_string(total_count_),
                cv::Point(10, y), 0.6,
                cv::Scalar(255, 255, 255), 2);
}



void AiProcessor::draw_basic_tracking_info(cv::Mat& frame,
                                      const std::vector<tracking::strack>& tracks) {
    for (const auto& strack : tracks) {
        cv::Scalar color(255, 255, 255); // 白色用于基本跟踪
        cv::Rect_<float> tlwh = strack.tlwh;
        cv::rectangle(frame, cv::Rect(tlwh.x, tlwh.y, tlwh.width, tlwh.height), color, 2);

        std::string label = "ID:" + std::to_string(strack.track_id);
        utils::putTextZH(frame, label,
                    cv::Point(tlwh.x, tlwh.y - 5),
                    0.6, color, 2);
    }
}



// 统一渲染函数
void AiProcessor::render_frame_result(cv::Mat& frame, const FrameResult& result) {
    // 根据任务类型选择不同的渲染方式
    if (result.task_type == "counting") {
        // 绘制跟踪框和计数信息
        draw_counting_info(frame, result.detection_tracks);
    }



    if (result.task_type == "detection") {
        // 只绘制基本的检测框
        draw_basic_tracking_info(frame, result.detection_tracks);
    }

    // 在右上角显示任务类型
    std::string task_text = "Task: " + result.task_type;
    utils::putTextZH(frame, task_text, cv::Point(frame.cols - 300, 30), 0.6, cv::Scalar(255, 255, 255), 2);

    // 在右上角显示帧ID
    std::string frame_id_text = "Frame ID: " + std::to_string(result.frame_id);
    utils::putTextZH(frame, frame_id_text, cv::Point(frame.cols - 300, 60), 0.6, cv::Scalar(255, 255, 255), 2);
}





void AiProcessor::set_max_frame_history(int count) {
    if (count > 0 && frame_memory_) {
        frame_memory_->set_max_history(count);
    }
}

int AiProcessor::get_max_frame_history() const {
    return frame_memory_ ? frame_memory_->get_max_history() : 0;
}

std::shared_ptr<PythonFrameProcessor> AiProcessor::get_python_frame_processor() const {
    return python_frame_processor_;
}

std::shared_ptr<CppFrameProcessor> AiProcessor::get_cpp_frame_processor() const {
    return cpp_frame_processor_;
}

std::shared_ptr<FrameMemory> AiProcessor::get_frame_memory() const {
    return frame_memory_;
}

const std::deque<cv::Mat>& AiProcessor::get_frame_history() const {
    // 兼容旧接口，返回帧内存中的所有帧
    return frame_memory_->get_all_frames();
}

void AiProcessor::update_frame_history(const cv::Mat& frame, const std::vector<tracking::strack>& tracks) {
    // 更新帧历史
    if (frame_memory_) {
        frame_memory_->add_frame(frame, tracks);
    }
}

bool AiProcessor::set_python_script(const std::string& script_path) {
    if (!python_frame_processor_) {
        return false;
    }
    return python_frame_processor_->set_script_path(script_path);
}

void AiProcessor::enable_python_frame_processing(bool enable) {
    if (python_frame_processor_) {
        python_frame_processor_->set_enabled(enable);
    }
}

void AiProcessor::set_python_script_params(const std::map<std::string, std::string>& params) {
    if (python_frame_processor_) {
        python_frame_processor_->set_params(params);
    }
}

std::map<std::string, std::string> AiProcessor::get_python_script_params() const {
    if (!python_frame_processor_) {
        return {};
    }
    return python_frame_processor_->get_params();
}

bool AiProcessor::initialize_python_frame_processor() {
    if (!python_frame_processor_) {
        return false;
    }

    // 初始化Python帧处理器
    // 注意：这个方法应该在visionflow初始化之后调用
    bool result = python_frame_processor_->initialize();

    // 加载保存的配置
    if (result) {
        python_frame_processor_->load_config();
    }

    return result;
}

bool AiProcessor::initialize_cpp_frame_processor() {
    if (!cpp_frame_processor_) {
        return false;
    }

    // 初始化C++帧处理器
    bool result = cpp_frame_processor_->initialize();

    // 加载保存的配置
    if (result) {
        cpp_frame_processor_->load_config();
    }

    return result;
}

std::shared_ptr<plugins::PluginManager> AiProcessor::get_plugin_manager() const {
    return plugin_manager_;
}

void AiProcessor::set_use_cpp_frame_processor(bool use) {
    use_cpp_frame_processor_ = use;
}

bool AiProcessor::is_using_cpp_frame_processor() const {
    return use_cpp_frame_processor_;
}

} // namespace ai

















