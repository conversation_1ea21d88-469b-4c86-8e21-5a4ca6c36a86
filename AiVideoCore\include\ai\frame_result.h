#pragma once

#include <json/json.h>
#include <opencv2/opencv.hpp>

#include <map>
#include <string>
#include <vector>

#include "tracking/strack.h"
#include "../aivideocore_export.h"

namespace ai {

/**
 * @brief 帧处理结果结构体，统一存储各种处理结果
 */
struct AIVIDEOCORE_API FrameResult {
    int frame_id;                                  ///< 帧ID
    int64_t timestamp;                            ///< 结果时间戳
    cv::Mat processed_frame;                       ///< 处理后的帧
    std::vector<tracking::strack> detection_tracks; ///< 检测和跟踪结果
    std::map<std::string, int> class_counts;       ///< 各类别计数
    int total_count;                               ///< 总计数
    double action_duration;                         ///< 当前行为持续时间
    std::string task_type;                         ///< 任务类型：detection, counting, event_detection
    std::string ext_info;                          ///< 扩展信息

    /**
     * @brief 构造函数，初始化时间戳
     */
    FrameResult() : frame_id(0), timestamp(0), total_count(0) {}

    /**
     * @brief 将结果转换为JSON格式
     * @return JSON格式的结果
     */
    Json::Value to_json() const;

    /**
     * @brief 从JSON解析结果
     * @param json JSON数据
     * @return 解析后的帧结果
     */
    static FrameResult from_json(const Json::Value& json);
};

} // namespace ai


