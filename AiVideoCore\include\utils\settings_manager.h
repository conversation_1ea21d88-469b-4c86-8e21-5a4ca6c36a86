#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <variant>
#include <filesystem>
#include "aivideocore_export.h"

namespace utils {

/**
 * @brief 设置值类，用于存储不同类型的配置值
 */
class AIVIDEOCORE_API SettingsValue {
public:
    using ValueType = std::variant<bool, int, double, std::string>;

    SettingsValue() : value_(std::string{}) {}
    SettingsValue(bool val) : value_(val) {}
    SettingsValue(int val) : value_(val) {}
    SettingsValue(double val) : value_(val) {}
    SettingsValue(const std::string& val) : value_(val) {}
    SettingsValue(const char* val) : value_(std::string(val)) {}

    // 类型转换方法
    bool toBool() const;
    int toInt() const;
    double toDouble() const;
    std::string toString() const;

    // 检查是否为空
    bool isNull() const { return std::holds_alternative<std::string>(value_) && std::get<std::string>(value_).empty(); }

    // 获取类型名称
    const char* typeName() const;

private:
    ValueType value_;
};

/**
 * @brief 设置管理器，负责应用程序配置的存储和读取
 *
 * 该类将配置存储在AppData目录下的INI文件中，而不是Windows注册表
 */
class AIVIDEOCORE_API SettingsManager {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static SettingsManager& get_instance();

    /**
     * @brief 初始化设置管理器
     * @param organization 组织名称
     * @param application 应用程序名称
     */
    void initialize(const std::string& organization, const std::string& application);

    /**
     * @brief 获取设置值
     * @param key 设置键
     * @param defaultValue 默认值
     * @return 设置值
     */
    SettingsValue value(const std::string& key, const SettingsValue& defaultValue = SettingsValue()) const;

    /**
     * @brief 设置值
     * @param key 设置键
     * @param value 设置值
     */
    void setValue(const std::string& key, const SettingsValue& value);

    /**
     * @brief 开始组
     * @param prefix 组前缀
     */
    void beginGroup(const std::string& prefix);

    /**
     * @brief 结束组
     */
    void endGroup();

    /**
     * @brief 获取当前组的所有键
     * @return 键列表
     */
    std::vector<std::string> childKeys() const;

    /**
     * @brief 获取当前组的所有组
     * @return 组列表
     */
    std::vector<std::string> childGroups() const;

    /**
     * @brief 检查是否包含指定键
     * @param key 键名
     * @return 是否包含
     */
    bool contains(const std::string& key) const;

    /**
     * @brief 移除指定键
     * @param key 键名
     */
    void remove(const std::string& key);

    /**
     * @brief 获取设置文件路径
     * @return 设置文件路径
     */
    std::string fileName() const;

    /**
     * @brief 强制同步设置到磁盘
     */
    void sync();

    /**
     * @brief 从注册表迁移设置到文件
     * @param organization 组织名称
     * @param application 应用程序名称
     */
    void migrateFromRegistry(const std::string& organization, const std::string& application);

private:
    /**
     * @brief 构造函数
     */
    SettingsManager();

    /**
     * @brief 析构函数
     */
    ~SettingsManager();

    /**
     * @brief 复制构造函数（禁用）
     */
    SettingsManager(const SettingsManager&) = delete;

    /**
     * @brief 赋值运算符（禁用）
     */
    SettingsManager& operator=(const SettingsManager&) = delete;

private:
    // 内部 INI 文件解析器
    class IniParser;

    std::unique_ptr<IniParser> parser_;   ///< INI 解析器
    std::string config_path_;             ///< 配置文件路径
    std::string current_group_;           ///< 当前组路径
    bool initialized_ = false;            ///< 是否已初始化
};

} // namespace utils
