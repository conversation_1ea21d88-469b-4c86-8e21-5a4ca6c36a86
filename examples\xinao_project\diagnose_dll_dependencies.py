#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DLL依赖诊断脚本
用于诊断Python模块的DLL依赖问题
"""

import os
import sys
import subprocess
import platform

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✓ {description}: {file_path}")
        return True
    else:
        print(f"✗ {description}: {file_path} (不存在)")
        return False

def get_dll_dependencies(dll_path):
    """获取DLL的依赖项（仅Windows）"""
    if platform.system() != "Windows":
        return []
    
    try:
        # 使用dumpbin工具获取依赖项
        result = subprocess.run(
            ["dumpbin", "/dependents", dll_path],
            capture_output=True,
            text=True,
            check=True
        )
        
        dependencies = []
        lines = result.stdout.split('\n')
        in_dependencies = False
        
        for line in lines:
            line = line.strip()
            if "Image has the following dependencies:" in line:
                in_dependencies = True
                continue
            elif in_dependencies and line.endswith('.dll'):
                dependencies.append(line)
            elif in_dependencies and line == "":
                break
                
        return dependencies
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  警告: 无法获取DLL依赖项（需要Visual Studio工具）")
        return []

def diagnose_python_module():
    """诊断Python模块"""
    print("=" * 60)
    print("Python模块DLL依赖诊断")
    print("=" * 60)
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 检查Python模块文件
    module_paths = [
        "../../bin/release/xinao_anomaly_detection.pyd",
        "../../build/examples/xinao_project/Release/xinao_anomaly_detection.pyd",
        "../../build/examples/xinao_project/xinao_anomaly_detection.pyd",
        "./xinao_anomaly_detection.pyd"
    ]
    
    module_found = False
    module_path = None
    
    for path in module_paths:
        if os.path.exists(path):
            module_path = os.path.abspath(path)
            print(f"✓ 找到Python模块: {module_path}")
            module_found = True
            break
    
    if not module_found:
        print("✗ 未找到Python模块文件")
        print("请确保已正确编译项目")
        return False
    
    # 检查模块所在目录的DLL文件
    module_dir = os.path.dirname(module_path)
    print(f"\n模块目录: {module_dir}")
    
    # 必需的DLL文件
    required_dlls = [
        "AiVideoCore.dll",
        "xinao_project.dll",
        "opencv_world470.dll",
        "python312.dll"  # 根据Python版本调整
    ]
    
    print("\n检查必需的DLL文件:")
    all_dlls_found = True
    
    for dll in required_dlls:
        dll_path = os.path.join(module_dir, dll)
        if not check_file_exists(dll_path, dll):
            all_dlls_found = False
    
    # 列出模块目录中的所有DLL文件
    print(f"\n{module_dir} 中的所有DLL文件:")
    dll_files = [f for f in os.listdir(module_dir) if f.endswith('.dll')]
    if dll_files:
        for dll in sorted(dll_files):
            print(f"  - {dll}")
    else:
        print("  (无DLL文件)")
    
    # 检查DLL依赖项
    if platform.system() == "Windows" and module_path:
        print(f"\n分析 {os.path.basename(module_path)} 的依赖项:")
        dependencies = get_dll_dependencies(module_path)
        if dependencies:
            for dep in dependencies:
                dep_path = os.path.join(module_dir, dep)
                if os.path.exists(dep_path):
                    print(f"  ✓ {dep}")
                else:
                    print(f"  ✗ {dep} (缺失)")
                    all_dlls_found = False
    
    return all_dlls_found

def test_import():
    """测试导入模块"""
    print("\n" + "=" * 60)
    print("测试导入模块")
    print("=" * 60)
    
    # 添加可能的模块路径到sys.path
    possible_paths = [
        "../../bin/release",
        "../../build/examples/xinao_project/Release",
        "../../build/examples/xinao_project",
        "."
    ]
    
    for path in possible_paths:
        abs_path = os.path.abspath(path)
        if os.path.exists(abs_path) and abs_path not in sys.path:
            sys.path.insert(0, abs_path)
            print(f"添加路径到sys.path: {abs_path}")
    
    try:
        print("\n尝试导入模块...")
        import xinao_anomaly_detection
        print("✓ 模块导入成功!")
        
        # 测试基本功能
        print(f"模块版本: {getattr(xinao_anomaly_detection, '__version__', '未知')}")
        print(f"模块作者: {getattr(xinao_anomaly_detection, '__author__', '未知')}")
        
        # 测试类创建（使用虚拟路径）
        try:
            detector = xinao_anomaly_detection.AnomalyDetectionInterface("dummy_path")
            print("✓ 类实例化成功!")
            return True
        except Exception as e:
            print(f"✗ 类实例化失败: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 未知错误: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案建议")
    print("=" * 60)
    
    print("1. 重新编译项目:")
    print("   cd ../../build")
    print("   cmake --build . --config Release --target xinao_anomaly_detection")
    print("   cmake --install .")
    
    print("\n2. 手动复制DLL文件:")
    print("   将以下文件复制到Python模块所在目录:")
    print("   - AiVideoCore.dll")
    print("   - xinao_project.dll") 
    print("   - opencv_world470.dll")
    print("   - 其他依赖的DLL文件")
    
    print("\n3. 检查Python版本兼容性:")
    print("   确保使用Python 3.12（项目配置的版本）")
    
    print("\n4. 设置环境变量:")
    print("   将包含DLL文件的目录添加到PATH环境变量")
    
    print("\n5. 使用依赖检查工具:")
    print("   使用Dependency Walker或类似工具检查缺失的依赖项")

def main():
    """主函数"""
    print("Xinao异常检测Python绑定 - DLL依赖诊断工具")
    
    # 诊断模块
    dll_ok = diagnose_python_module()
    
    # 测试导入
    import_ok = test_import()
    
    # 提供解决方案
    if not dll_ok or not import_ok:
        provide_solutions()
    else:
        print("\n✓ 所有检查通过！模块应该可以正常使用。")

if __name__ == "__main__":
    main()
