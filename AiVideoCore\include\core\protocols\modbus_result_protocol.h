#pragma once

#include "result_protocol.h"

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

namespace core {
namespace protocols {

/**
 * @brief Modbus协议实现类
 */
class AIVIDEOCORE_API ModbusResultProtocol : public IResultProtocol {
public:
    /**
     * @brief 构造函数
     */
    ModbusResultProtocol();

    /**
     * @brief 析构函数
     */
    ~ModbusResultProtocol() override;

    /**
     * @brief 启动协议服务
     * @param port Modbus服务端口，默认为502
     * @return 是否成功启动
     */
    bool start(int port) override;

    /**
     * @brief 停止协议服务
     */
    void stop() override;

    /**
     * @brief 发送结果数据
     * @param json_str JSON格式的结果数据
     * @return 是否成功发送
     */
    bool send_result(const std::string& json_str) override;

    /**
     * @brief 获取连接客户端数量
     * @return 连接客户端数量
     */
    int get_client_count() const override;

    /**
     * @brief 获取服务端口
     * @return 服务端口
     */
    int get_port() const override;

    /**
     * @brief 获取协议名称
     * @return 协议名称
     */
    std::string get_protocol_name() const override;

    /**
     * @brief 获取协议状态
     * @return 协议是否正在运行
     */
    bool is_running() const override;

    /**
     * @brief 设置新连接回调函数
     * @param callback 新连接回调函数
     */
    void set_new_connection_callback(std::function<void(void*)> callback) override;

    /**
     * @brief 设置Modbus寄存器映射
     * @param register_map 寄存器映射表
     */
    void set_register_map(const std::unordered_map<std::string, uint16_t>& register_map);

    /**
     * @brief 获取Modbus寄存器映射
     * @return 寄存器映射表
     */
    std::unordered_map<std::string, uint16_t> get_register_map() const;

private:
    /**
     * @brief Modbus服务线程函数
     */
    void modbus_server_thread_func();

    /**
     * @brief 解析JSON结果并更新Modbus寄存器
     * @param json_str JSON字符串
     */
    void parse_json_and_update_registers(const std::string& json_str);

private:
    std::atomic<bool> running_;                 ///< 服务是否正在运行
    std::atomic<int> port_;                     ///< Modbus服务端口
    std::atomic<int> client_count_;             ///< 连接客户端数量

    std::thread modbus_server_thread_;          ///< Modbus服务线程

    // Modbus服务相关
    void* modbus_context_;                      ///< Modbus上下文
    std::mutex modbus_mutex_;                   ///< Modbus操作互斥锁

    // 寄存器映射
    std::unordered_map<std::string, uint16_t> register_map_; ///< JSON字段到Modbus寄存器的映射
    std::mutex register_map_mutex_;             ///< 寄存器映射互斥锁

    std::function<void(void*)> new_connection_callback_; ///< 新连接回调函数
};

} // namespace protocols
} // namespace core
