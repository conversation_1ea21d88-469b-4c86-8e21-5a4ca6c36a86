#pragma once

#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "ai/plugins/task_plugin.h"
#include "ai/plugins/task_plugin_dll_loader.h"
#include "aivideocore_export.h"

namespace ai {
namespace plugins {

/**
 * @brief 插件管理器，负责管理和调用任务处理插件
 */
class AIVIDEOCORE_API PluginManager {
public:

    /**
     * @brief 注册插件
     * @param plugin 插件指针
     * @return 是否注册成功
     */
    bool register_plugin(std::shared_ptr<TaskPlugin> plugin);

    /**
     * @brief 注销插件
     * @param name 插件名称
     * @return 是否注销成功
     */
    bool unregister_plugin(const std::string& name);

    /**
     * @brief 获取插件
     * @param name 插件名称
     * @return 插件指针，如果不存在则返回nullptr
     */
    std::shared_ptr<TaskPlugin> get_plugin(const std::string& name);

    /**
     * @brief 获取所有插件
     * @return 所有插件的列表
     */
    std::vector<std::shared_ptr<TaskPlugin>> get_all_plugins();

    /**
     * @brief 获取特定类型的插件
     * @param type 插件类型
     * @return 特定类型的插件列表
     */
    std::vector<std::shared_ptr<TaskPlugin>> get_plugins_by_type(const std::string& type);

    /**
     * @brief 启用插件（并保存到全局设置）
     * @param name 插件名称
     * @return 是否成功启用
     */
    bool enable_plugin(const std::string& name);

    /**
     * @brief 禁用插件（并保存到全局设置）
     * @param name 插件名称
     * @return 是否成功禁用
     */
    bool disable_plugin(const std::string& name);

    /**
     * @brief 启用插件（不保存到全局设置）
     * @param name 插件名称
     * @return 是否成功启用
     */
    bool set_plugin_enabled(const std::string& name, bool enabled);

    /**
     * @brief 禁用所有插件（不保存到全局设置）
     */
    void disable_all_plugins();

    /**
     * @brief 处理所有启用的插件
     * @param frame 输入/输出图像
     * @param tracks 跟踪结果
     * @param result 处理结果
     * @deprecated 已过时，请使用多帧处理接口
     */
    void process_all_plugins(cv::Mat& frame,
                           const std::vector<tracking::strack>& tracks,
                           FrameResult& result);

    /**
     * @brief 批量处理所有启用的插件
     * @param frames 输入/输出图像列表，包含当前帧及前序帧
     * @param tracks_list 跟踪结果列表，每个元素对应一帧的跟踪结果
     * @param result 处理结果，包含当前帧的处理结果
     */
    void process_all_plugins_batch(const std::vector<cv::Mat>& frames,
                                 const std::vector<std::vector<tracking::strack>>& tracks_list,
                                 FrameResult& result);

    /**
     * @brief 重置所有插件
     */
    void reset_all_plugins();

    /**
     * @brief 设置插件加载路径
     * @param path 插件加载路径
     */
    void set_plugin_path(const std::string& path);

    /**
     * @brief 获取插件加载路径
     * @return 插件加载路径
     */
    std::string get_plugin_path() const;

    /**
     * @brief 加载插件
     * @param plugin_file 插件文件路径
     * @return 是否加载成功
     */
    bool load_plugin(const std::string& plugin_file);

    /**
     * @brief 加载目录中的所有插件
     * @param directory 插件目录
     * @return 成功加载的插件数量
     */
    int load_plugins_from_directory(const std::string& directory);

    /**
     * @brief 设置插件加载回调
     * @param callback 回调函数
     */
    void set_plugin_load_callback(std::function<void(const std::string&, bool)> callback);

    /**
     * @brief 加载DLL任务插件
     * @param dll_path DLL文件路径
     * @return 是否加载成功
     */
    bool load_dll_plugin(const std::string& dll_path);

    /**
     * @brief 从目录加载所有DLL任务插件
     * @param directory 目录路径
     * @return 成功加载的插件数量
     */
    int load_dll_plugins_from_directory(const std::string& directory);

public:
    /**
     * @brief 构造函数
     * @param plugin_path 插件加载路径，默认为"plugins"
     */
    PluginManager(const std::string& plugin_path = "plugins");

    /**
     * @brief 析构函数
     */
    ~PluginManager();

private:

    void load_plugin_state(std::shared_ptr<TaskPlugin> plugin);
    void save_plugin_state(const std::string& name, bool enabled);

    std::map<std::string, std::shared_ptr<TaskPlugin>> plugins_;  ///< 插件映射表
    std::string plugin_path_;  ///< 插件加载路径
    std::function<void(const std::string&, bool)> plugin_load_callback_;  ///< 插件加载回调
    TaskPluginDllLoader dll_plugin_loader_;  ///< DLL任务插件加载器
};

} // namespace plugins
} // namespace ai

