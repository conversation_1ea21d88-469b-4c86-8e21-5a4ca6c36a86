#pragma once

#include <opencv2/opencv.hpp>

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "ai/frame_result.h"
#include "tracking/byte_tracker.h"

namespace ai {
namespace plugins {

class TaskPlugin;

/**
 * @brief 任务处理插件基类
 */
class TaskPlugin {
public:
    /**
     * @brief 构造函数
     * @param name 插件名称
     */
    TaskPlugin(const std::string& name) : name_(name), enabled_(true) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~TaskPlugin() = default;

    /**
     * @brief 获取插件名称
     * @return 插件名称
     */
    std::string get_name() const { return name_; }

    /**
     * @brief 设置插件是否启用
     * @param enabled 是否启用
     */
    void set_enabled(bool enabled) { enabled_ = enabled; }

    /**
     * @brief 获取插件是否启用
     * @return 是否启用
     */
    bool is_enabled() const { return enabled_; }

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    virtual bool initialize() = 0;

    /**
     * @brief 处理任务
     * @param frame 输入/输出图像
     * @param tracks 跟踪结果
     * @param result 处理结果
     * @return 是否处理成功
     * @deprecated 已过时，请使用多帧处理接口
     */
    virtual bool process(cv::Mat& frame,
                        const std::vector<tracking::strack>& tracks,
                        ai::FrameResult& result) {
        // 默认实现，调用多帧接口并只使用第一帧结果
        std::vector<cv::Mat> frames = {frame};
        std::vector<std::vector<tracking::strack>> tracks_list = {tracks};
        ai::FrameResult temp_result;
        bool success = process_batch(frames, tracks_list, temp_result);
        return success;
    }

    /**
     * @brief 批量处理多帧任务
     * @param frames 输入/输出图像列表，包含当前帧及前序帧
     * @param tracks_list 跟踪结果列表，每个元素对应一帧的跟踪结果
     * @param result 处理结果，包含当前帧的处理结果
     * @return 是否处理成功
     */
    virtual bool process_batch(const std::vector<cv::Mat>& frames,
                             const std::vector<std::vector<tracking::strack>>& tracks_list,
                             ai::FrameResult& result) = 0;

    /**
     * @brief 重置插件状态
     */
    virtual void reset() = 0;

    /**
     * @brief 获取插件需要处理的帧数
     * @return 需要处理的帧数，默认为5
     */
    virtual int get_required_frames() const { return 5; }

    /**
     * @brief 获取插件类型
     * @return 插件类型
     */
    virtual std::string get_type() const { return "unknown"; }

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    virtual std::string get_description() const = 0;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    virtual std::string get_version() const = 0;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    virtual std::string get_author() const = 0;

    /**
     * @brief 设置插件参数
     * @param params 参数映射
     */
    virtual void set_params(const std::map<std::string, std::string>& params) {
        params_ = params;
        load_parameters();
    }

    /**
     * @brief 获取插件参数
     * @return 参数映射
     */
    virtual std::map<std::string, std::string> get_params() const {
        return params_;
    }

    /**
     * @brief 获取插件默认参数
     * @return 默认参数映射
     */
    virtual std::map<std::string, std::string> get_default_params() const {
        return std::map<std::string, std::string>();
    }

    /**
     * @brief 从参数加载插件配置
     * 子类应该重写此方法以从params_加载参数到内部变量
     */
    virtual void load_parameters() {}

    /**
     * @brief 获取插件在任务列表中的显示名称
     * @return 插件显示名称
     */
     virtual std::string get_display_name() const { return get_name(); }


protected:
    std::map<std::string, std::string> params_; ///< 插件参数

private:
    std::string name_;  ///< 插件名称
    bool enabled_;      ///< 是否启用
};

} // namespace plugins
} // namespace ai


