#pragma once

#include <memory>
#include <string>
#include <vector>
#include <functional>

#include "../../aivideocore_export.h"

namespace ai {
    struct FrameResult;
}

namespace core {
namespace protocols {

/**
 * @brief 结果协议接口类，定义了不同通信协议的通用接口
 */
class AIVIDEOCORE_API IResultProtocol {
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~IResultProtocol() = default;

    /**
     * @brief 启动协议服务
     * @param port 服务端口
     * @return 是否成功启动
     */
    virtual bool start(int port) = 0;

    /**
     * @brief 停止协议服务
     */
    virtual void stop() = 0;

    /**
     * @brief 发送结果数据
     * @param json_str JSON格式的结果数据
     * @return 是否成功发送
     */
    virtual bool send_result(const std::string& json_str) = 0;

    /**
     * @brief 获取连接客户端数量
     * @return 连接客户端数量
     */
    virtual int get_client_count() const = 0;

    /**
     * @brief 获取服务端口
     * @return 服务端口
     */
    virtual int get_port() const = 0;

    /**
     * @brief 获取协议名称
     * @return 协议名称
     */
    virtual std::string get_protocol_name() const = 0;

    /**
     * @brief 获取协议状态
     * @return 协议是否正在运行
     */
    virtual bool is_running() const = 0;

    /**
     * @brief 设置新连接回调函数
     * @param callback 新连接回调函数
     */
    virtual void set_new_connection_callback(std::function<void(void*)> callback) = 0;
};

/**
 * @brief 协议类型枚举
 */
enum class ProtocolType {
    TCP,        ///< TCP/IP协议
    MODBUS,     ///< Modbus协议
    MQTT,       ///< MQTT协议
    CUSTOM      ///< 自定义协议
};

/**
 * @brief 创建协议实例的工厂函数
 * @param type 协议类型
 * @return 协议实例
 */
AIVIDEOCORE_API std::shared_ptr<IResultProtocol> create_protocol(ProtocolType type);

} // namespace protocols
} // namespace core
