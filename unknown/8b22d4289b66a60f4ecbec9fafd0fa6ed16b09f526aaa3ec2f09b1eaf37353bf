#include "core/protocols/modbus_result_protocol.h"
#include "utils/log_manager.h"

#include <chrono>
#include <iostream>
#include <thread>
#include <json/json.h>

// 注意：这里使用了一个简化的Modbus实现
// 在实际项目中，应该使用完整的Modbus库，如libmodbus

namespace core {
namespace protocols {

// 简单的Modbus上下文结构
struct ModbusContext {
    int port;
    bool running;
    std::vector<uint16_t> holding_registers;  // 保持寄存器
    std::vector<uint16_t> input_registers;    // 输入寄存器
    std::vector<bool> coils;                  // 线圈
    std::vector<bool> discrete_inputs;        // 离散输入

    ModbusContext() : port(502), running(false) {
        // 初始化寄存器和线圈
        holding_registers.resize(100, 0);
        input_registers.resize(100, 0);
        coils.resize(100, false);
        discrete_inputs.resize(100, false);
    }
};

ModbusResultProtocol::ModbusResultProtocol()
    : running_(false), port_(0), client_count_(0), modbus_context_(nullptr) {
    // 创建Modbus上下文
    modbus_context_ = new ModbusContext();
}

ModbusResultProtocol::~ModbusResultProtocol() {
    stop();

    // 释放Modbus上下文
    if (modbus_context_) {
        delete static_cast<ModbusContext*>(modbus_context_);
        modbus_context_ = nullptr;
    }
}

bool ModbusResultProtocol::start(int port) {
    if (running_) {
        return true; // 已经在运行
    }

    // 设置Modbus端口
    port_ = port;

    // 初始化Modbus上下文
    ModbusContext* context = static_cast<ModbusContext*>(modbus_context_);
    context->port = port;
    context->running = true;

    // 标记为运行状态
    running_ = true;

    // 启动Modbus服务线程
    modbus_server_thread_ = std::thread(&ModbusResultProtocol::modbus_server_thread_func, this);

    LOG_INFO("Modbus协议服务已启动，端口: " + std::to_string(port_));
    return true;
}

void ModbusResultProtocol::stop() {
    if (!running_) {
        return;
    }

    // 标记为停止状态
    running_ = false;

    // 停止Modbus上下文
    if (modbus_context_) {
        ModbusContext* context = static_cast<ModbusContext*>(modbus_context_);
        context->running = false;
    }

    // 等待Modbus服务线程结束
    if (modbus_server_thread_.joinable()) {
        modbus_server_thread_.join();
    }

    LOG_INFO("Modbus协议服务已停止");
}

bool ModbusResultProtocol::send_result(const std::string& json_str) {
    if (!running_) {
        return false;
    }

    // 解析JSON并更新Modbus寄存器
    parse_json_and_update_registers(json_str);
    return true;
}

int ModbusResultProtocol::get_client_count() const {
    return client_count_;
}

int ModbusResultProtocol::get_port() const {
    return port_;
}

std::string ModbusResultProtocol::get_protocol_name() const {
    return "Modbus";
}

bool ModbusResultProtocol::is_running() const {
    return running_;
}

void ModbusResultProtocol::set_new_connection_callback(std::function<void(void*)> callback) {
    new_connection_callback_ = callback;
}

void ModbusResultProtocol::set_register_map(const std::unordered_map<std::string, uint16_t>& register_map) {
    std::lock_guard<std::mutex> lock(register_map_mutex_);
    register_map_ = register_map;
}

std::unordered_map<std::string, uint16_t> ModbusResultProtocol::get_register_map() const {
    std::lock_guard<std::mutex> lock(*const_cast<std::mutex*>(&register_map_mutex_));
    return register_map_;
}

void ModbusResultProtocol::modbus_server_thread_func() {
    ModbusContext* context = static_cast<ModbusContext*>(modbus_context_);

    LOG_INFO("Modbus服务线程已启动");

    // 模拟Modbus服务器循环
    while (running_ && context->running) {
        // 在实际实现中，这里应该处理Modbus请求
        // 例如，使用libmodbus库的modbus_receive函数

        // 模拟客户端连接
        client_count_ = 1;

        // 休眠一段时间
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_INFO("Modbus服务线程已退出");
}

void ModbusResultProtocol::parse_json_and_update_registers(const std::string& json_str) {
    if (!modbus_context_) {
        return;
    }

    ModbusContext* context = static_cast<ModbusContext*>(modbus_context_);

    try {
        // 解析JSON
        Json::Value root;
        Json::Reader reader;
        if (!reader.parse(json_str, root)) {
            LOG_ERROR("解析JSON失败: " + reader.getFormattedErrorMessages());
            return;
        }

        // 获取寄存器映射
        std::unordered_map<std::string, uint16_t> register_map;
        {
            std::lock_guard<std::mutex> lock(register_map_mutex_);
            register_map = register_map_;
        }

        // 如果没有寄存器映射，使用默认映射
        if (register_map.empty()) {
            // 默认映射：将frame_id映射到寄存器0，total_count映射到寄存器1
            register_map["frame_id"] = 0;
            register_map["total_count"] = 1;
            register_map["timestamp"] = 2;
        }

        // 更新寄存器
        std::lock_guard<std::mutex> lock(modbus_mutex_);
        for (const auto& mapping : register_map) {
            const std::string& json_field = mapping.first;
            uint16_t register_address = mapping.second;

            // 检查JSON中是否存在该字段
            if (root.isMember(json_field)) {
                // 根据JSON字段类型设置寄存器值
                if (root[json_field].isInt()) {
                    int value = root[json_field].asInt();
                    if (register_address < context->holding_registers.size()) {
                        context->holding_registers[register_address] = static_cast<uint16_t>(value);
                    }
                } else if (root[json_field].isString()) {
                    // 对于字符串，可以考虑将其转换为数值或者存储其长度
                    std::string value = root[json_field].asString();
                    if (register_address < context->holding_registers.size()) {
                        context->holding_registers[register_address] = static_cast<uint16_t>(value.length());
                    }
                } else if (root[json_field].isDouble()) {
                    // 对于浮点数，可以考虑将其转换为整数或者使用两个寄存器存储
                    double value = root[json_field].asDouble();
                    if (register_address < context->holding_registers.size()) {
                        context->holding_registers[register_address] = static_cast<uint16_t>(value);
                    }
                } else if (root[json_field].isBool()) {
                    // 对于布尔值，可以使用线圈
                    bool value = root[json_field].asBool();
                    if (register_address < context->coils.size()) {
                        context->coils[register_address] = value;
                    }
                }
            }
        }

        // 特殊处理：检查是否有class_counts字段
        if (root.isMember("class_counts") && root["class_counts"].isObject()) {
            const Json::Value& class_counts = root["class_counts"];
            int register_offset = 10; // 从寄存器10开始存储类别计数

            for (auto it = class_counts.begin(); it != class_counts.end(); ++it) {
                std::string class_name = it.key().asString();
                int count = it->asInt();

                // 将类别计数存储到寄存器中
                if (register_offset < context->holding_registers.size()) {
                    context->holding_registers[register_offset++] = static_cast<uint16_t>(count);
                }
            }
        }

        LOG_DEBUG("已更新Modbus寄存器");
    } catch (const std::exception& e) {
        LOG_ERROR("解析JSON并更新Modbus寄存器失败: " + std::string(e.what()));
    }
}

} // namespace protocols
} // namespace core
