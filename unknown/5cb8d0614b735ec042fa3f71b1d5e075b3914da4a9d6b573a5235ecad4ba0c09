#include "utils/log_manager.h"

#include <iostream>
#include <filesystem>
#include <chrono>
#include <iomanip>
#include <sstream>

#include <boost/log/core.hpp>
#include <boost/log/expressions.hpp>
#include <boost/log/sinks/text_file_backend.hpp>
#include <boost/log/sinks/text_ostream_backend.hpp>
#include <boost/log/utility/setup/file.hpp>
#include <boost/log/utility/setup/console.hpp>
#include <boost/log/utility/setup/common_attributes.hpp>
#include <boost/log/support/date_time.hpp>
#include <boost/core/null_deleter.hpp>
#include <boost/log/sinks/sync_frontend.hpp>
#include <boost/make_shared.hpp>
#include <boost/shared_ptr.hpp>

namespace logging = boost::log;
namespace sinks = boost::log::sinks;
namespace expr = boost::log::expressions;
namespace attrs = boost::log::attributes;
namespace keywords = boost::log::keywords;

namespace utils {

LogManager& LogManager::get_instance() {
    static LogManager instance;
    return instance;
}

LogManager::LogManager()
    : initialized_(false), console_level_(LogLevel::Info), file_level_(LogLevel::Debug) {
}

LogManager::~LogManager() {
}

void LogManager::initialize(const std::string& app_name,
                            LogLevel console_level,
                            LogLevel file_level,
                            const std::string& log_dir) {
    if (initialized_) {
        return;
    }

    app_name_ = app_name;
    log_dir_ = log_dir;
    console_level_ = console_level;
    file_level_ = file_level;

    // 确保日志目录存在
    if (!ensure_directory_exists(log_dir_)) {
        std::cerr << "Failed to create log directory: " << log_dir_ << std::endl;
        return;
    }

    // 添加通用属性
    logging::add_common_attributes();

    // 获取当前日期时间作为文件名的一部分
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_time_t);

    std::ostringstream date_stream;
    date_stream << std::put_time(&now_tm, "%Y%m%d_%H%M%S");
    std::string timestamp = date_stream.str();

    // 设置文件名格式
    std::string file_name_pattern = log_dir_ + "/" + app_name_ + "_" + timestamp + "_%N.log";

    // 创建文件日志接收器
    auto file_sink = logging::add_file_log(
        keywords::file_name = file_name_pattern,
        keywords::rotation_size = 10 * 1024 * 1024, // 10 MB
        keywords::time_based_rotation = sinks::file::rotation_at_time_point(0, 0, 0), // 每天午夜轮换
        keywords::format = (
            expr::stream
                << expr::format_date_time<boost::posix_time::ptime>("TimeStamp", "%Y-%m-%d %H:%M:%S.%f")
                << " [" << expr::attr<logging::trivial::severity_level>("Severity") << "] "
                << expr::smessage
        ),
        keywords::auto_flush = true
    );

    // 设置文件日志级别
    file_sink->set_filter(
        logging::trivial::severity >= to_boost_level(file_level_)
    );

    // 创建控制台日志接收器
    auto console_sink = logging::add_console_log(
        std::cout,
        keywords::format = (
            expr::stream
                << expr::format_date_time<boost::posix_time::ptime>("TimeStamp", "%Y-%m-%d %H:%M:%S.%f")
                << " [" << expr::attr<logging::trivial::severity_level>("Severity") << "] "
                << expr::smessage
        )
    );

    // 设置控制台日志级别
    console_sink->set_filter(
        logging::trivial::severity >= to_boost_level(console_level_)
    );

    initialized_ = true;

    // 记录初始化成功的日志
    info("Log system initialized. App: " + app_name_ +
         ", Console level: " + level_to_string(console_level_) +
         ", File level: " + level_to_string(file_level_), __FILE__, __LINE__);
}

void LogManager::set_console_level(LogLevel level) {
    console_level_ = level;

    // 更新控制台日志级别
    auto core = logging::core::get();

    // 重新创建控制台接收器
    auto console_sink = logging::add_console_log(
        std::cout,
        keywords::format = (
            expr::stream
                << expr::format_date_time<boost::posix_time::ptime>("TimeStamp", "%Y-%m-%d %H:%M:%S.%f")
                << " [" << expr::attr<logging::trivial::severity_level>("Severity") << "] "
                << expr::smessage
        )
    );

    // 设置新的过滤器
    console_sink->set_filter(
        logging::trivial::severity >= to_boost_level(level)
    );

    info("Console log level changed to: " + level_to_string(level), __FILE__, __LINE__);
}

void LogManager::set_file_level(LogLevel level) {
    file_level_ = level;

    // 更新文件日志级别
    auto core = logging::core::get();

    // 获取当前日期时间作为文件名的一部分
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_time_t);

    std::ostringstream date_stream;
    date_stream << std::put_time(&now_tm, "%Y%m%d_%H%M%S");
    std::string timestamp = date_stream.str();

    // 设置文件名格式
    std::string file_name_pattern = log_dir_ + "/" + app_name_ + "_" + timestamp + "_%N.log";

    // 创建新的文件日志接收器
    auto file_sink = logging::add_file_log(
        keywords::file_name = file_name_pattern,
        keywords::rotation_size = 10 * 1024 * 1024, // 10 MB
        keywords::time_based_rotation = sinks::file::rotation_at_time_point(0, 0, 0), // 每天午夜轮换
        keywords::format = (
            expr::stream
                << expr::format_date_time<boost::posix_time::ptime>("TimeStamp", "%Y-%m-%d %H:%M:%S.%f")
                << " [" << expr::attr<logging::trivial::severity_level>("Severity") << "] "
                << expr::smessage
        ),
        keywords::auto_flush = true
    );

    // 设置新的过滤器
    file_sink->set_filter(
        logging::trivial::severity >= to_boost_level(level)
    );

    info("File log level changed to: " + level_to_string(level), __FILE__, __LINE__);
}

LogLevel LogManager::get_console_level() const {
    return console_level_;
}

LogLevel LogManager::get_file_level() const {
    return file_level_;
}

void LogManager::trace(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::trace) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::trace) << message << "\n";
        }
    }
}

void LogManager::debug(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::debug) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::debug) << message << "\n";
        }
    }
}

void LogManager::info(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::info) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::info) << message << "\n";
        }
    }
}

void LogManager::warning(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::warning) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::warning) << message << "\n";
        }
    }
}

void LogManager::error(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::error) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::error) << message << "\n";
        }
    }
}

void LogManager::fatal(const std::string& message, const char* file, int line) {
    if (initialized_) {
        if (file && line > 0) {
            BOOST_LOG_SEV(logger_, logging::trivial::fatal) << "[" << file << ":" << line << "] " << message << "\n";
        } else {
            BOOST_LOG_SEV(logger_, logging::trivial::fatal) << message << "\n";
        }
    }
}

boost::log::trivial::severity_level LogManager::to_boost_level(LogLevel level) {
    switch (level) {
        case LogLevel::Trace:
            return logging::trivial::trace;
        case LogLevel::Debug:
            return logging::trivial::debug;
        case LogLevel::Info:
            return logging::trivial::info;
        case LogLevel::Warning:
            return logging::trivial::warning;
        case LogLevel::Error:
            return logging::trivial::error;
        case LogLevel::Fatal:
            return logging::trivial::fatal;
        default:
            return logging::trivial::info;
    }
}

std::string LogManager::level_to_string(LogLevel level) {
    switch (level) {
        case LogLevel::Trace:
            return "Trace";
        case LogLevel::Debug:
            return "Debug";
        case LogLevel::Info:
            return "Info";
        case LogLevel::Warning:
            return "Warning";
        case LogLevel::Error:
            return "Error";
        case LogLevel::Fatal:
            return "Fatal";
        default:
            return "Unknown";
    }
}

LogLevel LogManager::string_to_level(const std::string& level_str) {
    if (level_str == "Trace") {
        return LogLevel::Trace;
    } else if (level_str == "Debug") {
        return LogLevel::Debug;
    } else if (level_str == "Info") {
        return LogLevel::Info;
    } else if (level_str == "Warning") {
        return LogLevel::Warning;
    } else if (level_str == "Error") {
        return LogLevel::Error;
    } else if (level_str == "Fatal") {
        return LogLevel::Fatal;
    } else {
        return LogLevel::Info; // 默认为Info级别
    }
}

bool LogManager::ensure_directory_exists(const std::string& dir_path) {
    try {
        if (!std::filesystem::exists(dir_path)) {
            return std::filesystem::create_directories(dir_path);
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error creating directory " << dir_path << ": " << e.what() << std::endl;
        return false;
    }
}

} // namespace utils
