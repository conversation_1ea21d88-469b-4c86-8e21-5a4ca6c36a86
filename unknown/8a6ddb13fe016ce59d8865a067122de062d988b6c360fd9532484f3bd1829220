#pragma once

#include <string>
#include <map>
#include <vector>
#include <unordered_map>
#include "../aivideocore_export.h"
#include "protocols/result_protocol.h"

namespace core {

/**
 * @brief 项目类，存储项目相关信息
 */
class AIVIDEOCORE_API Project {
public:
    /**
     * @brief 构造函数
     */
    Project();

    /**
     * @brief 析构函数
     */
    ~Project();

    /**
     * @brief 设置项目名称
     * @param name 项目名称
     */
    void set_name(const std::string& name);

    /**
     * @brief 获取项目名称
     * @return 项目名称
     */
    std::string get_name() const;

    /**
     * @brief 设置项目文件路径
     * @param path 项目文件路径
     */
    void set_file_path(const std::string& path);

    /**
     * @brief 获取项目文件路径
     * @return 项目文件路径
     */
    std::string get_file_path() const;

    /**
     * @brief 设置视频文件路径
     * @param path 视频文件路径
     */
    void set_video_path(const std::string& path);

    /**
     * @brief 获取视频文件路径
     * @return 视频文件路径
     */
    std::string get_video_path() const;

    /**
     * @brief 设置模型文件路径
     * @param path 模型文件路径
     */
    void set_model_path(const std::string& path);

    /**
     * @brief 获取模型文件路径
     * @return 模型文件路径
     */
    std::string get_model_path() const;

    /**
     * @brief 设置输入节点ID
     * @param id 输入节点ID
     */
    void set_input_node_id(const std::string& id);

    /**
     * @brief 获取输入节点ID
     * @return 输入节点ID
     */
    std::string get_input_node_id() const;

    /**
     * @brief 设置输出节点ID
     * @param id 输出节点ID
     */
    void set_output_node_id(const std::string& id);

    /**
     * @brief 获取输出节点ID
     * @return 输出节点ID
     */
    std::string get_output_node_id() const;

    /**
     * @brief 设置提示词
     * @param prompt 提示词
     */
    void set_prompt(const std::string& prompt);

    /**
     * @brief 获取提示词
     * @return 提示词
     */
    std::string get_prompt() const;

    /**
     * @brief 设置置信度阈值
     * @param score 置信度阈值
     */
    void set_score_threshold(double score);

    /**
     * @brief 获取置信度阈值
     * @return 置信度阈值
     */
    double get_score_threshold() const;

    /**
     * @brief 设置IOU阈值
     * @param iou IOU阈值
     */
    void set_iou_threshold(int iou);

    /**
     * @brief 获取IOU阈值
     * @return IOU阈值
     */
    int get_iou_threshold() const;

    /**
     * @brief 设置帧跳过间隔
     * @param interval 帧跳过间隔
     */
    void set_frame_skip_interval(int interval);

    /**
     * @brief 获取帧跳过间隔
     * @return 帧跳过间隔
     */
    int get_frame_skip_interval() const;

    /**
     * @brief 设置录制输出路径
     * @param path 录制输出路径
     */
    void set_recording_output_path(const std::string& path);

    /**
     * @brief 获取录制输出路径
     * @return 录制输出路径
     */
    std::string get_recording_output_path() const;

    /**
     * @brief 设置是否启用结果存储
     * @param enable 是否启用
     */
    void set_enable_result_storage(bool enable);

    /**
     * @brief 获取是否启用结果存储
     * @return 是否启用
     */
    bool get_enable_result_storage() const;

    /**
     * @brief 设置结果存储TCP端口
     * @param port 端口号
     */
    void set_result_storage_tcp_port(int port);

    /**
     * @brief 获取结果存储TCP端口
     * @return 端口号
     */
    int get_result_storage_tcp_port() const;

    /**
     * @brief 设置结果存储模式
     * @param mode 存储模式
     */
    void set_result_storage_mode(int mode);

    /**
     * @brief 获取结果存储模式
     * @return 存储模式
     */
    int get_result_storage_mode() const;

    /**
     * @brief 设置结果存储刷新间隔
     * @param interval 刷新间隔
     */
    void set_result_storage_flush_interval(int interval);

    /**
     * @brief 获取结果存储刷新间隔
     * @return 刷新间隔
     */
    int get_result_storage_flush_interval() const;

    /**
     * @brief 设置结果存储协议类型
     * @param type 协议类型
     */
    void set_result_storage_protocol_type(protocols::ProtocolType type);

    /**
     * @brief 获取结果存储协议类型
     * @return 协议类型
     */
    protocols::ProtocolType get_result_storage_protocol_type() const;

    /**
     * @brief 设置Modbus寄存器映射
     * @param register_map 寄存器映射表
     */
    void set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map);

    /**
     * @brief 获取Modbus寄存器映射
     * @return 寄存器映射表
     */
    std::unordered_map<std::string, uint16_t> get_modbus_register_map() const;

    /**
     * @brief 设置最大帧历史数量
     * @param count 最大帧数
     */
    void set_max_frame_history(int count);

    /**
     * @brief 获取最大帧历史数量
     * @return 最大帧数
     */
    int get_max_frame_history() const;

    /**
     * @brief 设置用户定义的类别名称
     * @param classNames 类别名称列表
     */
    void set_user_defined_class_names(const std::vector<std::string>& classNames);

    /**
     * @brief 获取用户定义的类别名称
     * @return 类别名称列表
     */
    std::vector<std::string> get_user_defined_class_names() const;

    /**
     * @brief 设置插件参数
     * @param pluginName 插件名称
     * @param params 参数映射
     */
    void set_plugin_params(const std::string& pluginName, const std::map<std::string, std::string>& params);

    /**
     * @brief 获取插件参数
     * @param pluginName 插件名称
     * @return 参数映射
     */
    std::map<std::string, std::string> get_plugin_params(const std::string& pluginName) const;

    /**
     * @brief 获取所有插件参数
     * @return 所有插件的参数映射
     */
    const std::map<std::string, std::map<std::string, std::string>>& get_all_plugin_params() const;

    /**
     * @brief 设置启用的插件列表
     * @param plugins 插件名称列表
     */
    void set_enabled_plugins(const std::vector<std::string>& plugins);

    /**
     * @brief 获取启用的插件列表
     * @return 插件名称列表
     */
    std::vector<std::string> get_enabled_plugins() const;

    /**
     * @brief 设置帧处理器脚本路径
     * @param path 脚本路径
     */
    void set_frame_processor_script_path(const std::string& path);

    /**
     * @brief 获取帧处理器脚本路径
     * @return 脚本路径
     */
    std::string get_frame_processor_script_path() const;

    /**
     * @brief 设置帧处理器是否启用
     * @param enabled 是否启用
     */
    void set_frame_processor_enabled(bool enabled);

    /**
     * @brief 获取帧处理器是否启用
     * @return 是否启用
     */
    bool get_frame_processor_enabled() const;

    /**
     * @brief 设置帧处理器参数
     * @param params 参数映射
     */
    void set_frame_processor_params(const std::map<std::string, std::string>& params);

    /**
     * @brief 获取帧处理器参数
     * @return 参数映射
     */
    std::map<std::string, std::string> get_frame_processor_params() const;

    /**
     * @brief 设置是否使用C++帧处理器
     * @param use 是否使用
     */
    void set_use_cpp_frame_processor(bool use);

    /**
     * @brief 获取是否使用C++帧处理器
     * @return 是否使用
     */
    bool get_use_cpp_frame_processor() const;

    /**
     * @brief 设置C++帧处理器插件名称
     * @param name 插件名称
     */
    void set_cpp_frame_processor_plugin_name(const std::string& name);

    /**
     * @brief 获取C++帧处理器插件名称
     * @return 插件名称
     */
    std::string get_cpp_frame_processor_plugin_name() const;

    /**
     * @brief 从JSON字符串加载项目
     * @param jsonStr JSON字符串
     * @return 是否加载成功
     */
    bool load_from_json_string(const std::string& jsonStr);

    /**
     * @brief 导出为JSON字符串
     * @return JSON字符串
     */
    std::string to_json_string() const;

    /**
     * @brief 从文件加载项目
     * @param filePath 文件路径
     * @return 是否加载成功
     */
    bool load_from_file(const std::string& filePath);

    /**
     * @brief 保存项目到文件
     * @param filePath 文件路径
     * @return 是否保存成功
     */
    bool save_to_file(const std::string& filePath);

private:
    std::string name_;                                  ///< 项目名称
    std::string file_path_;                             ///< 项目文件路径
    std::string video_path_;                            ///< 视频文件路径
    std::string model_path_;                            ///< 模型文件路径
    std::string input_node_id_;                         ///< 输入节点ID
    std::string output_node_id_;                        ///< 输出节点ID
    std::string prompt_;                                ///< 提示词
    double score_threshold_;                            ///< 置信度阈值
    int iou_threshold_;                                 ///< IOU阈值
    int frame_skip_interval_;                           ///< 帧跳过间隔
    std::string recording_output_path_;                 ///< 录制输出路径
    bool enable_result_storage_;                        ///< 是否启用结果存储
    int result_storage_tcp_port_;                       ///< 结果存储TCP端口
    int result_storage_mode_;                           ///< 结果存储模式
    int result_storage_flush_interval_;                 ///< 结果存储刷新间隔
    protocols::ProtocolType result_storage_protocol_type_; ///< 结果存储协议类型
    std::unordered_map<std::string, uint16_t> modbus_register_map_; ///< Modbus寄存器映射
    int max_frame_history_;                             ///< 最大帧历史数量
    std::vector<std::string> user_defined_class_names_; ///< 用户定义的类别名称
    std::map<std::string, std::map<std::string, std::string>> plugin_params_; ///< 插件参数
    std::vector<std::string> enabled_plugins_;          ///< 启用的插件列表
    std::string frame_processor_script_path_;           ///< 帧处理器脚本路径
    bool frame_processor_enabled_;                      ///< 帧处理器是否启用
    std::map<std::string, std::string> frame_processor_params_; ///< 帧处理器参数
    bool use_cpp_frame_processor_;                      ///< 是否使用C++帧处理器
    std::string cpp_frame_processor_plugin_name_;       ///< C++帧处理器插件名称
};

} // namespace core
