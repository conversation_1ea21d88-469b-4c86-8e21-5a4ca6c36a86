﻿#include "utils/video_frame_provider.h"

#include <filesystem>
#include <iomanip>
#include <sstream>

#include "utils/string_utils.h"
#include "utils/log_manager.h"

namespace utils {

VideoFrameProvider::VideoFrameProvider()
    : currentFrame(0), totalFrames(0), fps(0.0), fromCamera(false),
      rtspUrl(""), videoPath(""), readTimeoutMs(5000), reconnectInterval(3),
      maxReconnectAttempts(5), accelType(HardwareAccelType::AUTO), bufferSize(30),
      prefetchEnabled(false), decodeThreadCount(0), rtspThreadRunning(false),
      newFrameAvailable(false), connectionLost(false), prefetchThreadRunning(false),
      queueSize(0) {
}

VideoFrameProvider::~VideoFrameProvider() {
    // 先停止预解码线程
    stop_prefetch_thread();

    // 停止RTSP线程
    stop_rtsp_thread();

    // 关闭视频源
    if (videoCapture.isOpened()) {
        videoCapture.release();
    }
}

bool VideoFrameProvider::open_video_file(const std::string& filePath) {
    LOG_INFO("Opening video file: " + filePath);

    // 关闭之前打开的视频
    close();

    // 标记为文件视频来源
    fromCamera = false;
    videoPath = filePath;  // 保存视频路径

    // 处理中文路径
    std::string path;
#ifdef _WIN32
    // Windows下使用本地编码打开文件
    path = utf8ToAnsi(filePath);
#else
    // 其他系统直接使用UTF-8
    path = filePath;
#endif

    // 配置硬件加速
    bool opened = false;

    // 首先尝试使用硬件加速打开
    if (accelType != HardwareAccelType::NONE) {
        // 使用参数方式打开视频文件，以便设置硬件加速
        int cvAccelType = convert_to_cv_accel_type(accelType);
        LOG_DEBUG("Trying hardware acceleration type: " + std::to_string(cvAccelType));

        try {
            videoCapture.open(path, cv::CAP_FFMPEG, {
                cv::CAP_PROP_HW_ACCELERATION, cvAccelType,
                cv::CAP_PROP_BUFFERSIZE, bufferSize,
                cv::CAP_PROP_READ_TIMEOUT_MSEC, readTimeoutMs
            });

            if (videoCapture.isOpened()) {
                opened = true;
                LOG_INFO("Successfully opened video with hardware acceleration");
            } else {
                LOG_WARNING("Failed to open video with hardware acceleration, will try software decoding");
            }
        } catch (const cv::Exception& e) {
            LOG_WARNING("OpenCV exception while opening video with hardware acceleration: " + std::string(e.what()) + ", will try software decoding");
        } catch (const std::exception& e) {
            LOG_WARNING("Exception while opening video with hardware acceleration: " + std::string(e.what()) + ", will try software decoding");
        }
    }

    // 如果硬件加速失败，尝试使用软件解码
    if (!opened) {
        LOG_INFO("Trying to open video with software decoding");
        try {
            // 确保先释放之前的VideoCapture实例
            if (videoCapture.isOpened()) {
                videoCapture.release();
            }

            // 使用软件解码打开视频
            videoCapture.open(path);

            if (videoCapture.isOpened()) {
                opened = true;
                LOG_INFO("Successfully opened video with software decoding");
            } else {
                LOG_ERROR("Failed to open video with software decoding");
                return false;
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while opening video with software decoding: " + std::string(e.what()));
            return false;
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while opening video with software decoding: " + std::string(e.what()));
            return false;
        }
    }

    if (!videoCapture.isOpened()) {
        LOG_ERROR("Failed to open video file: " + filePath);
        return false;
    }

    // 配置缓冲区大小
    videoCapture.set(cv::CAP_PROP_BUFFERSIZE, bufferSize);

    // 获取视频属性
    double width = videoCapture.get(cv::CAP_PROP_FRAME_WIDTH);
    double height = videoCapture.get(cv::CAP_PROP_FRAME_HEIGHT);

    // 获取视频总帧数
    totalFrames = static_cast<int>(videoCapture.get(cv::CAP_PROP_FRAME_COUNT));
    fps = videoCapture.get(cv::CAP_PROP_FPS);

    LOG_INFO("Video properties - Width: " + std::to_string(width) +
             ", Height: " + std::to_string(height) +
             ", Total frames: " + std::to_string(totalFrames) +
             ", FPS: " + std::to_string(fps));

    if (totalFrames <= 0) {
        LOG_ERROR("Invalid video file: total frames is 0");
        close();
        return false;
    }

    // 重置当前帧索引
    currentFrame = 0;

    // 如果启用了预解码，启动预解码线程
    if (prefetchEnabled) {
        start_prefetch_thread();
    }

    // 检查是否成功启用了硬件加速
    int actualAccelType = videoCapture.get(cv::CAP_PROP_HW_ACCELERATION);
    HardwareAccelType actualType = convert_from_cv_accel_type(actualAccelType);
    LOG_INFO("Hardware acceleration: " + std::string(actualType != HardwareAccelType::NONE ? "Enabled" : "Disabled"));

    LOG_INFO("Video file opened successfully: " + filePath);
    return true;
}

bool VideoFrameProvider::open_camera(int cameraId) {
    LOG_INFO("Opening camera with ID: " + std::to_string(cameraId));

    // 关闭之前打开的视频
    close();

    // 标记为摄像头视频来源
    fromCamera = true;

    // 打开摄像头
    try {
        videoCapture.open(cameraId);
    } catch (const cv::Exception& e) {
        LOG_ERROR("OpenCV exception while opening camera: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR("Exception while opening camera: " + std::string(e.what()));
        return false;
    }

    if (!videoCapture.isOpened()) {
        LOG_ERROR("Failed to open camera with ID: " + std::to_string(cameraId));
        return false;
    }

    // 尝试设置摄像头分辨率为720p
    videoCapture.set(cv::CAP_PROP_FRAME_WIDTH, 1280);
    videoCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 720);

    // 获取摄像头实际分辨率
    double width = videoCapture.get(cv::CAP_PROP_FRAME_WIDTH);
    double height = videoCapture.get(cv::CAP_PROP_FRAME_HEIGHT);

    // 获取摄像头帧率
    fps = videoCapture.get(cv::CAP_PROP_FPS);
    if (fps <= 0) {
        LOG_WARNING("Invalid FPS value from camera, using default 30fps");
        fps = 30.0; // 如果无法获取，假设为30fps
    }

    LOG_INFO("Camera properties - Width: " + std::to_string(width) +
             ", Height: " + std::to_string(height) +
             ", FPS: " + std::to_string(fps));

    // 由于摄像头没有总帧数
    totalFrames = 0;
    currentFrame = 0;

    LOG_INFO("Camera opened successfully");
    return true;
}

bool VideoFrameProvider::open_rtsp_stream(const std::string& url) {
    LOG_INFO("Opening RTSP stream: " + url);

    // 关闭之前打开的视频
    close();

    // 标记为摄像头视频来源（对RTSP流也使用相同的处理方式）
    fromCamera = true;

    // 设置RTSP流的参数
    rtspUrl = url;

    // 确保URL格式正确
    if (rtspUrl.empty()) {
        LOG_ERROR("RTSP URL is empty");
        return false;
    }

    // 添加RTSP优化参数
    if (rtspUrl.find("rtsp://") == 0) {
        // 如果URL中没有参数，添加TCP传输参数
        if (rtspUrl.find("?") == std::string::npos) {
            rtspUrl += "?rtsp_transport=tcp";  // 使用TCP传输，更稳定
            rtspUrl += "&buffer_size=1024000";  // 设置较大的缓冲区
            rtspUrl += "&max_delay=500";        // 最大延迟500ms
            rtspUrl += "&drop_if_late=1";      // 如果帧太晚到达则丢弃
        }
        // 如果URL已有参数但没有指定传输协议，添加TCP传输参数
        else if (rtspUrl.find("rtsp_transport=") == std::string::npos) {
            rtspUrl += "&rtsp_transport=tcp";
        }
    } else {
        LOG_ERROR("Invalid RTSP URL format: " + rtspUrl);
        return false;
    }

    LOG_INFO("Configured RTSP URL: " + rtspUrl);

    // 尝试多次打开RTSP流
    bool opened = false;

    // 首先尝试使用硬件加速
    if (accelType != HardwareAccelType::NONE) {
        int cvAccelType = convert_to_cv_accel_type(accelType);
        LOG_DEBUG("Trying hardware acceleration type: " + std::to_string(cvAccelType));

        for (int attempt = 1; attempt <= 2; attempt++) {
            LOG_INFO("Attempt " + std::to_string(attempt) + " to open RTSP stream with hardware acceleration");

            try {
                // 使用参数方式打开，设置硬件加速和缓冲区大小
                videoCapture.open(rtspUrl, cv::CAP_FFMPEG, {
                    cv::CAP_PROP_HW_ACCELERATION, cvAccelType,
                    cv::CAP_PROP_BUFFERSIZE, bufferSize,
                    cv::CAP_PROP_OPEN_TIMEOUT_MSEC, readTimeoutMs,
                    cv::CAP_PROP_READ_TIMEOUT_MSEC, readTimeoutMs,
                    cv::CAP_PROP_FOURCC, cv::VideoWriter::fourcc('H', '2', '6', '4'),
                    cv::CAP_PROP_FPS, 30 // 尝试设置帧率为30fps
                });
            } catch (const cv::Exception& e) {
                LOG_WARNING("OpenCV exception while opening RTSP stream with hardware acceleration: " + std::string(e.what()));
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            } catch (const std::exception& e) {
                LOG_WARNING("Exception while opening RTSP stream with hardware acceleration: " + std::string(e.what()));
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            if (videoCapture.isOpened()) {
                opened = true;
                LOG_INFO("Successfully opened RTSP stream with hardware acceleration");
                break;
            }

            LOG_WARNING("Failed to open RTSP stream with hardware acceleration on attempt " + std::to_string(attempt));
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    // 如果硬件加速失败，尝试使用软件解码
    if (!opened) {
        LOG_INFO("Trying to open RTSP stream with software decoding");

        // 确保先释放之前的VideoCapture实例
        if (videoCapture.isOpened()) {
            videoCapture.release();
        }

        for (int attempt = 1; attempt <= 2; attempt++) {
            LOG_INFO("Attempt " + std::to_string(attempt) + " to open RTSP stream with software decoding");

            try {
                // 使用软件解码打开RTSP流
                videoCapture.open(rtspUrl);
            } catch (const cv::Exception& e) {
                LOG_WARNING("OpenCV exception while opening RTSP stream with software decoding: " + std::string(e.what()));
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            } catch (const std::exception& e) {
                LOG_WARNING("Exception while opening RTSP stream with software decoding: " + std::string(e.what()));
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            if (videoCapture.isOpened()) {
                opened = true;
                LOG_INFO("Successfully opened RTSP stream with software decoding");
                break;
            }

            LOG_WARNING("Failed to open RTSP stream with software decoding on attempt " + std::to_string(attempt));
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    if (!opened) {
        LOG_ERROR("Failed to open RTSP stream after multiple attempts: " + rtspUrl);
        return false;
    }

    // 获取视频属性
    double width = videoCapture.get(cv::CAP_PROP_FRAME_WIDTH);
    double height = videoCapture.get(cv::CAP_PROP_FRAME_HEIGHT);
    fps = videoCapture.get(cv::CAP_PROP_FPS);

    LOG_INFO("RTSP stream properties - Width: " + std::to_string(width) +
             ", Height: " + std::to_string(height) +
             ", FPS: " + std::to_string(fps));

    if (fps <= 0) {
        LOG_WARNING("Invalid FPS value from RTSP stream, using default 30fps");
        fps = 30.0; // 如果无法获取，假设为30fps
    }

    // 由于RTSP流没有总帧数
    totalFrames = 0;
    currentFrame = 0;

    // 尝试预读取一帧以确保连接正常
    cv::Mat testFrame;
    bool readSuccess = false;

    // 尝试多次读取第一帧
    for (int attempt = 1; attempt <= 3; attempt++) {
        try {
            LOG_DEBUG("Attempting to read first frame, attempt " + std::to_string(attempt));
            if (videoCapture.grab() && videoCapture.retrieve(testFrame) && !testFrame.empty()) {
                readSuccess = true;
                break;
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while reading first frame: " + std::string(e.what()));
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while reading first frame: " + std::string(e.what()));
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    if (!readSuccess) {
        LOG_WARNING("Could not read first frame from RTSP stream, creating placeholder");
        // 创建一个灰色的测试帧
        testFrame = cv::Mat(480, 640, CV_8UC3, cv::Scalar(128, 128, 128));
        // 在帧上添加文本说明连接中
        cv::putText(testFrame, "Connecting to RTSP stream...", cv::Point(50, 240),
                    cv::FONT_HERSHEY_SIMPLEX, 1.0, cv::Scalar(255, 255, 255), 2);
    } else {
        LOG_INFO("Successfully read first frame from RTSP stream");
    }

    // 初始化帧缓冲区
    {
        std::lock_guard<std::mutex> lock(frameMutex);
        latestFrame = testFrame.clone();
        newFrameAvailable = true;
    }

    // 重置连接状态
    connectionLost = false;

    // 启动RTSP读取线程
    start_rtsp_thread();

    // 如果启用了预解码，启动预解码线程
    if (prefetchEnabled) {
        start_prefetch_thread();
    }

    // 检查是否成功启用了硬件加速
    int actualAccelType = videoCapture.get(cv::CAP_PROP_HW_ACCELERATION);
    utils::HardwareAccelType actualType = convert_from_cv_accel_type(actualAccelType);
    LOG_INFO("Hardware acceleration: " + std::string(actualType != utils::HardwareAccelType::NONE ? "Enabled" : "Disabled"));

    LOG_INFO("RTSP stream opened successfully");
    return true;
}

void VideoFrameProvider::close() {
    // 停止预解码线程
    stop_prefetch_thread();

    // 停止RTSP线程
    stop_rtsp_thread();

    // 释放视频捕获对象
    if (videoCapture.isOpened()) {
        videoCapture.release();
    }

    // 重置参数
    currentFrame = 0;
    totalFrames = 0;
    fps = 0.0;
    rtspUrl = "";
    videoPath = "";  // 清空视频路径
    connectionLost = false;

    // 清空帧缓冲区
    {
        std::lock_guard<std::mutex> lock(frameMutex);
        latestFrame = cv::Mat();
        newFrameAvailable = false;
    }

    // 清空预解码队列
    {
        std::lock_guard<std::mutex> lock(queueMutex);
        std::queue<cv::Mat> empty;
        std::swap(frameQueue, empty);
        queueSize = 0;
    }
}

bool VideoFrameProvider::read_frame(cv::Mat& frame) {
    if (!videoCapture.isOpened() && rtspUrl.empty()) {
        LOG_DEBUG("Video not opened");
        return false;
    }

    // 如果启用了预解码，优先从预解码队列中获取帧
    if (prefetchEnabled) {
        std::unique_lock<std::mutex> lock(queueMutex);

        // 如果队列不为空，从队列中获取帧
        if (!frameQueue.empty()) {
            frame = frameQueue.front().clone(); // 使用clone确保安全复制
            frameQueue.pop();
            queueSize--;

            // 如果是视频文件，更新当前帧计数
            if (!fromCamera) {
                currentFrame++;
            }

            // 通知预解码线程可以继续填充队列
            queueCV.notify_one();

            return true;
        }

        // 如果队列为空，释放锁，继续使用常规方式读取
        lock.unlock();
    }

    // 对于RTSP流，从帧缓冲区获取帧
    if (fromCamera && !rtspUrl.empty()) {
        // 检查连接状态
        if (connectionLost) {
            LOG_WARNING("RTSP connection lost");
            return false;
        }

        // 从帧缓冲区获取最新帧
        std::lock_guard<std::mutex> lock(frameMutex);

        if (newFrameAvailable && !latestFrame.empty()) {
            // 有新帧可用
            try {
                frame = latestFrame.clone();
                return true;
            } catch (const cv::Exception& e) {
                LOG_ERROR("OpenCV exception while cloning frame: " + std::string(e.what()));
                return false;
            } catch (const std::exception& e) {
                LOG_ERROR("Exception while cloning frame: " + std::string(e.what()));
                return false;
            }
        } else {
            // 没有新帧可用
            LOG_DEBUG("No new frame available from RTSP stream");
            return false;
        }
    } else {
        // 对于普通视频文件或摄像头，使用正常的读取方式
        bool success = false;
        try {
            // 使用grab和retrieve分离，可以减少延迟
            if (videoCapture.grab()) {
                success = videoCapture.retrieve(frame);
            }

            if (success && !fromCamera) {
                // 对于文件视频，更新当前帧计数
                currentFrame++;
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while reading frame: " + std::string(e.what()));
            success = false;
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while reading frame: " + std::string(e.what()));
            success = false;
        }

        return success;
    }
}

void VideoFrameProvider::set_frame_position(int frameIndex) {
    if (videoCapture.isOpened() && !fromCamera) {
        videoCapture.set(cv::CAP_PROP_POS_FRAMES, frameIndex);
        currentFrame = frameIndex;
    }
}

int VideoFrameProvider::get_total_frames() const {
    return totalFrames;
}

int VideoFrameProvider::get_current_frame() const {
    return currentFrame;
}

std::string VideoFrameProvider::get_rtsp_url() const {
    return rtspUrl;
}

double VideoFrameProvider::get_fps() const {
    return fps;
}

std::string VideoFrameProvider::get_video_path() const {
    return videoPath;
}

// RTSP线程相关方法
void VideoFrameProvider::start_rtsp_thread() {
    // 先停止已存在的线程
    stop_rtsp_thread();

    // 初始化线程状态
    rtspThreadRunning = true;
    connectionLost = false;

    // 启动新线程
    rtspThread = std::thread(&VideoFrameProvider::rtsp_thread_func, this);

    LOG_INFO("RTSP thread started");
}

void VideoFrameProvider::stop_rtsp_thread() {
    // 停止线程
    if (rtspThreadRunning) {
        LOG_DEBUG("Stopping RTSP thread...");

        // 先设置停止标志
        rtspThreadRunning = false;

        // 通知所有等待的线程
        frameCV.notify_all();

        // 等待线程结束，设置超时防止死锁
        if (rtspThread.joinable()) {
            LOG_DEBUG("Waiting for RTSP thread to join...");

            // 使用超时等待，避免永久阻塞
            std::chrono::seconds timeout(5); // 增加超时时间到5秒

            // 创建一个临时线程来等待rtspThread结束
            std::thread temp(std::move(rtspThread));

            if (temp.joinable()) {
                auto future = std::async(std::launch::async, [&temp]() {
                    temp.join();
                });

                if (future.wait_for(timeout) == std::future_status::timeout) {
                    // 如果超时，记录警告但不强制结束线程
                    LOG_WARNING("RTSP thread did not exit cleanly within timeout period");
                } else {
                    LOG_DEBUG("RTSP thread joined successfully");
                }
            }
        }

        // 释放视频捕获，防止read阻塞
        // 注意：这里移动到线程join之后，避免在线程还在运行时释放资源
        if (videoCapture.isOpened()) {
            LOG_DEBUG("Releasing video capture in stop_rtsp_thread");
            videoCapture.release();
        }

        // 重置连接状态
        connectionLost = false;
        newFrameAvailable = false;

        // 清空最新帧
        {
            std::lock_guard<std::mutex> lock(frameMutex);
            latestFrame = cv::Mat();
        }

        LOG_INFO("RTSP thread stopped");
    }
}

void VideoFrameProvider::rtsp_thread_func() {
    LOG_INFO("RTSP thread function started");

    // 重连计数器
    int reconnectCount = 0;
    int consecutiveReadFailures = 0;
    const int MAX_CONSECUTIVE_READ_FAILURES = 10; // 连续读取失败的最大次数

    while (rtspThreadRunning) {
        // 检查是否需要退出
        if (!rtspThreadRunning) {
            LOG_DEBUG("RTSP thread exit requested");
            break;
        }

        // 检查视频源是否打开
        if (!videoCapture.isOpened()) {
            LOG_WARNING("Video source not opened in RTSP thread");

            // 检查是否需要退出
            if (!rtspThreadRunning) {
                break;
            }

            // 标记连接丢失
            connectionLost = true;

            // 尝试重新连接
            if (reconnectCount < maxReconnectAttempts && !rtspUrl.empty() && rtspThreadRunning) {
                reconnectCount++;
                LOG_INFO("Attempting to reconnect to RTSP stream (" + std::to_string(reconnectCount) +
                         "/" + std::to_string(maxReconnectAttempts) + "): " + rtspUrl);

                // 重新打开视频源
                if (videoCapture.isOpened()) {
                    LOG_DEBUG("Releasing video capture before reconnect");
                    videoCapture.release();
                }

                // 检查是否需要退出
                if (!rtspThreadRunning) {
                    break;
                }

                // 等待重连间隔
                LOG_DEBUG("Waiting " + std::to_string(reconnectInterval) + " seconds before reconnect");
                for (int i = 0; i < reconnectInterval && rtspThreadRunning; i++) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    if (!rtspThreadRunning) {
                        LOG_DEBUG("RTSP thread exit requested during reconnect wait");
                        break;
                    }
                }

                // 再次检查是否需要退出
                if (!rtspThreadRunning) {
                    break;
                }

                // 使用参数方式打开，设置硬件加速和缓冲区大小
                int cvAccelType = convert_to_cv_accel_type(accelType);

                LOG_DEBUG("Opening RTSP stream with hardware acceleration type: " + std::to_string(cvAccelType));

                try {
                    // 添加更多的参数来提高稳定性
                    videoCapture.open(rtspUrl, cv::CAP_FFMPEG, {
                        cv::CAP_PROP_HW_ACCELERATION, cvAccelType,
                        cv::CAP_PROP_BUFFERSIZE, bufferSize,
                        cv::CAP_PROP_OPEN_TIMEOUT_MSEC, readTimeoutMs,
                        cv::CAP_PROP_READ_TIMEOUT_MSEC, readTimeoutMs,
                        cv::CAP_PROP_FOURCC, cv::VideoWriter::fourcc('H', '2', '6', '4'),
                        cv::CAP_PROP_FPS, 30 // 尝试设置帧率为30fps
                    });
                } catch (const cv::Exception& e) {
                    LOG_ERROR("OpenCV exception while opening RTSP stream: " + std::string(e.what()));
                } catch (const std::exception& e) {
                    LOG_ERROR("Exception while opening RTSP stream: " + std::string(e.what()));
                }

                if (videoCapture.isOpened()) {
                    LOG_INFO("Successfully reconnected to RTSP stream");
                    connectionLost = false;
                    reconnectCount = 0;
                    consecutiveReadFailures = 0;

                    // 获取视频属性
                    double width = videoCapture.get(cv::CAP_PROP_FRAME_WIDTH);
                    double height = videoCapture.get(cv::CAP_PROP_FRAME_HEIGHT);
                    double fps_val = videoCapture.get(cv::CAP_PROP_FPS);

                    LOG_INFO("RTSP stream properties - Width: " + std::to_string(width) +
                             ", Height: " + std::to_string(height) +
                             ", FPS: " + std::to_string(fps_val));

                    // 更新FPS
                    if (fps_val > 0) {
                        fps = fps_val;
                    } else {
                        fps = 30.0; // 默认值
                    }
                }
            } else {
                if (reconnectCount >= maxReconnectAttempts) {
                    LOG_ERROR("Max reconnection attempts reached, RTSP thread exiting");
                    rtspThreadRunning = false;
                    break;
                }

                // 等待一段时间再尝试
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }
        }

        // 检查是否需要退出
        if (!rtspThreadRunning) {
            break;
        }

        // 读取帧
        cv::Mat newFrame;
        bool readSuccess = false;

        try {
            // 使用grab和retrieve分离，可以减少延迟
            if (!videoCapture.grab()) {
                LOG_DEBUG("Failed to grab frame from RTSP stream");
                consecutiveReadFailures++;

                if (consecutiveReadFailures > MAX_CONSECUTIVE_READ_FAILURES) {
                    LOG_WARNING("Too many consecutive grab failures: " + std::to_string(consecutiveReadFailures));
                }

                // 短暂休眠后继续尝试
                std::this_thread::sleep_for(std::chrono::milliseconds(30));
                continue;
            }

            readSuccess = videoCapture.retrieve(newFrame);

            if (!readSuccess || newFrame.empty()) {
                LOG_DEBUG("Failed to retrieve frame after successful grab");
                consecutiveReadFailures++;
                continue;
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while reading frame in RTSP thread: " + std::string(e.what()));
            readSuccess = false;
            consecutiveReadFailures++;
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while reading frame in RTSP thread: " + std::string(e.what()));
            readSuccess = false;
            consecutiveReadFailures++;
        }

        if (readSuccess && !newFrame.empty()) {
            // 读取成功，更新帧缓冲区
            {
                std::lock_guard<std::mutex> lock(frameMutex);
                latestFrame = newFrame.clone();
                newFrameAvailable = true;
            }

            // 重置计数器
            reconnectCount = 0;
            consecutiveReadFailures = 0;

            // 如果启用了预解码，且不是通过预解码线程处理，则将帧添加到队列
            if (prefetchEnabled && !prefetchThreadRunning) {
                std::lock_guard<std::mutex> queueLock(queueMutex);
                // 如果队列已满，移除最旧的帧
                while (queueSize >= bufferSize && !frameQueue.empty()) {
                    frameQueue.pop();
                    queueSize--;
                }
                // 添加新帧到队列
                frameQueue.push(newFrame.clone());
                queueSize++;
            }
        } else {
            // 读取失败
            LOG_DEBUG("Failed to read frame in RTSP thread");
            consecutiveReadFailures++;

            // 如果连续失败次数过多，尝试重新连接
            if (consecutiveReadFailures >= MAX_CONSECUTIVE_READ_FAILURES) {
                LOG_WARNING("Too many consecutive read failures (" +
                           std::to_string(consecutiveReadFailures) +
                           "), attempting to reconnect");

                if (videoCapture.isOpened()) {
                    videoCapture.release();
                }

                // 重置连续失败计数器，但增加重连计数器
                consecutiveReadFailures = 0;
                reconnectCount++;

                // 标记连接丢失
                connectionLost = true;

                // 等待一段时间再尝试重连
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }
        }

        // 控制读取频率，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    // 确保在退出前释放资源
    if (videoCapture.isOpened()) {
        videoCapture.release();
    }

    LOG_INFO("RTSP thread function exited");
}

void VideoFrameProvider::prefetch_thread_func() {
    LOG_INFO("Prefetch thread function started");

    while (prefetchThreadRunning) {
        // 检查是否需要退出
        if (!prefetchThreadRunning) {
            break;
        }

        // 检查视频源是否打开
        if (!videoCapture.isOpened()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // 检查队列是否已满
        {
            std::unique_lock<std::mutex> lock(queueMutex);
            if (queueSize >= bufferSize) {
                // 队列已满，等待消费者通知
                queueCV.wait_for(lock, std::chrono::milliseconds(100), [this]() {
                    return queueSize < bufferSize || !prefetchThreadRunning;
                });

                // 再次检查是否需要退出
                if (!prefetchThreadRunning) {
                    break;
                }

                // 如果队列仍然满，继续等待
                if (queueSize >= bufferSize) {
                    continue;
                }
            }
        }

        // 读取新帧
        cv::Mat newFrame;
        bool success = false;

        try {
            // 对于RTSP流，使用最新帧
            if (fromCamera && !rtspUrl.empty()) {
                std::lock_guard<std::mutex> lock(frameMutex);
                if (newFrameAvailable && !latestFrame.empty()) {
                    newFrame = latestFrame.clone();
                    success = true;
                }
            } else {
                // 对于视频文件，直接读取下一帧
                success = videoCapture.read(newFrame);
            }
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while reading frame in prefetch thread: " + std::string(e.what()));
            success = false;
        }

        // 如果读取成功，将帧添加到队列
        if (success && !newFrame.empty()) {
            std::lock_guard<std::mutex> lock(queueMutex);
            frameQueue.push(newFrame);
            queueSize++;
        } else {
            // 如果是视频文件且读取失败，可能已到达文件末尾，暂停一段时间
            if (!fromCamera) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        // 控制读取频率，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }

    LOG_INFO("Prefetch thread function exited");
}

void VideoFrameProvider::start_prefetch_thread() {
    // 先停止已存在的线程
    stop_prefetch_thread();

    // 初始化线程状态
    prefetchThreadRunning = true;

    // 启动新线程
    prefetchThread = std::thread(&VideoFrameProvider::prefetch_thread_func, this);

    LOG_INFO("Prefetch thread started");
}

void VideoFrameProvider::stop_prefetch_thread() {
    // 停止线程
    if (prefetchThreadRunning) {
        // 先设置停止标志
        prefetchThreadRunning = false;

        // 通知所有等待的线程
        queueCV.notify_all();

        // 等待线程结束
        if (prefetchThread.joinable()) {
            // 使用超时等待，避免永久阻塞
            std::chrono::seconds timeout(3);
            std::thread temp(std::move(prefetchThread));
            if (temp.joinable()) {
                auto future = std::async(std::launch::async, [&temp]() {
                    temp.join();
                });
                if (future.wait_for(timeout) == std::future_status::timeout) {
                    // 如果超时，则强制结束线程（在生产环境中应该避免这样做）
                    LOG_WARNING("Prefetch thread did not exit cleanly");
                }
            }
        }

        LOG_INFO("Prefetch thread stopped");
    }
}

bool VideoFrameProvider::is_opened() const {
    return videoCapture.isOpened();
}

bool VideoFrameProvider::is_from_camera() const {
    return fromCamera;
}

std::string VideoFrameProvider::save_frame(const cv::Mat& frame, const std::string& outputPath) {
    if (frame.empty() || outputPath.empty()) {
        return "";
    }

    // 创建输出目录
    std::filesystem::path outDir(outputPath);
    if (!std::filesystem::exists(outDir)) {
        std::filesystem::create_directories(outDir);
    }

    // 设置文件名
    std::filesystem::path framePath;
    if (fromCamera) {
        // 对于摄像头，使用时间戳命名
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

        std::stringstream timestamp;
        std::tm tm_buf;

#ifdef _WIN32
        localtime_s(&tm_buf, &now_time_t);
#else
        localtime_r(&now_time_t, &tm_buf);
#endif

        timestamp << std::put_time(&tm_buf, "%Y%m%d_%H%M%S_") << std::setfill('0') << std::setw(3) << now_ms.count();
        framePath = outDir / ("camera_" + timestamp.str() + ".png");
    } else {
        // 对于视频文件，使用帧序号命名
        std::stringstream ss;
        ss << "frame_" << std::setfill('0') << std::setw(6) << currentFrame << ".png";
        framePath = outDir / ss.str();
    }

    std::string framePathStr = framePath.string();

#ifdef _WIN32
    std::string savePath = utf8ToAnsi(framePathStr);
    cv::imwrite(savePath, frame);
#else
    cv::imwrite(framePathStr, frame);
#endif

    return framePathStr;
}

void VideoFrameProvider::set_hardware_acceleration(HardwareAccelType type) {
    // 保存旧的加速类型，以便在失败时回退
    HardwareAccelType oldType = accelType;
    accelType = type;

    // 如果视频已经打开，应用新的硬件加速设置
    if (videoCapture.isOpened()) {
        try {
            apply_hardware_acceleration();

            // 验证硬件加速是否成功应用
            int actualAccelType = videoCapture.get(cv::CAP_PROP_HW_ACCELERATION);
            HardwareAccelType actualType = convert_from_cv_accel_type(actualAccelType);

            // 如果请求的是硬件加速但实际上没有启用，记录警告
            if (type != HardwareAccelType::NONE && actualType == HardwareAccelType::NONE) {
                LOG_WARNING("Requested hardware acceleration type " + std::to_string(static_cast<int>(type)) +
                           " could not be applied, using software decoding instead");
            }

            // 测试读取一帧，确保解码器正常工作
            cv::Mat testFrame;
            if (!videoCapture.read(testFrame) || testFrame.empty()) {
                LOG_WARNING("Failed to read frame after changing hardware acceleration, reverting to previous setting");

                // 恢复到之前的设置
                accelType = oldType;
                apply_hardware_acceleration();
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while applying hardware acceleration: " + std::string(e.what()) + ", reverting to previous setting");

            // 恢复到之前的设置
            accelType = oldType;
            apply_hardware_acceleration();
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while applying hardware acceleration: " + std::string(e.what()) + ", reverting to previous setting");

            // 恢复到之前的设置
            accelType = oldType;
            apply_hardware_acceleration();
        }
    }
}

HardwareAccelType VideoFrameProvider::get_hardware_acceleration() const {
    return accelType;
}

void VideoFrameProvider::set_buffer_size(int size) {
    if (size > 0) {
        bufferSize = size;

        // 如果视频已经打开，设置新的缓冲区大小
        if (videoCapture.isOpened()) {
            videoCapture.set(cv::CAP_PROP_BUFFERSIZE, bufferSize);
        }
    }
}

int VideoFrameProvider::get_buffer_size() const {
    return bufferSize;
}

void VideoFrameProvider::enable_prefetch(bool enable) {
    prefetchEnabled = enable;

    // 如果视频已经打开，根据设置启动或停止预解码线程
    if (videoCapture.isOpened()) {
        if (prefetchEnabled) {
            start_prefetch_thread();
        } else {
            stop_prefetch_thread();
        }
    }
}

bool VideoFrameProvider::is_prefetch_enabled() const {
    return prefetchEnabled;
}

void VideoFrameProvider::set_decode_thread_count(int count) {
    decodeThreadCount = count;

    // 如果视频已经打开，设置解码线程数
    if (videoCapture.isOpened()) {
        // 注意：OpenCV 4.x 中没有直接设置解码线程数的属性
        // 这里可以根据实际情况添加相关代码
        // 例如，可以使用环境变量或其他方式设置

        // 如果使用FFmpeg后端，可以尝试设置线程数
        if (decodeThreadCount > 0) {
            // 这是一个示例，实际上OpenCV可能不支持直接设置这个属性
            // videoCapture.set(cv::CAP_PROP_THREAD_COUNT, decodeThreadCount);

            // 可以考虑使用环境变量设置FFmpeg的线程数
            std::string threadCount = std::to_string(decodeThreadCount);
            #ifdef _WIN32
            _putenv_s("OPENCV_FFMPEG_THREADS", threadCount.c_str());
            #else
            setenv("OPENCV_FFMPEG_THREADS", threadCount.c_str(), 1);
            #endif
        }
    }
}

int VideoFrameProvider::get_decode_thread_count() const {
    return decodeThreadCount;
}

void VideoFrameProvider::apply_hardware_acceleration() {
    if (videoCapture.isOpened()) {
        try {
            int cvAccelType = convert_to_cv_accel_type(accelType);

            // 尝试设置硬件加速
            bool success = videoCapture.set(cv::CAP_PROP_HW_ACCELERATION, cvAccelType);

            // 检查是否成功设置了硬件加速
            int actualAccelType = videoCapture.get(cv::CAP_PROP_HW_ACCELERATION);
            HardwareAccelType actualType = convert_from_cv_accel_type(actualAccelType);

            LOG_INFO("Hardware acceleration "
                    + std::string(actualType != HardwareAccelType::NONE ? "enabled" : "disabled")
                    + " (requested: " + std::to_string(static_cast<int>(accelType))
                    + ", actual: " + std::to_string(static_cast<int>(actualType)) + ")");

            // 如果请求的是硬件加速但实际上没有启用，记录警告
            if (accelType != HardwareAccelType::NONE && actualType == HardwareAccelType::NONE) {
                LOG_WARNING("Requested hardware acceleration could not be applied. This may be due to:");
                LOG_WARNING("1. The hardware does not support the requested acceleration type");
                LOG_WARNING("2. The video codec is not compatible with hardware acceleration");
                LOG_WARNING("3. OpenCV was not built with the required hardware acceleration support");
                LOG_WARNING("Using software decoding instead");
            }
        } catch (const cv::Exception& e) {
            LOG_ERROR("OpenCV exception while applying hardware acceleration: " + std::string(e.what()));
        } catch (const std::exception& e) {
            LOG_ERROR("Exception while applying hardware acceleration: " + std::string(e.what()));
        }
    }
}

HardwareAccelType VideoFrameProvider::convert_from_cv_accel_type(int cvType) {
    switch (cvType) {
        case cv::VIDEO_ACCELERATION_NONE:
            return HardwareAccelType::NONE;
        case cv::VIDEO_ACCELERATION_ANY:
            return HardwareAccelType::AUTO;
        case cv::VIDEO_ACCELERATION_D3D11:
            return HardwareAccelType::D3D11;
        case cv::VIDEO_ACCELERATION_VAAPI:
            return HardwareAccelType::VAAPI;
        case cv::VIDEO_ACCELERATION_MFX:
            return HardwareAccelType::MFX;
        default:
            return HardwareAccelType::NONE;
    }
}

int VideoFrameProvider::convert_to_cv_accel_type(HardwareAccelType type) {
    switch (type) {
        case HardwareAccelType::NONE:
            return cv::VIDEO_ACCELERATION_NONE;
        case HardwareAccelType::AUTO:
            return cv::VIDEO_ACCELERATION_ANY;
        case HardwareAccelType::D3D11:
            return cv::VIDEO_ACCELERATION_D3D11;
        case HardwareAccelType::VAAPI:
            return cv::VIDEO_ACCELERATION_VAAPI;
        case HardwareAccelType::MFX:
            return cv::VIDEO_ACCELERATION_MFX;
        case HardwareAccelType::CUDA:
            // OpenCV 4.x 中没有直接对应的CUDA加速类型，使用ANY
            return cv::VIDEO_ACCELERATION_ANY;
        case HardwareAccelType::QSV:
            // OpenCV 4.x 中没有直接对应的QSV加速类型，使用MFX
            return cv::VIDEO_ACCELERATION_MFX;
        default:
            return cv::VIDEO_ACCELERATION_NONE;
    }
}

} // namespace utils






