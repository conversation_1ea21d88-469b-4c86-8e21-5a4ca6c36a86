#include "ai/plugins/python_script_manager.h"

#include <Python.h>

#include <algorithm>
#include <fstream>
#include <iostream>

#include "ai/plugins/plugin_manager.h"

namespace ai {
namespace plugins {

PythonScriptManager::PythonScriptManager(const std::string& script_directory)
    : script_directory_(script_directory) {
    // 确保脚本目录存在
    if (!std::filesystem::exists(script_directory_)) {
        std::filesystem::create_directories(script_directory_);
    }
}

PythonScriptManager::~PythonScriptManager() {
    // 清理脚本插件
    scripts_.clear();
}

void PythonScriptManager::set_script_directory(const std::string& dir) {
    script_directory_ = dir;
}

std::string PythonScriptManager::get_script_directory() const {
    return script_directory_;
}

int PythonScriptManager::load_scripts_from_directory() {
    int loaded_count = 0;

    // 确保脚本目录存在
    if (!std::filesystem::exists(script_directory_)) {
        std::filesystem::create_directories(script_directory_);
    }

    // 遍历脚本目录
    for (const auto& entry : std::filesystem::directory_iterator(script_directory_)) {
        if (entry.is_regular_file() && entry.path().extension() == ".py") {
            std::string script_path = entry.path().string();
            if (load_script(script_path)) {
                loaded_count++;
            }
        }
    }

    return loaded_count;
}

bool PythonScriptManager::load_script(const std::string& script_path) {
    // 检查脚本是否已加载
    std::string script_name = std::filesystem::path(script_path).stem().string();
    for (const auto& script : scripts_) {
        if (script->get_name() == "PythonScript_" + script_name) {
            // 脚本已加载，尝试重新加载
            if (script->reload_script()) {
                if (script_load_callback_) {
                    script_load_callback_(script_path, true);
                }
                return true;
            } else {
                if (script_load_callback_) {
                    script_load_callback_(script_path, false);
                }
                return false;
            }
        }
    }

    // 创建新的脚本插件
    auto script = std::make_shared<PythonScriptPlugin>(script_path);
    if (script->initialize()) {
        scripts_.push_back(script);

        // 注意：脚本加载成功，但不会自动注册到插件管理器
        // 需要调用 register_script_to_plugin_manager 或 register_all_scripts_to_plugin_manager 来注册
        // 例如: register_script_to_plugin_manager(script, plugin_manager);

        if (script_load_callback_) {
            script_load_callback_(script_path, true);
        }
        return true;
    } else {
        if (script_load_callback_) {
            script_load_callback_(script_path, false);
        }
        return false;
    }
}

bool PythonScriptManager::unload_script(const std::string& script_name) {
    auto it = std::find_if(scripts_.begin(), scripts_.end(),
                         [&script_name](const auto& script) {
                             return script->get_name() == "PythonScript_" + script_name;
                         });

    if (it != scripts_.end()) {
        scripts_.erase(it);
        return true;
    }

    return false;
}

std::vector<std::shared_ptr<PythonScriptPlugin>> PythonScriptManager::get_all_scripts() const {
    return scripts_;
}

std::shared_ptr<PythonScriptPlugin> PythonScriptManager::get_script(const std::string& script_name) const {
    auto it = std::find_if(scripts_.begin(), scripts_.end(),
                         [&script_name](const auto& script) {
                             return script->get_name() == "PythonScript_" + script_name;
                         });

    if (it != scripts_.end()) {
        return *it;
    }

    return nullptr;
}

bool PythonScriptManager::create_new_script(const std::string& script_name) {
    // 构建脚本路径
    std::string script_path = script_directory_ + "/" + script_name + ".py";

    // 检查脚本是否已存在
    if (std::filesystem::exists(script_path)) {
        return false;
    }

    // 创建脚本文件
    std::ofstream file(script_path);
    if (!file.is_open()) {
        return false;
    }

    // 写入默认脚本模板
    file << get_default_script_template();
    file.close();

    // 加载新创建的脚本
    return load_script(script_path);
}

void PythonScriptManager::set_script_load_callback(std::function<void(const std::string&, bool)> callback) {
    script_load_callback_ = callback;
}

bool PythonScriptManager::register_script_to_plugin_manager(std::shared_ptr<PythonScriptPlugin> script,
                                                     std::shared_ptr<PluginManager> plugin_manager) {
    if (!script || !plugin_manager) {
        return false;
    }

    // 将脚本注册到插件管理器
    return plugin_manager->register_plugin(script);
}

int PythonScriptManager::register_all_scripts_to_plugin_manager(std::shared_ptr<PluginManager> plugin_manager) {
    if (!plugin_manager) {
        return 0;
    }

    int registered_count = 0;

    for (const auto& script : scripts_) {
        // 检查脚本是否已经注册
        auto existing_plugin = plugin_manager->get_plugin(script->get_name());
        if (!existing_plugin) {
            // 如果脚本还没有注册，则注册它
            if (register_script_to_plugin_manager(script, plugin_manager)) {
                registered_count++;
                std::cout << "Python script registered to plugin manager: " << script->get_name() << std::endl;
            }
        }
    }

    return registered_count;
}

std::string PythonScriptManager::get_default_script_template() const {
    return R"(# -*- coding: utf-8 -*-
"""
AiVideo Python 脚本插件示例

此脚本演示了如何创建一个 Python 脚本插件来处理视频分析任务。
"""

import numpy as np
import cv2

# 全局变量
params = {}
frame_count = 0
total_count = 0
class_counts = {}

def initialize(parameters):
    """
    初始化函数，在插件加载时调用

    Args:
        parameters: 插件参数字典
    """
    global params, frame_count, total_count, class_counts
    # 清空旧参数
    params.clear()
    # 更新新参数
    params.update(parameters)

    # 重置计数器
    frame_count = 0
    total_count = 0
    class_counts = {}

    print("Python script initialized with parameters:", params)

def process(frame, tracks):
    """
    处理函数，每帧调用一次

    Args:
        frame: numpy 数组，表示当前帧图像
        tracks: 跟踪结果列表，每个元素是一个字典，包含跟踪信息

    Returns:
        包含处理结果的字典
    """
    global frame_count, total_count, class_counts
    frame_count += 1

    # 创建结果字典
    result = {
        "task_type": "python_script",
        "frame_id": frame_count,
        "total_count": total_count,
        "class_counts": class_counts,
        "ext_info": {
            "frame_count": frame_count,
            "track_count": len(tracks)
        }
    }

    # 处理跟踪结果
    for track in tracks:
        # 获取跟踪信息
        track_id = track["track_id"]
        detect_class = track["detect_class"]
        score = track["score"]
        state = track["state"]
        tlwh = track["tlwh"]

        # 更新类别计数
        if state == 3:  # 目标离开场景
            if detect_class not in class_counts:
                class_counts[detect_class] = 0
            class_counts[detect_class] += 1
            total_count += 1

        # 在图像上绘制跟踪框
        if state == 1 or state == 2:  # 确认的跟踪
            x, y = int(tlwh["x"]), int(tlwh["y"])
            w, h = int(tlwh["width"]), int(tlwh["height"])

            # 绘制矩形框
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # 绘制标签
            label = f"{detect_class} {track_id}"
            cv2.putText(frame, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # 绘制统计信息
    cv2.putText(frame, f"Frame: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
    cv2.putText(frame, f"Total: {total_count}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

    y = 90
    for cls, count in class_counts.items():
        cv2.putText(frame, f"{cls}: {count}", (10, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        y += 30

    # 返回处理后的帧
    result["processed_frame"] = frame

    return result

def reset():
    """
    重置函数，在插件重置时调用
    """
    global frame_count, total_count, class_counts
    frame_count = 0
    total_count = 0
    class_counts = {}
    print("Python script reset")

def get_info():
    """
    获取插件信息

    Returns:
        包含插件信息的字典
    """
    return {
        "type": "python_script",
        "name": "Python 脚本插件示例",
        "description": "Python 脚本插件示例",
        "version": "1.0.0",
        "author": "用户"
    }
)";
}

} // namespace plugins
} // namespace ai

