﻿#pragma once

#include <opencv2/opencv.hpp>

#include <atomic>
#include <chrono>
#include <filesystem>
#include <future>
#include <memory>
#include <queue>
#include <string>
#include <thread>
#include "aivideocore_export.h"

namespace utils {

/**
 * @brief 硬件加速类型枚举
 */
enum class HardwareAccelType {
    NONE,       ///< 不使用硬件加速
    AUTO,       ///< 自动选择最佳硬件加速方式
    D3D11,      ///< DirectX 11 (Windows)
    VAAPI,      ///< Video Acceleration API (Linux)
    MFX,        ///< Intel Media SDK/oneVPL
    CUDA,       ///< NVIDIA CUDA
    QSV         ///< Intel Quick Sync Video
};

/**
 * @brief 视频帧提供者类，负责从视频文件或摄像头获取帧
 */
class AIVIDEOCORE_API VideoFrameProvider {
public:
    /**
     * @brief 构造函数
     */
    VideoFrameProvider();

    /**
     * @brief 析构函数
     */
    ~VideoFrameProvider();

    /**
     * @brief 打开视频文件
     * @param filePath 视频文件路径
     * @return 是否成功打开
     */
    bool open_video_file(const std::string& filePath);

    /**
     * @brief 打开摄像头
     * @param cameraId 摄像头ID
     * @return 是否成功打开
     */
    bool open_camera(int cameraId);

    /**
     * @brief 打开RTSP视频流
     * @param url RTSP URL
     * @return 是否成功打开
     */
    bool open_rtsp_stream(const std::string& url);

    /**
     * @brief 关闭视频源
     */
    void close();

    /**
     * @brief 读取下一帧
     * @param frame 输出帧
     * @return 是否成功读取
     */
    bool read_frame(cv::Mat& frame);

    /**
     * @brief 设置当前帧位置（仅对视频文件有效）
     * @param frameIndex 帧索引
     */
    void set_frame_position(int frameIndex);

    /**
     * @brief 获取总帧数（仅对视频文件有效）
     * @return 总帧数
     */
    int get_total_frames() const;

    /**
     * @brief 获取当前帧索引
     * @return 当前帧索引
     */
    int get_current_frame() const;

    /**
     * @brief 获取RTSP URL
     * @return RTSP URL
     */
    std::string get_rtsp_url() const;

    /**
     * @brief 获取帧率
     * @return 帧率
     */
    double get_fps() const;

    /**
     * @brief 是否已打开
     * @return 是否已打开
     */
    bool is_opened() const;

    /**
     * @brief 是否是摄像头源
     * @return 是否是摄像头源
     */
    bool is_from_camera() const;

    /**
     * @brief 保存当前帧
     * @param frame 要保存的帧
     * @param outputPath 输出路径
     * @return 保存的文件路径
     */
    std::string save_frame(const cv::Mat& frame, const std::string& outputPath);

    /**
     * @brief 获取视频文件路径
     * @return 视频文件路径，如果是摄像头或RTSP则返回空字符串
     */
    std::string get_video_path() const;

    /**
     * @brief 设置硬件加速类型
     * @param accelType 硬件加速类型
     */
    void set_hardware_acceleration(HardwareAccelType accelType);

    /**
     * @brief 获取当前硬件加速类型
     * @return 当前硬件加速类型
     */
    HardwareAccelType get_hardware_acceleration() const;

    /**
     * @brief 设置帧缓冲区大小
     * @param size 缓冲区大小（帧数）
     */
    void set_buffer_size(int size);

    /**
     * @brief 获取帧缓冲区大小
     * @return 缓冲区大小（帧数）
     */
    int get_buffer_size() const;

    /**
     * @brief 启用或禁用预解码
     * @param enable 是否启用预解码
     */
    void enable_prefetch(bool enable);

    /**
     * @brief 检查预解码是否启用
     * @return 预解码是否启用
     */
    bool is_prefetch_enabled() const;

    /**
     * @brief 设置解码线程数
     * @param count 线程数（0表示自动选择）
     */
    void set_decode_thread_count(int count);

    /**
     * @brief 获取解码线程数
     * @return 解码线程数
     */
    int get_decode_thread_count() const;

private:
    /**
     * @brief 启动RTSP流读取线程
     */
    void start_rtsp_thread();

    /**
     * @brief 停止RTSP流读取线程
     */
    void stop_rtsp_thread();

    /**
     * @brief RTSP流读取线程函数
     */
    void rtsp_thread_func();

    /**
     * @brief 预解码线程函数
     */
    void prefetch_thread_func();

    /**
     * @brief 启动预解码线程
     */
    void start_prefetch_thread();

    /**
     * @brief 停止预解码线程
     */
    void stop_prefetch_thread();

    /**
     * @brief 应用硬件加速设置
     */
    void apply_hardware_acceleration();

    /**
     * @brief 将OpenCV硬件加速类型转换为内部枚举类型
     * @param cvType OpenCV硬件加速类型
     * @return 内部枚举类型
     */
    HardwareAccelType convert_from_cv_accel_type(int cvType);

    /**
     * @brief 将内部枚举类型转换为OpenCV硬件加速类型
     * @param type 内部枚举类型
     * @return OpenCV硬件加速类型
     */
    int convert_to_cv_accel_type(HardwareAccelType type);

private:
    cv::VideoCapture videoCapture;  ///< OpenCV视频捕获对象
    int currentFrame;               ///< 当前帧索引
    int totalFrames;                ///< 总帧数
    double fps;                     ///< 帧率
    bool fromCamera;                ///< 是否是摄像头源
    std::string rtspUrl;            ///< RTSP URL地址
    std::string videoPath;          ///< 视频文件路径
    int readTimeoutMs;              ///< 读取超时时间（毫秒）
    int reconnectInterval;          ///< 重连间隔（秒）
    int maxReconnectAttempts;       ///< 最大重连次数
    HardwareAccelType accelType;    ///< 硬件加速类型
    int bufferSize;                 ///< 帧缓冲区大小
    bool prefetchEnabled;           ///< 是否启用预解码
    int decodeThreadCount;          ///< 解码线程数

    // RTSP流读取线程相关
    std::thread rtspThread;         ///< RTSP流读取线程
    std::atomic<bool> rtspThreadRunning; ///< RTSP流读取线程是否运行
    std::mutex frameMutex;          ///< 帧缓冲区互斥锁
    std::condition_variable frameCV; ///< 帧缓冲区条件变量
    cv::Mat latestFrame;            ///< 最新读取的帧
    std::atomic<bool> newFrameAvailable; ///< 是否有新帧可用
    std::atomic<bool> connectionLost;    ///< 连接是否丢失

    // 预解码相关
    std::thread prefetchThread;     ///< 预解码线程
    std::atomic<bool> prefetchThreadRunning; ///< 预解码线程是否运行
    std::queue<cv::Mat> frameQueue; ///< 帧队列
    std::mutex queueMutex;          ///< 队列互斥锁
    std::condition_variable queueCV; ///< 队列条件变量
    std::atomic<int> queueSize;     ///< 当前队列大小
};

} // namespace utils
