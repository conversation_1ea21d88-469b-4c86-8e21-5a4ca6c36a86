#pragma once

#include <Python.h>
#include <filesystem>

#include <memory>
#include <string>
#include <vector>

#include "ai/plugins/python_script_plugin.h"
#include "ai/plugins/plugin_manager.h"
#include "aivideocore_export.h"

namespace ai {
namespace plugins {

/**
 * @brief Python 脚本管理器类，用于管理 Python 脚本插件
 */
class AIVIDEOCORE_API PythonScriptManager {
public:

    /**
     * @brief 设置脚本目录
     * @param dir 脚本目录
     */
    void set_script_directory(const std::string& dir);

    /**
     * @brief 获取脚本目录
     * @return 脚本目录
     */
    std::string get_script_directory() const;

    /**
     * @brief 加载脚本目录中的所有脚本
     * @return 加载的脚本数量
     */
    int load_scripts_from_directory();

    /**
     * @brief 加载指定脚本
     * @param script_path 脚本路径
     * @return 是否加载成功
     */
    bool load_script(const std::string& script_path);

    /**
     * @brief 卸载指定脚本
     * @param script_name 脚本名称
     * @return 是否卸载成功
     */
    bool unload_script(const std::string& script_name);

    /**
     * @brief 获取所有脚本插件
     * @return 脚本插件列表
     */
    std::vector<std::shared_ptr<PythonScriptPlugin>> get_all_scripts() const;

    /**
     * @brief 获取指定脚本插件
     * @param script_name 脚本名称
     * @return 脚本插件
     */
    std::shared_ptr<PythonScriptPlugin> get_script(const std::string& script_name) const;

    /**
     * @brief 创建新脚本
     * @param script_name 脚本名称
     * @return 是否创建成功
     */
    bool create_new_script(const std::string& script_name);

    /**
     * @brief 设置脚本加载回调
     * @param callback 回调函数
     */
    void set_script_load_callback(std::function<void(const std::string&, bool)> callback);

    /**
     * @brief 将脚本注册到插件管理器
     * @param script 脚本插件
     * @param plugin_manager 插件管理器
     * @return 是否注册成功
     */
    bool register_script_to_plugin_manager(std::shared_ptr<PythonScriptPlugin> script,
                                         std::shared_ptr<PluginManager> plugin_manager);

    /**
     * @brief 将所有脚本注册到插件管理器
     * @param plugin_manager 插件管理器
     * @return 注册成功的脚本数量
     */
    int register_all_scripts_to_plugin_manager(std::shared_ptr<PluginManager> plugin_manager);

public:
    /**
     * @brief 构造函数
     * @param script_directory 脚本目录，默认为"scripts/task_plugins"
     */
    PythonScriptManager(const std::string& script_directory = "scripts/task_plugins");

    /**
     * @brief 析构函数
     */
    ~PythonScriptManager();

private:
    /**
     * @brief 禁用拷贝构造函数
     */
    PythonScriptManager(const PythonScriptManager&) = delete;

    /**
     * @brief 禁用赋值运算符
     */
    PythonScriptManager& operator=(const PythonScriptManager&) = delete;

    /**
     * @brief 获取默认脚本模板
     * @return 默认脚本模板
     */
    std::string get_default_script_template() const;

    std::string script_directory_;  ///< 脚本目录
    std::vector<std::shared_ptr<PythonScriptPlugin>> scripts_;  ///< 脚本插件列表
    std::function<void(const std::string&, bool)> script_load_callback_;  ///< 脚本加载回调
};

} // namespace plugins
} // namespace ai
