# 视频插件处理示例

本目录包含两个示例，演示了如何使用 AiVideoCore 库加载视频，获取任务插件列表，启用插件处理视频，并使用 OpenCV 显示原始帧和处理后的帧。

## 编译

在项目根目录下执行以下命令：

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

## 示例说明

### 1. 基本视频插件示例 (plugin_video_example)

这个示例直接使用 VideoProcessingCore 加载视频并处理。

```bash
./bin/plugin_video_example <视频文件路径>
```

### 2. 基于项目管理的视频插件示例 (project_video_example)

这个示例使用 ProjectManager 创建项目，配置项目属性，然后使用 VideoProcessingCore 处理视频。

```bash
./bin/project_video_example <视频文件路径> <项目保存路径>
```

## 功能说明

两个示例都实现了以下功能：

1. 加载视频文件
2. 获取并显示所有可用的任务插件
3. 启用第一个任务插件
4. 处理视频帧并显示原始帧和处理后的帧
5. 按 ESC 键退出

基于项目管理的示例还实现了：

1. 创建和保存项目
2. 配置项目属性
3. 在项目和 VideoProcessingCore 之间导入导出配置

## 注意事项

- 需要替换示例中的许可证 ID 和服务器地址为有效值
- 如果没有可用的任务插件，程序将退出
- 处理过程中可能会抛出异常，程序会捕获并显示异常信息
