cmake_minimum_required(VERSION 3.10)
project(AiVideoCppPlugins)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/release)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/release/plugins/task)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找依赖包
find_package(Qt5 COMPONENTS Core Widgets REQUIRED)

# 包含目录
include_directories(
    ${OpenCV_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/AiVideoCore/include
    ${CMAKE_SOURCE_DIR}/3rdparty/include
)

# 链接目录
link_directories(
    ${CMAKE_SOURCE_DIR}/lib
    ${CMAKE_SOURCE_DIR}/3rdparty/lib
    ${CMAKE_BINARY_DIR}/AiVideoCore
)

# 定义宏
add_definitions(-DBEHAVIOR_RETRIEVAL_PLUGIN_EXPORTS)
add_definitions(-DCOUNTING_PLUGIN_EXPORTS)
add_definitions(-DPARAM_DEMO_PLUGIN_EXPORTS)

# 行为检索插件
add_library(behavior_retrieval_plugin SHARED
    behavior_retrieval_plugin.cpp
    behavior_retrieval_plugin.h
)

target_link_libraries(behavior_retrieval_plugin
    ${OpenCV_LIBS}
    Qt5::Core
    Qt5::Widgets
    AIDIJSONCPP
    AiVideoCore
    AIDIOPENCV
)

# 计数插件
add_library(counting_plugin SHARED
    counting_plugin.cpp
    counting_plugin.h
)

target_link_libraries(counting_plugin
    ${OpenCV_LIBS}
    Qt5::Core
    Qt5::Widgets
    AIDIJSONCPP
    AIDIOPENCV
    AiVideoCore
)

# 安装目标
install(TARGETS behavior_retrieval_plugin counting_plugin
    RUNTIME DESTINATION release/plugins/task
    LIBRARY DESTINATION release/plugins/task
)
