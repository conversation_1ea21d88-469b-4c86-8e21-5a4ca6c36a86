﻿#include "ai/ai_model_manager.h"

#include <visionflow/param/region_calculation_parameters.hpp>

namespace ai {

AiModelManager::AiModelManager() : model(nullptr), runtime(nullptr), modelLoaded(false), hasVLM(false) {
}

AiModelManager::~AiModelManager() {
    if (runtime) {
        delete runtime;
        runtime = nullptr;
    }

    if (model) {
        delete model;
        model = nullptr;
    }
}

bool AiModelManager::load_model(const std::string& modelPath, const std::string& promptValue, double scoreValue, int iouValue) {
    try {
        // 释放之前的资源
        if (model) {
            delete model;
            model = nullptr;
        }
        if (runtime) {
            delete runtime;
            runtime = nullptr;
        }

        // 加载新模型
        model = new visionflow::Model(modelPath);

        // 检查是否包含万物检测大模型
        auto tools_list = model->tool_list();
        hasVLM = false;
        for (const auto& tool_name : tools_list) {
            if (tool_name == "万物检测大模型") {
                hasVLM = true;
                break;
            }
        }

        // 如果包含万物检测大模型，设置参数
        if (hasVLM) {
            // 获取当前参数
            auto user_param = model->get_param({"万物检测大模型", "calculator.args"});
            auto user_vars = std::map<std::string, visionflow::param::TypeValuePair>();

            // 设置提示词
            auto prompt = visionflow::param::TypeValuePair();
            prompt.set_type("string");
            prompt.set_value(promptValue);
            user_vars.insert({"prompt", prompt});

            // 设置score阈值
            auto score = visionflow::param::TypeValuePair();
            score.set_type("number");
            score.set_value(std::to_string(static_cast<int>(scoreValue * 100)));
            user_vars.insert({"score", score});

            // 设置服务器地址
            auto url = visionflow::param::TypeValuePair();
            url.set_type("string");
            url.set_value("117.50.174.121:8080");
            user_vars.insert({"url", url});

            // 设置IOU阈值
            auto iou = visionflow::param::TypeValuePair();
            iou.set_type("number");
            iou.set_value(std::to_string(iouValue));
            user_vars.insert({"iou", iou});

            user_param->as<visionflow::param::RegionCalculationParameter>().set_user_vars(user_vars);
            model->set_param({"万物检测大模型", "calculator.args"}, *user_param);
        }

        modelLoaded = true;
        return true;
    } catch (const std::exception& e) {
        modelLoaded = false;
        throw std::runtime_error(std::string("加载模型失败: ") + e.what());
    }
}

bool AiModelManager::initialize_runtime(const std::string& inputNodeId, const std::string& outputNodeId) {
    if (!modelLoaded || !model) {
        return false;
    }

    try {
        // 释放之前的运行时（如果存在）
        if (runtime) {
            delete runtime;
            runtime = nullptr;
        }

        // 创建新的运行时
        visionflow::runtime::AllTools strategy;
        runtime = new visionflow::Runtime(model->create_runtime(strategy));
        return true;
    }
    catch (const std::runtime_error& e) {
        if (runtime) {
            delete runtime;
            runtime = nullptr;
        }
        throw std::runtime_error(std::string("运行时初始化失败: ") + e.what());
    }
    catch (const std::exception& e) {
        if (runtime) {
            delete runtime;
            runtime = nullptr;
        }
        throw std::runtime_error(std::string("运行时初始化时发生未知错误: ") + e.what());
    }
    catch (...) {
        if (runtime) {
            delete runtime;
            runtime = nullptr;
        }
        throw std::runtime_error("运行时初始化时发生未知错误");
    }
}

bool AiModelManager::update_model_params(const std::string& promptValue, double scoreValue, int iouValue) {
    if (!modelLoaded || !model || !hasVLM) {
        return false;
    }

    try {
        auto user_param = model->get_param({"万物检测大模型", "calculator.args"});
        auto user_vars = std::map<std::string, visionflow::param::TypeValuePair>();

        // 设置提示词
        auto prompt = visionflow::param::TypeValuePair();
        prompt.set_type("string");
        prompt.set_value(promptValue);
        user_vars.insert({"prompt", prompt});

        // 设置score阈值
        auto score = visionflow::param::TypeValuePair();
        score.set_type("number");
        score.set_value(std::to_string(static_cast<int>(scoreValue * 100)));
        user_vars.insert({"score", score});

        // 设置服务器地址
        auto url = visionflow::param::TypeValuePair();
        url.set_type("string");
        url.set_value("117.50.174.121:8080");
        user_vars.insert({"url", url});

        // 设置IOU阈值
        auto iou = visionflow::param::TypeValuePair();
        iou.set_type("number");
        iou.set_value(std::to_string(iouValue));
        user_vars.insert({"iou", iou});

        user_param->as<visionflow::param::RegionCalculationParameter>().set_user_vars(user_vars);
        model->set_param({"万物检测大模型", "calculator.args"}, *user_param);

        return true;
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("更新模型参数失败: ") + e.what());
    }
}

bool AiModelManager::is_model_loaded() const {
    return modelLoaded;
}

visionflow::Model* AiModelManager::get_model() const {
    return model;
}

visionflow::Runtime* AiModelManager::get_runtime() const {
    return runtime;
}

void AiModelManager::initialize_visionflow(const std::string& licenseId, const std::string& serverAddr) {
    visionflow::InitOptions opts;

    // 设置日志输出
    opts.logger.file_sink = "visionflow.log";
    opts.logger.stdout_sink = true;

    // 设置语言
    opts.language = "zh_CN";

    // 设置许可证
    opts.license.license_id = licenseId;
    opts.license.server_addr = serverAddr;

    // 初始化VisionFlow
    visionflow::initialize(opts);
}

} // namespace ai



